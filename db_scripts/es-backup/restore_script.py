import boto3
import requests
from requests_aws4auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

host ='https://vpc-replication-test-2-2yp32leuiusxlsz6gxaupnnlie.eu-west-1.es.amazonaws.com/' # include https:// and trailing /
region = 'eu-west-1' # e.g. us-west-1
service = 'es'
credentials = boto3.Session().get_credentials()
awsauth = AWS4Auth(credentials.access_key, credentials.secret_key, region, service, session_token=credentials.token)

# TODO: Registering Repository
path = '_snapshot/replication-test-1-snapshot-a' # the OpenSearch API endpoint
url = host + path
payload = {
  "type": "s3",
  "settings": {
    "bucket": "os-to-es-backup-testing",
    "region": "eu-west-1",
    "role_arn": "arn:aws:iam::848569320300:role/storage-ec2-role"
  }
}
headers = {"Content-Type": "application/json"}
r = requests.put(url, auth=awsauth, json=payload, headers=headers)
print(r.status_code)
print(r.text)
print("Registered Repository\n\n")


# TODO: Restore snapshot (all indexes except Dashboards and fine-grained access control)
headers = {"Content-Type": "application/json"}
payload = {
   "indices": "-.kibana*,-.opendistro_security",
   "include_global_state": False
 }
now = datetime.now()
print("Restore Starting = ", now)
path = '_snapshot/replication-test-1-snapshot-a/snapshot-a/_restore'
url = host + path
r = requests.post(url, auth=awsauth, json=payload, headers=headers)
print(r.status_code)
print(r.text)