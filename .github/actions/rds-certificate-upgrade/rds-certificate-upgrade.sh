#!/bin/bash -e

env=$1
rds_instance=$2
ca_certificate_name=$3

echo "Storage-Team"
cd rds_ca_certificates_upgrade

if [[ "$env" == "dev-rh" ]]; then
    profile="RH_QA"
elif [[ "$env" == "staging-mot" ]]; then
    profile="NOW_STAG"
elif [[ "$env" == "prod-rh" ]]; then
    profile="RH_PROD"
elif [[ "$env" == "prod-mot" ]]; then
    profile="NOW_PROD"
else
    echo "Invalid environment: $env"
    exit 1
fi


python3 certificate_upgrade.py --profile="${profile}" \
                                --rds_instance="${rds_instance}" \
                                --ca_certificate_name="${ca_certificate_name}"
