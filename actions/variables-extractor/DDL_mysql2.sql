#endpoint = utility-db.careem-engineering.com
#schema = CareemUtility
#env = prod-rh

CREATE TABLE CareemUtility.`shift_slot_campaign_performance` (
                                                                 `id` int NOT NULL AUTO_INCREMENT,
                                                                 `shift_slot_id` bigint unsigned NOT NULL,
                                                                 `campaign_driver_score_board_id` int NOT NULL,
                                                                 `adherence_in_minutes` int NOT NULL,
                                                                 `required_adherence_in_minutes` int NOT NULL,
                                                                 `adherence_percentage` float NOT NULL,
                                                                 `required_adherence_percentage` float NOT NULL,
                                                                 `is_achieved` tinyint(1) NOT NULL,
                                                                 PRIMARY KEY (`id`),
                                                                 UNIQUE KEY `unique_shift_slot_campaign` (`shift_slot_id`,`campaign_driver_score_board_id`),
                                                                 KEY `campaign_driver_score_board_id` (`campaign_driver_score_board_id`),
                                                                 CONSTRAINT `shift_slot_campaign_performance_ibfk_1` FOREIGN KEY (`shift_slot_id`) REFERENCES CareemUtility.`shift_slot` (`id`),
                                                                 CONSTRAINT `shift_slot_campaign_performance_ibfk_2` FOREIGN KEY (`campaign_driver_score_board_id`) REFERENCES CareemUtility.`campaign_driver_score_board` (`id`)
);