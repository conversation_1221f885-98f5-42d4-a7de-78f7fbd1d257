import yaml
from database_stats import dump_query_stats_to
from database_stats import dump_truncated_query_stats_to
from query_stats import get_queries_stats_in_parallel_against
from query_stats import get_query_stats_tuple_list_against
from truncated_query_stats import get_truncated_query_stats_tuple_list_against
from truncated_query_stats import get_all_truncated_queries_data_against


def main():
    configs = yaml.load(open('/Users/<USER>/Documents/projects/git-hub-db-scripts/storage-db-scripts/db_scripts/query-stats/config.yaml'), Loader=yaml.FullLoader)
    database_to_dump_stats = configs.get("database_stats_db_url")
    configuration_tables = configs.get('configuration_tables')

    query_stats = get_queries_stats_in_parallel_against(configuration_tables)
    query_stats_tuple_list = get_query_stats_tuple_list_against(query_stats)
    dump_query_stats_to(database_to_dump_stats, query_stats_tuple_list)


if __name__ == "__main__":
    main()

def lambda_handler(event, context):
    main()