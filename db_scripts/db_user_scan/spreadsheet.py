from datetime import datetime
import gspread
from google.oauth2 import service_account
import logging
import json

SHARE_EMAIL = "<EMAIL>"


class Spreadsheet:
    @staticmethod
    def csv_to_google_sheets(
        csv_file_path, spreadsheet_name, google_json, account, database
    ):
        logging.info(f"Starting to export the CSV output file to {spreadsheet_name}")
        google_json = json.loads(google_json)
        # Set up credentials to access Google Sheets
        credentials = service_account.Credentials.from_service_account_info(
            google_json, scopes=["https://www.googleapis.com/auth/drive"]
        )
        gc = gspread.authorize(credentials)

        try:
            # Try to open the existing Google Sheets spreadsheet
            spreadsheet = gc.open(spreadsheet_name)
            logging.info(f"Found existing Google Sheets spreadsheet: {spreadsheet.url}")

        except gspread.SpreadsheetNotFound:
            # If the spreadsheet does not exist, create a new one
            spreadsheet = gc.create(spreadsheet_name)
            logging.info(f"Created new Google Sheets spreadsheet: {spreadsheet.url}")
        spreadsheet.share(SHARE_EMAIL, perm_type="user", role="writer")
        # Generate a unique sheet name with the current date and time
        sheet_name = datetime.now().strftime("%Y-%m-%d %H-%M-%S")
        sheet_name = account + "-" + sheet_name + "-[" + database + "]"

        # Read the CSV file
        with open(csv_file_path, "r") as csv_file:
            csv_content = csv_file.read()

        # Split CSV content into rows and columns
        rows = [row.split(",") for row in csv_content.split("\n")]

        # Create a new worksheet and update it with rows and columns
        worksheet = spreadsheet.add_worksheet(
            title=sheet_name, rows=len(rows), cols=len(rows[0])
        )

        # Prepare a list of lists for the batch update
        cell_list = []

        # Populate the cell list with values
        for i, row in enumerate(rows, start=1):
            cell_list.append(row)

        # Perform the batch update to insert all rows at once
        worksheet.insert_rows(cell_list, value_input_option="RAW")

        logging.info(
            f"CSV data has been imported to the Google Sheets spreadsheet: {worksheet.url}"
        )
        return worksheet.url
