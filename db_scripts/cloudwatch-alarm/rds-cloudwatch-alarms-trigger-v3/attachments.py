import re

import event
from configs import Config
from dms_enum import DMSAlarmType

class Attachment:

    def __init__(self, msg, instance_tags):
        self.msg = msg
        self.tags = instance_tags

    def get_color(self):
        return "danger" if self.msg['NewStateValue'] == 'ALARM' else "good"

    def get_all_attachments(self):
        all_attachments = []
        all_attachments.append(self.get_alert_as_slack_attachment())
        all_attachments.append(self.get_instance_tags_as_slack_attachment())
        return all_attachments

    def get_instance_tags_as_slack_attachment(self):
        slack_attachment = {}
        slack_attachment["fallback"] = self.msg
        slack_attachment["message"] = self.msg
        slack_attachment["color"] = self.get_color()
        slack_attachment["fields"] = []
        for tag in self.tags:
            field = {
                "title": tag['Key'],
                "value": tag['Value'],
                "short": True
            }
            slack_attachment["fields"].append(field)
        return slack_attachment

    def get_alert_as_slack_attachment(self):
        json_msg = self.msg
        if Config.name_space == 'RDS':
            slack_attachment = {
                "fallback": json_msg,
                "message": json_msg,
                "color": self.get_color(),
                "fields": [
                    {
                        "title": "Metric",
                        "value": json_msg['Trigger']['MetricName'],
                        "short": False
                    },
                    {
                        "title": "Status",
                        "value": json_msg['NewStateValue'],
                        "short": True
                    },
                    {
                        "title": "State Change Time",
                        "value": json_msg['StateChangeTime'],
                        "short": True
                    },
                    {
                        "title": "RDS Instance",
                        "value": json_msg['Trigger']['Dimensions'][0]['value'],
                        "short": True
                    },
                    {
                        "title": "Environment",
                        "value": Config.slack.environment,
                        "short": True
                    },
                    {
                        "title": "Alarm Name",
                        "value": json_msg['AlarmName'],
                        "short": False
                    },
                    {
                        "title": "Reason",
                        "value": json_msg['NewStateReason'],
                        "short": False
                    },
                ]
            }
        elif Config.name_space == 'DMS':
                metrics = json_msg['Trigger'].get("Metrics", [])
                if len(metrics) > 0:
                    metric_name = json_msg['Trigger']['Metrics'][0]['Label']
                else:
                    metric_name = json_msg['Trigger']['MetricName']
                slack_attachment = {
                    "fallback": json_msg,
                    "message": json_msg,
                    "color": self.get_color(),
                    "fields": [
                        {
                            "title": "Metric",
                            "value": metric_name,
                            "short": False
                        },
                        {
                            "title": "Status",
                            "value": json_msg['NewStateValue'],
                            "short": True
                        },
                        {
                            "title": "State Change Time",
                            "value": json_msg['StateChangeTime'],
                            "short": True
                        },
                        {
                            "title": "DMS Replication Instance" if Config.dms_alarm_type == DMSAlarmType.REPLICATION_INSTANCE else "DMS Task",
                            "value": Config.dms_resource_id,
                            "short": True
                        },
                        {
                            "title": "Environment",
                            "value": Config.slack.environment,
                            "short": True
                        },
                        {
                            "title": "Alarm Name",
                            "value": json_msg['AlarmName'],
                            "short": False
                        },
                        {
                            "title": "Reason",
                            "value": json_msg['NewStateReason'],
                            "short": False
                        },
                    ]
                }
        elif Config.name_space == 'Redis':
            slack_attachment = {
                "fallback": json_msg,
                "message": json_msg,
                "color": self.get_color(),
                "fields": [
                    {
                        "title": "Metric",
                        "value": json_msg['Trigger']['MetricName'],
                        "short": False
                    },
                    {
                        "title": "Status",
                        "value": json_msg['NewStateValue'],
                        "short": True
                    },
                    {
                        "title": "State Change Time",
                        "value": json_msg['StateChangeTime'],
                        "short": True
                    },
                    {
                        "title": "Redis Instance",
                        "value": event.get_redis_instance_name(),
                        "short": True
                    },
                    {
                        "title": "Environment",
                        "value": Config.slack.environment,
                        "short": True
                    },
                    {
                        "title": "Alarm Name",
                        "value": json_msg['AlarmName'],
                        "short": False
                    },
                    {
                        "title": "Reason",
                        "value": json_msg['NewStateReason'],
                        "short": False
                    },
                ]
            }
        else:
            try:
                redis_instance = event.get_redis_instance_name()
            except Exception:
                redis_instance = str(Config.redis_instance_details)
            slack_attachment = {
                "fallback": json_msg,
                "message": json_msg,
                "color": self.get_color(),
                "fields": [
                    {
                        "title": "Message",
                        "value": str(json_msg),
                        "short": False
                    },
                    {
                        "title": "Reason",
                        "value": self.camel_case_to_words(str(json_msg).split(':')[1]),
                        "short": False
                    },
                    {
                        "title": "Environment",
                        "value": Config.slack.environment,
                        "short": True
                    },
                    {
                        "title": "Redis Instance",
                        "value": redis_instance,
                        "short": True
                    },
                ]
            }
        return slack_attachment

    def camel_case_to_words(self, sentence):
        # Use regular expression to split CamelCase into words
        words = re.sub(r'([a-z])([A-Z])', r'\1 \2', sentence)
        return words
