name: Perform RDS activity with replica

on:
  workflow_dispatch:
    inputs:
      aws_profile:
        type: choice
        description: "Which environment to use"
        required: true
        options:
          - dev-rh
        default: "dev-rh"
      route53_host:
        type: string
        description: "Route53 Endpoint"
        required: true
      database:
        type: string
        description: "Database name is required if engine is postgres"
        required: false  
      new_instance_id:
        type: string
        description: "New RDS Identifier"
        required: true

jobs:
  start-replica:
    name: Spin up new replica to perform long running activities
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)[inputs.aws_profile].runner }}
    env:
      AWS_REGION: eu-west-1
      AWS_DEFAULT_REGION: eu-west-1
      ROUTE53_HOST: ${{ inputs.route53_host }}
      NEW_INSTANCE_ID: ${{ inputs.new_instance_id }}
      DATABASE: ${{ inputs.database }}
      RESUME: "false"

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: ${{ fromJSON(vars.DEVX_PROFILES_ROLES)[inputs.aws_profile] }}
          role-duration-seconds: 3600
          aws-region: eu-west-1        
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.23.4'

      - name: Spin up and promote a new replica with external replication
        run: |
          cd db_scripts/rds_zero_downtime_activity
          go get .
          go run .

      - name: Pause Execution for User Activity
        if: success()
        run: |
          echo "🚀 Replication setup complete."
          echo "Waiting for approval to promote the replica."
          echo "Please go to the GitHub Actions UI and click 'Approve' under the 'promote-approval' environment to continue."

  promote:
    name: Switchover the traffic to replica
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)[inputs.aws_profile].runner }}
    needs: start-replica  # Ensures start-replica completes first
    environment: promote-approval  # Requires manual approval
    env:
      AWS_REGION: eu-west-1
      AWS_DEFAULT_REGION: eu-west-1
      ROUTE53_HOST: ${{ inputs.route53_host }}
      NEW_INSTANCE_ID: ${{ inputs.new_instance_id }}
      DATABASE: ${{ inputs.database }}
      RESUME: "true"  # Automatically set for the Go app

    steps:
      - name: Confirm Approval
        run: |
          echo "✅ Approval received. Proceeding with promotion and switchover."

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: ${{ fromJSON(vars.DEVX_PROFILES_ROLES)[inputs.aws_profile] }}
          role-duration-seconds: 3600
          aws-region: eu-west-1

      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.23.4'

      - name: Run Replica Switch Promote
        run: |
          cd db_scripts/rds_zero_downtime_activity
          go get .
          go run .