name: mysql-version-upgrade
run-name: Execute this workflow so we can perform version upgrade for an RDS instance

on:
  workflow_dispatch:
    inputs:
      account_identifier:
        type: choice
        description: Which is the source account on which we want to perform the upgrade
        required: true
        options:
          - dev-rh
          - staging-mot
          - prod-rh
          - prod-mot
      rds_identifier:
        type: string
        description: Name of the rds which we want to upgrade
        required: true
      version_upgrade_to:
        type: choice
        description: Engine version which we want to upgrade
        required: true
        options:
          - 8.0.33
          - 8.0.34
          - 8.0.35
          - 8.0.36
          - 8.0.37
          - 8.0.39

jobs:
  create:
    name: mysql-version-upgrade
    runs-on: ${{fromJSON(vars.DB_RUNNERS)[inputs.account_identifier]}}
    env:
      AWS_REGION: eu-west-1
      AWS_DEFAULT_REGION: eu-west-1
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{fromJSON(vars.DEVX_PROFILES_ROLES)[inputs.account_identifier]}}
          role-duration-seconds: 3600
          aws-region: eu-west-1
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Checkout
        uses: actions/checkout@v3


      - name: mysql-version-upgrade
        id: execute-upgrade
        uses: ./.github/actions/mysql-version-upgrade
        with:
          account_identifier: ${{ inputs.account_identifier }}
          rds_identifier: ${{inputs.rds_identifier}}
          version_upgrade_to:  ${{inputs.version_upgrade_to}}