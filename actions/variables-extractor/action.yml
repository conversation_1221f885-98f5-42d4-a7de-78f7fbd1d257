name: SQL Variables Extractor
description: Extract the variables from the provided SQL file

inputs:
  path_pattern:
    description: The path pattern to the SQL file
    required: true
  sql_file:
    description: The provided SQL file in the PR
    required: true

outputs:
  endpoint:
    description: Database route35 endpoint
    value: ${{ steps.info.outputs.endpoint }}
  database:
    description: Database instance RDS host
    value: ${{ steps.info.outputs.database }}
  schema:
    description: Database instance name
    value: ${{ steps.info.outputs.schema }}
  env:
    description: Environment name
    value: ${{ steps.info.outputs.env }}
  downtime_tolerance:
    description: Downtime tolerance
    value: ${{ steps.info.outputs.downtime_tolerance }}
  aws_region:
    description: Aws region
    value: ${{ steps.info.outputs.aws_region }}
  rds_instance_name:
    description: Rds Instance name
    value: ${{ steps.info.outputs.rds_instance_name }}
  sql_content:
    description: SQL content to analyze
    value: ${{ steps.info.outputs.sql_content }}

runs:
  using: composite
  steps:
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.x'

    - shell: bash
      id: info
      working-directory: ${{ github.action_path }}
      env:
        INPUT_FILE: ${{ inputs.sql_file }}
        WORKSPACE: ${{ github.workspace }}
        PATH_PATTERN: ${{ inputs.path_pattern }}
      run: |
        pip3 install -r requirements.txt
        python3 -m module.main
