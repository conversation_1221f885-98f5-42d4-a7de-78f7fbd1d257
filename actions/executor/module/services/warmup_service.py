import logging
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor
from threading import Semaphore

from ..dto import WarmupConfig
from ..shared.dto.database import DatabaseInfo
from ..shared.lib.database import DatabaseConnector
from ..shared.lib.serialization_tools import *
from ..shared.models import RDSInstance


class WarmupService:
    def __init__(self,
                 source_db_info: DatabaseInfo,
                 target_db_info: DatabaseInfo,
                 source_db: RDSInstance,
                 target_db: RDSInstance,
                 config: WarmupConfig
                 ):
        self.config = config
        self.source_db_info = source_db_info
        self.target_db_info = target_db_info
        self.source_db = source_db
        self.target_db = target_db
        self.source_connector = DatabaseConnector(db=self.source_db_info)
        self.target_connector = DatabaseConnector(db=self.target_db_info)
        self.semaphore = Semaphore(self.config.num_of_threads)

    @staticmethod
    def get_current_statements(schema):
        return f"""
            SELECT DISTINCT SQL_TEXT
            FROM performance_schema.events_statements_current
            WHERE SQL_TEXT IS NOT NULL
              AND SQL_TEXT LIKE '%SELECT%'
              AND SQL_TEXT NOT LIKE '%@@%'
              AND SQL_TEXT NOT LIKE '%SELECT DATABASE()%'
              AND SQL_TEXT NOT LIKE '%performance_schema%'
              AND SQL_TEXT NOT LIKE '%information_schema%'
              AND SQL_TEXT NOT LIKE '%SELECT 1'
              AND CURRENT_SCHEMA LIKE '{schema}'
              AND SQL_TEXT NOT LIKE '%UPDATE%'
              AND SQL_TEXT NOT LIKE '%INSERT%'
              AND SQL_TEXT NOT LIKE '%DELETE%'
              AND SQL_TEXT NOT LIKE '%CREATE%'
        """

    def warmup_statements(self, schema):
        logging.info(f"Warming-up the schema {schema}...")
        sql = self.get_current_statements(schema)
        logging.debug(f"Executing SQL: {sql}")

        def execute_with_semaphore(query):
            with self.semaphore:
                self.execute_query(query)

        for i in range(self.config.query_fetch_cycles):
            try:
                logging.debug(f"Fetching queries for {schema} - cycle {i + 1}")
                statements = self.source_connector.execute(sql)
                logging.debug(f"Statements fetched: {statements}")
                queries = mysql_extract_sql_from_current_statements(statements)
                if not queries:
                    logging.info(f"No queries found for schema {schema} in cycle {i + 1}")
                    continue  # Continue to the next cycle even if no queries are found
                with ThreadPoolExecutor(max_workers=self.config.num_of_threads) as executor:
                    executor.map(execute_with_semaphore, queries)
            except Exception as e:
                logging.info(f"Error executing this query: {sql} - {e}")

    def execute_query(self, query):
        logging.debug(f"Executing query: {query}")
        try:
            self.target_connector.execute(query)
        except Exception as e:
            logging.info(f"Error executing this query: {query} - {e}")

    def warmup_instance(self):
        logging.info(
            f"Initiating warming-up for {self.source_db.instance_identifier} from {self.target_db.instance_identifier}"
        )

        self.source_connector.connect()
        self.target_connector.connect()

        for schema in self.source_db_info.schemas:
            self.warmup_statements(schema)
