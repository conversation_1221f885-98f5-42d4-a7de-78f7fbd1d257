name: SQL Analyzer
description: Analyze the *.sql files on the DB

inputs:
  sql_content:
    description: SQL content to analyze
    required: true
  database:
    description: Database name
    required: false
  instance:
    description: Instance name
    required: false
  engine:
    description: Database engine
    required: false
  rds_host:
    description: Database RDS host
    required: true
  username:
    description: Database username
    required: true
  password:
    description: Database password
    required: true
  schemas:
    description: Database schemas array
    required: true
  schema:
    description: Selected database schema
    required: true
  env:
    description: Environment name
    required: false

outputs:
  analysis:
    description: Analysis object
    value: ${{ steps.execution.outputs.analysis }}

runs:
  using: composite
  steps:
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.x'

    - name: Setup Rust
      uses: actions-rust-lang/setup-rust-toolchain@v1

    - shell: bash
      id: execution
      working-directory: ${{ github.action_path }}
      env:
        ENV: ${{ inputs.env }}

        SQL_CONTENT: ${{ inputs.sql_content }}
        INSTANCE: ${{ inputs.instance }}
        DATABASE: ${{ inputs.database }}
        RDS_HOST: ${{ inputs.rds_host }}
        USERNAME: ${{ inputs.username }}
        PASSWORD: ${{ inputs.password }}
        SCHEMAS: ${{ inputs.schemas }}
        ENGINE: ${{ inputs.engine }}
        SCHEMA: ${{ inputs.schema }}
      run: |
        pip3 install -r requirements.txt
        python3 -m module.main

    - name: Comment on PR
      uses: mshick/add-pr-comment@v2
      with:
        message-path: ${{ github.action_path }}/analysis_output.md