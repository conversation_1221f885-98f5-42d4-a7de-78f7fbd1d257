from functools import cached_property

from sql_metadata import Parser

from ..models.table import Table


class QueryTable:
    def __init__(self, table: Table, parser: Parser):
        self.table: Table = table
        self.parser = parser

    @cached_property
    def columns(self):
        table_columns = {}
        if self.parser.columns:
            for column in self.parser.columns:
                table_name = '.'.join(column.split('.')[:-1])
                column_name = column.split('.')[-1]
                if not table_name:
                    continue
                if table_name not in table_columns:
                    table_columns[table_name] = set()
                table_columns[table_name].add(column_name)
        return table_columns.get(self.table.name, set())

    @property
    def name(self):
        return self.table.name
