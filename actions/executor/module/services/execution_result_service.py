import contextlib
import gc
import logging
import threading
from functools import cached_property

from rx.subject import BehaviorSubject
from typing_extensions import Optional

from ..dto.execution_result import ExecutionResult
from ..serializers import ResultCommentSerializer
from ..shared.dto.github import GitHubProps, CommentManagerProps, FinderInputs, ManagerInputs, Comment
from ..shared.services.github import GitHubCommentManager, GitHubCommentFinder


class ExecutionResultService:
    _disable_callback = False
    _comment_id_subject = BehaviorSubject(None)
    _lock = threading.Lock()

    def __init__(
            self,
            github_props: GitHubProps,
            result: ExecutionResult = None
    ):

        with self.disable_callback():
            self.github_props = github_props
            self.result_file = 'result.md'
            self.result_content = None
            self.result: ExecutionResult = result
            self.comment_id = None

        if self.result:
            self.serialize()

        self.set_callback()

    def deserialize(self) -> bool:
        comment = self.get_comment()
        if not comment: return False

        self.result_content = comment.body

        with self.disable_callback():
            del self.result
            gc.collect()

            self.result = ResultCommentSerializer.deserialize(self.result_content)

            self.set_callback()

        return True

    def serialize(self):
        self.result_content = (
            ResultCommentSerializer.serialize(self.result)
        )

    @cached_property
    def _comment_finder(self):
        return GitHubCommentFinder(inputs=self.finder_props)

    def get_comment(self) -> Optional[Comment]:
        comment = self._comment_finder.find_comment()
        if not comment: return None

        self.comment_id = comment.id
        self._comment_id_subject.on_next(self.comment_id)  # Update BehaviorSubject with the new comment ID
        return comment

    @cached_property
    def _comment_manager(self):
        self.get_comment()
        manager_props = CommentManagerProps(
            comment_id=self.comment_id,
            edit_mode='replace',
        )
        inputs = ManagerInputs(**{**self.github_props.model_dump(), **manager_props.model_dump()})
        return GitHubCommentManager(inputs=inputs)

    def save_comment(self):
        logging.info('Saving the comment...')
        self.comment_id = self._comment_manager.create_or_update_comment(self.result_content)
        self._comment_id_subject.on_next(self.comment_id)  # Update BehaviorSubject with the new comment ID
        self._comment_manager.inputs.comment_id = self.comment_id

    def save_to_file(self):
        with open(self.result_file, 'w') as file:
            file.write(self.result_content)


    def update(self, _name, _value):
        self.set_callback()
        self.serialize_and_save_comment(_name, _value)

    def serialize_and_save_comment(self, _name, _value):
        with self._lock:
            if not self._disable_callback:
                logging.info(f"Updating the result comment...")
                self.serialize()
                self.save_comment()

    @cached_property
    def finder_props(self):
        return FinderInputs(**{
            **self.github_props.model_dump(),
            "body_includes": "# 🚀 Execution Result",
            "direction": "last"
        })

    @contextlib.contextmanager
    def disable_callback(self):
        self._disable_callback = True
        try:
            yield
        finally:
            self._disable_callback = False

    def __setattr__(self, name, value):
        if not self._disable_callback and name in ['result']:
            with self.disable_callback():
                super().__setattr__(name, value)
                self.update(name, value)
        else:
            super().__setattr__(name, value)

    def set_callback(self):
        if self.result:
            self.result.set_callback(self.update)
            for query in self.result.queries:
                query.set_callback(self.update)

    def wait_for_completion(self):
        self.result.wait_for_completion()
        for query in self.result.queries:
            query.wait_for_completion()
