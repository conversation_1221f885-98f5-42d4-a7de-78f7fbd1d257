CONFIGURATION_FILE_PATH=resources/slack_configurations.yaml
CONFIGURATION_FILE_PATH_REDIS=resources/slack_configurations_redis.yaml
ROLE=db_rightsizing_role_cross_account
SECRET_ID=storage/prod/cloudwatchalerts
SLACK_TEAMS_FILE_PATH=resources
VICTOROPS_SECRET_ID=storage/prod/victorops-endpoints

AWS_PROFILE=prod-rh

# new keys
SERVICE_DIRECTORY_BUCKET=service-directory-snapshot
SERVICE_DIRECTORY_FILE=directory.json
SERVICE_DIRECTORY_ACCOUNT_ID=************

DEFAULT_VO_ROUTE=plt-infra-storage
DEFAULT_SLACK_GROUP=storage-on-call
