name: Health Report

on:
  workflow_call:
    inputs:
      profile:
        type: string
        description: 'AWS profile name'
      aws-region:
        type: string
        description: 'AWS region'
        default: eu-west-1
    secrets:
      google-json:
        description: 'Google JSON credentials'
        required: true


env:
  AWS_REGION: ${{ inputs.aws-region }}
  AWS_DEFAULT_REGION: ${{ inputs.aws-region }}

jobs:
  health-report:
    name: Generate Health Report
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)[inputs.profile].runner }}
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{fromJSON(vars.DEVX_PROFILES_ROLES)[inputs.profile]}}
          role-duration-seconds: 3600
          aws-region: ${{ inputs.aws-region }}

      - name: Generate Health Report
        id: execute
        uses: careem/storage-db-scripts/.github/actions/health-report@eng-40200-health-report
        with:
          google_json: ${{ secrets.google-json }}
          profile: ${{ inputs.profile }}
