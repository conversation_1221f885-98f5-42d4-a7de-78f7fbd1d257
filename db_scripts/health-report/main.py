import argparse
import logging
import sys

from pathlib2 import Path

from .configs import Config
from .lib import ProfileProcessor, TaskRegistry
from .spreadsheet import Spreadsheet
from .tasks import *

# Will be set based on command line arguments
logging_level = logging.INFO

def parse_arguments():
    parser = argparse.ArgumentParser(description="Generate health report for RDS instances")
    parser.add_argument(
        "-gj",
        "--googlejson",
        type=str,
        help="Google JSON secret for spreadsheet access",
        required=False
    )
    parser.add_argument(
        "-p",
        "--profile",
        type=str,
        help="AWS profile to use (e.g., prod-rh, prod-mot)",
        required=False
    )
    parser.add_argument(
        "--skip-prompt",
        action="store_true",
        help="Skip interactive prompts (useful for CI/CD environments)",
        default=False
    )
    parser.add_argument(
        "-c",
        "--config",
        type=str,
        help="Path to configuration file (default: configurations.yaml)",
        default="configurations.yaml"
    )
    parser.add_argument(
        "-d",
        "--debug-instance",
        type=str,
        help="Debug a specific RDS instance by its identifier (requires --profile to be specified)",
        required=False
    )
    parser.add_argument(
        "--debug-level",
        type=str,
        choices=["INFO", "DEBUG", "WARNING", "ERROR", "CRITICAL"],
        help="Set the logging level for debugging",
        default="INFO"
    )
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()

    # Set logging level based on command line argument
    logging_level = getattr(logging, args.debug_level)
    logging.basicConfig(
        stream=sys.stdout,
        level=logging_level,
        format="%(asctime)s %(levelname)s [%(filename)s:%(lineno)d] %(message)s"
    )

    if args.debug_instance:
        logging.info(f"Debug mode enabled for instance: {args.debug_instance}")
        if not args.profile:
            logging.error("Profile name is required when debug_instance is provided")
            sys.exit(1)

    # Load the specified configuration file
    Config().load_configs(config_file=args.config)

    GOOGLE_FILE = "google-config.json"
    CSV_FILE_NAME = "output.csv"
    CSV_SHEET_NAME = "Health_Report"

    SECRET_CACHE = {}
    all_results = {}

    # Task Registry Instance
    task_registry = TaskRegistry()

    # Register tasks inside main
    # task_registry.register(ListModifiedRDSParameters)
    # task_registry.register(CWMissingAlarmsList)
    # task_registry.register(CWInAlarmList)
    # task_registry.register(DatabaseTableSizes)
    # task_registry.register(DatabaseUsersList)
    # task_registry.register(ExpiringUsers)
    # task_registry.register(PKLimits)
    # task_registry.register(BigIntOverflowCheck)
    # task_registry.register(BookingIdColumnAudit)
    # task_registry.register(RDSSettingsList)
    # task_registry.register(EnsurePortalUser)
    task_registry.register(PrivilegedUsersAudit)


    # Filter profiles based on command line argument if provided
    if args.profile:
        filtered_profiles = [p for p in Config.aws_profiles if p.get("profile_name") == args.profile]
        if not filtered_profiles:
            logging.error(f"Profile '{args.profile}' not found in configuration file {args.config}")
            sys.exit(1)
        profiles_to_process = filtered_profiles
    else:
        profiles_to_process = Config.aws_profiles

    for profile_config in profiles_to_process:
        ProfileProcessor(profile_config).process_profile(task_registry, skip_prompt=args.skip_prompt, debug_instance=args.debug_instance)

    # Convert all results to CSV
    # CSVConverter.save_all_results_to_csv(all_results)

    # Get Google JSON secret
    if args.googlejson:
        # Use the provided Google JSON from command line
        google_secret = args.googlejson
    else:
        # Try to read from file
        try:
            google_file_path = Path(sys.argv[0]).resolve().parent / "google_json.json"
            google_secret = open(google_file_path, "r").read()
        except Exception as e:
            logging.error(f"Failed to read Google JSON file: {e}")
            sys.exit(1)

    # Create spreadsheet
    spreadsheet_url = Spreadsheet.all_results_to_google_sheets(task_registry, CSV_SHEET_NAME, google_secret)
    logging.info(f"Spreadsheet created: {spreadsheet_url}")

    # Sending Slack message is working
    # boto3.setup_default_session(profile_name="prod-rh")
    # Slack.send_message_slack(spreadsheet_url)
