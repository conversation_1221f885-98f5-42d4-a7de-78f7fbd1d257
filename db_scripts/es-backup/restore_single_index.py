import boto3
import requests
from requests_aws4auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

host = 'https://vpc-replication-test-2-2yp32leuiusxlsz6gxaupnnlie.eu-west-1.es.amazonaws.com/' # include https:// and trailing /
region = 'eu-west-1' # e.g. us-west-1
service = 'es'
credentials = boto3.Session().get_credentials()
awsauth = AWS4Auth(credentials.access_key, credentials.secret_key, region, service, session_token=credentials.token)

# Register repository

path = '_snapshot/replication-test-1-snapshot-a' # the OpenSearch API endpoint
url = host + path

payload = {
  "type": "s3",
  "settings": {
    "bucket": "os-to-es-backup-testing",
    "region": "eu-west-1",
    "role_arn": "arn:aws:iam::848569320300:role/storage-ec2-role"
  }
}

"""Restore snapshot (one index)"""
path = '_snapshot/replication-test-1-snapshot-a/snapshot-a/_restore'
url = host + path
payload = {"indices": "locations"}
headers = {"Content-Type": "application/json"}
now = datetime.now()
print("Restore Starting = ", now)
r = requests.post(url, auth=awsauth, json=payload, headers=headers)
print(r.text)

