import logging


class Alarm:
    def __init__(self, cloudwatch_client):
        self.cloudwatch_client = cloudwatch_client
        self.logger = logging.getLogger(self.__class__.__name__)

    def add_cloud_metric_alarm(self, alarm):
        self.logger.info("Adding cloud metric alarm: %s", alarm['alarm_name'])
        self.cloudwatch_client.put_metric_alarm(
            AlarmName=alarm['alarm_name'],
            ComparisonOperator=alarm['metric_operator'],
            EvaluationPeriods=alarm['datapoint'],
            MetricName=alarm['metric_name'],
            Namespace='AWS/ElastiCache',
            Period=alarm['period'],
            Statistic=alarm['statistic'],
            Threshold=alarm['threshold'],
            ActionsEnabled=True,
            AlarmActions=[alarm['alarm_action']],
            AlarmDescription=alarm['description'],
            Dimensions=[
                {'Name': 'CacheClusterId', 'Value': alarm['cluster_id']},
                {'Name': 'CacheNodeId', 'Value': alarm['node_id']}
            ]
        )

        self.logger.info("Successfully added/updated alarm configuration")
        self.logger.info(f"{alarm['metric_name']} alarm added to {alarm['cluster_id']}")
