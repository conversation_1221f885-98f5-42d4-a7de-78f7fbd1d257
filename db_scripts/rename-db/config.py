import json

class Config:
    def __init__(self, config_file='config.json'):
        with open(config_file, 'r') as file:
            self.config = json.load(file)

    def get_db_credentials(self):
        return self.config['db_credentials']

    def get_instances_to_rename(self):
        return self.config['instances_to_rename']

    def get_main_instance(self):
        return self.config['main_instance']