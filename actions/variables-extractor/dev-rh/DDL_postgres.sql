--endpoint = location-db.careem-internal.com
--database=location

-- Create an example table for PostgreSQL
CREATE TABLE IF NOT EXISTS example_table
(
    id         SERIAL PRIMARY KEY,
    name       VARCHAR(100)        NOT NULL,
    age        INT CHECK (age > 0),
    email      VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE global_city_location
    ADD COLUMN city_name varchar(255);


SELECT pp.id       AS pickup_point_id,
       pp.source_uuid,
       pp.lonlat,
       -- This is a bad comment
       pp.place_name,
       pp.city     AS pickup_city,
       pp.country  AS pickup_country,
       gba.id      AS building_area_id,
       gba.polygon,
       gba.city    AS building_city,
       gba.country AS building_country,
       sa.id       AS service_area_zone_id,
       sa.pick_up_allowed,
       sa.drop_off_allowed
FROM public.pickup_point pp
    -- here also a really bad comment
         JOIN
     public.building_pickup_point_mapping bppm
     ON pp.id = bppm.pickup_point_id
         JOIN
     public.building_and_area gba
     ON bppm.building_and_area_id = gba.id
         JOIN
     public.service_area_zone sa
     ON pp.service_area_id = sa.service_area_id
WHERE pp.active = true
  AND gba.active = true
ORDER BY pp.created_at DESC
LIMIT 10;

SELECT pp.id AS pickup_point_id,
       pp.place_name,
       saz.avg_pricing_zone
FROM public.pickup_point pp
         JOIN (SELECT service_area_id,
                      AVG(pricing_zone) AS avg_pricing_zone
               FROM public.service_area_zone
               GROUP BY service_area_id) saz ON pp.service_area_id = saz.service_area_id
WHERE pp.active = true
ORDER BY pp.place_name ASC;
