
# 📊 Database Analysis Report

- **🌍 Environment Name**: {{ env or '(not set)' }}
- **🖥️ RDS Instance Name**: {{ analysis.database_analysis.rds_instance_name }}
- **📂 Database Name**: {{ analysis.database_analysis.name }}
- **💾 Database Size**: {{ analysis.database_analysis.content.db_size_text }}
{% if db_info.engine in ['mysql', 'mariadb'] and not analysis.database_analysis.content.performance_schema -%}
- **⚠️ Warning**: Performance Schema is not enabled. Warmup cannot be performed on the MySQL database.
{% endif -%}
{% if db_info.engine == 'postgres' and not analysis.database_analysis.content.replication_enabled -%}
- **⚠️ Warning**: Replication is not enabled. Blue-Green deployment cannot be performed on this database.
{% endif -%}

---

## 🔍 Queries Analysis

{% for query in analysis.queries_analysis %}
<details>
  <summary><h3>▶️ Query <span>#</span>{{ loop.index }}</h3></summary>

```sql
{{ query.query }}
```

<h3>📋 Analysis:</h3>
{% if not (query.analysis.query_plan_text or query.analysis.plan_results) %}
  <p>No analysis available.</p>
{% endif %}

{% if query.analysis.plan_text %}
<h4>🗺️ Query Plan:</h4>

```text
{{ query.analysis.plan_text }}
```
{% endif %}

{% if query.analysis.plan_results %}
### 📊 Plan Results:

| Metric | Value |
| ------ | ----- |
{%- for metric, value in query.analysis.plan_results.items() %}
| {{ metric | replace("_", " ") | title }} | {{ value or '-' }} |
{%- endfor -%}
{% endif %}
</details>

{% endfor %}

---

## 📋 Tables Statistics

<details>
  <summary>📊 <h3>Tables Statistics</h3></summary>

| Table | Row Count | Table Size |
| ----- | --------- | --------------- |
{%- for item in analysis.tables_analysis %}
| {{ item.table }} | {{ item.analysis.stats.row_count }} | {{ item.analysis.stats.table_size_text }} |
{%- endfor %}

</details>
