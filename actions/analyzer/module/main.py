from .lib import *
from .shared.configs import Config
from .shared.dto.aws import Env
from .shared.dto.database import DatabaseInfo
from .shared.lib import github
from .shared.lib.database import DatabaseConnector


def main():
    Config.configure_logging()
    arg_parser = ArgumentParser()
    args = arg_parser.parse_args()

    sql_content = args.sql_content.replace('\\n', '\n')

    db_info = DatabaseInfo(
        engine=args.engine,
        database=args.database,
        instance=args.instance,
        rds_host=args.rds_host,
        username=args.username,
        password=args.password,
        schema=args.schema,
        schemas=args.schemas
    )

    env = Env(args.env) if args.env else None

    connector = DatabaseConnector(db_info)
    analyzer = SQLAnalyzer(env, sql_content, db_info, connector)
    analyzer.parse()
    results = analyzer.execute_analysis()

    results.save_to_file()

    github.add_output('analysis', results.analysis)


if __name__ == "__main__":
    main()
