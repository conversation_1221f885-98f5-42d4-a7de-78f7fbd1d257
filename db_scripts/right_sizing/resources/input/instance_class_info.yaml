supported_instance_types:
  - db.m5.24xlarge
  - db.m5.16xlarge
  - db.m5.12xlarge
  - db.m5.8xlarge
  - db.m5.4xlarge
  - db.m5.2xlarge
  - db.m5.xlarge
  - db.m5.large
  - db.m4.16xlarge
  - db.m4.10xlarge
  - db.m4.4xlarge
  - db.m4.2xlarge
  - db.m4.xlarge
  - db.m4.large
  - db.m3.2xlarge
  - db.m3.xlarge
  - db.m3.large
  - db.m3.medium
  - db.r5.24xlarge
  - db.r5.16xlarge
  - db.r5.12xlarge
  - db.r5.8xlarge
  - db.r5.4xlarge
  - db.r5.2xlarge
  - db.r5.xlarge
  - db.r5.large
  - db.r4.16xlarge
  - db.r4.8xlarge
  - db.r4.4xlarge
  - db.r4.2xlarge
  - db.r4.xlarge
  - db.r4.large
  - db.r3.8xlarge
  - db.r3.4xlarge
  - db.r3.2xlarge
  - db.r3.xlarge
  - db.r3.large
  - db.t3.2xlarge
  - db.t3.xlarge
  - db.t3.large
  - db.t3.medium
  - db.t3.small
  - db.t3.micro
  - db.t2.2xlarge
  - db.t2.xlarge
  - db.t2.large
  - db.t2.medium
  - db.t2.small
  - db.t2.micro

higher_instance_class_mapping:
  micro: [small, medium, large, xlarge, 2xlarge, 4xlarge, 8xlarge, 12xlarge, 16xlarge, 32xlarge]
  small: [medium, large, xlarge, 2xlarge, 4xlarge, 8xlarge, 12xlarge, 16xlarge, 32xlarge]
  medium: [large, xlarge, 2xlarge, 4xlarge, 8xlarge, 12xlarge, 16xlarge, 32xlarge]
  large: [xlarge, 2xlarge, 4xlarge, 8xlarge, 12xlarge, 16xlarge, 32xlarge]
  xlarge: [2xlarge, 4xlarge, 8xlarge, 12xlarge, 16xlarge, 32xlarge]
  2xlarge: [4xlarge, 8xlarge, 12xlarge, 16xlarge, 32xlarge]
  4xlarge: [8xlarge, 12xlarge, 16xlarge, 32xlarge]
  8xlarge: [12xlarge, 16xlarge, 32xlarge]
  12xlarge: [16xlarge, 32xlarge]
  16xlarge: [32xlarge]

lower_instance_class_mapping:
  32xlarge: [16xlarge, 12xlarge, 8xlarge, 4xlarge, 2xlarge, xlarge, large, medium, small]
  16xlarge: [12xlarge, 8xlarge, 4xlarge, 2xlarge, xlarge, large, medium, small]
  12xlarge: [8xlarge, 4xlarge, 2xlarge, xlarge, large, medium, small]
  8xlarge: [4xlarge, 2xlarge, xlarge, large, medium, small]
  4xlarge: [2xlarge, xlarge, large, medium, small]
  2xlarge: [xlarge, large, medium, small]
  xlarge: [large, medium, small]
  large: [medium, small]
  medium: [small]

old_generation_classes: [m1,m2,m3,m4,r3,r4,t2]