from gateway.clients import Client
from services.cloud_watch_alarm_manager import CloudWatchAlarmManager
from configs import Configs

import logging
import argparse

# Set up logging configuration
logging.basicConfig(level=logging.INFO)

def main(args):
    logging.info(f"Alarms assignment to the instance {args.db_identifier} started")

    account_id = Configs().get_account_id(args.profile)

    client = Client()
    client.account_arn = account_id
    client.role = "db_rightsizing_role_cross_account"

    rds_client = client.get_session_client('rds')
    ec2_client = client.get_session_client('ec2')
    cloudwatch_client = client.get_session_client('cloudwatch')

    obj = CloudWatchAlarmManager(ec2_client, rds_client, cloudwatch_client, client.account_arn)

    obj.add_alarms_for_instance(args.db_identifier)

    logging.info(f"Alarms assignment to the instance {args.db_identifier} completed")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--db_identifier", required=True)
    parser.add_argument("--profile", required=True)
    arguments = parser.parse_args()
    main(arguments)
