import jpype
import jpype.imports
import pytest
import sqlglot
from sql_metadata import Parser as SQLMetadataParser

from conftest import queries

# noinspection PyUnresolvedReferences
from jpype.types import *

test_ids, queries = queries()


def parse_with_jsqlparser(query):
    jpype.startJVM(classpath=["jsqlparser-5.0.jar"])

    # noinspection PyUnresolvedReferences
    from net.sf.jsqlparser.parser import CCJSqlParserUtil
    # noinspection PyUnresolvedReferences
    from net.sf.jsqlparser.util import TablesNamesFinder
    # noinspection PyUnresolvedReferences
    from net.sf.jsqlparser.statement.select import Select
    # noinspection PyUnresolvedReferences
    from net.sf.jsqlparser.util.TablesNamesFinder import findTables

    try:
        tables = [str(table) for table in findTables(query)]
        statement = CCJSqlParserUtil.parse(query)
        columns = []
        if isinstance(statement, Select):
            select_body = statement.getSelectBody()
            select_items = select_body.getSelectItems()
            for item in select_items:
                columns.append(str(item))
    finally:
        jpype.shutdownJVM()

    return tables, columns


def parse_with_sqlglot(query):
    tables = list(set(table.name for table in sqlglot.parse_one(query).find_all(sqlglot.exp.Table)))
    columns = list(set(column.name for column in sqlglot.parse_one(query).find_all(sqlglot.exp.Column)))
    return tables, columns


def parse_with_sql_metadata(query):
    parser = SQLMetadataParser(query)
    tables = parser.tables
    columns = parser.columns
    return tables, columns


@pytest.mark.parametrize("test_case", queries, ids=test_ids)
def test_parsers(test_case, request):
    _test_id = request.node.callspec.id

    query = test_case["query"]
    expected_parsers = test_case["expected-parsers"]
    expected_tables = test_case["tables"]
    expected_columns = test_case["columns"]

    parsers = {
        "sqlglot": parse_with_sqlglot,
        "sql_metadata": parse_with_sql_metadata,
        "jsqlparser": parse_with_jsqlparser
    }

    successful_parsers = []

    for parser_name, parser_func in parsers.items():
        try:
            tables, columns = parser_func(query)
            if sorted(tables) == sorted(expected_tables) and sorted(columns) == sorted(expected_columns):
                print(f"Parser '{parser_name}' succeeded in parsing the query correctly.")
                successful_parsers.append(parser_name)
        except Exception as e:
            print(f"Parser '{parser_name}' failed with error: {e}")

    assert successful_parsers, "No parser succeeded in parsing the query correctly."
    assert set(successful_parsers).issubset(set(expected_parsers)), \
        f"Expected parsers: {expected_parsers}, but got: {successful_parsers}"


if __name__ == "__main__":
    pytest.main()
