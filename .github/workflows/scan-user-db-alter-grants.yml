name:  <PERSON>an DBs for users with Alter
run-name:  Scan Dbs for users with Alt<PERSON>

on:
  schedule:
    - cron: '0 2 10 * *'
  workflow_dispatch:

jobs:
  dev-rh:
    name: <PERSON>an DEV-RH
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)['dev-rh'].runner }}
    env:
      ACTIONS_ALLOW_USE_UNSECURE_NODE_VERSION: true
    container:
      image: ************.dkr.ecr.eu-west-1.amazonaws.com/storage:latest
      env:
        AWS_REGION: eu-west-1
        AWS_DEFAULT_REGION: eu-west-1
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: arn:aws:iam::************:role/crossaccount/cicd-role
          role-duration-seconds: 3600
          aws-region: eu-west-1

      - name: Checkout
        uses: actions/checkout@v3

      - name: Scan DB users
        id: execute-create
        uses: ./.github/actions/scan-user-db-alter-grants/
        with:
          env: dev-rh
          google_json: ${{ secrets.GOOGLE_JSON }}

  staging-mot:
    name: Scan MOT-STAGING
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)['staging-mot'].runner }}
    env:
      ACTIONS_ALLOW_USE_UNSECURE_NODE_VERSION: true
    container:
      image: ************.dkr.ecr.eu-west-1.amazonaws.com/storage:latest
      env:
        AWS_REGION: eu-west-1
        AWS_DEFAULT_REGION: eu-west-1
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: arn:aws:iam::************:role/careemfoodJenkins
          role-duration-seconds: 3600
          aws-region: eu-west-1

      - name: Checkout
        uses: actions/checkout@v3

      - name: Scan DB users
        id: execute-create
        uses: ./.github/actions/scan-user-db-alter-grants/
        with:
          env: staging-mot
          google_json: ${{ secrets.GOOGLE_JSON }}

  prod-rh:
    name: Scan PROD-RH
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)['prod-rh'].runner }}
    env:
      ACTIONS_ALLOW_USE_UNSECURE_NODE_VERSION: true
    container:
      image: ************.dkr.ecr.eu-west-1.amazonaws.com/storage:latest
      env:
        AWS_REGION: eu-west-1
        AWS_DEFAULT_REGION: eu-west-1
    steps:
      - name: Configure AWS credentials
        env:
          ACTIONS_ALLOW_USE_UNSECURE_NODE_VERSION: true
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: "arn:aws:iam::************:role/crossaccount/cicd-role"
          role-duration-seconds: 3600
          aws-region: eu-west-1

      - name: Checkout
        uses: actions/checkout@v3

      - name: Scan DB users
        id: execute-create
        uses: ./.github/actions/scan-user-db-alter-grants/
        with:
          env: prod-rh
          google_json: ${{ secrets.GOOGLE_JSON }}

  prod-mot:
    name: Scan PROD-MOT
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)['prod-mot'].runner }}
    env:
      ACTIONS_ALLOW_USE_UNSECURE_NODE_VERSION: true
    container:
      image: ************.dkr.ecr.eu-west-1.amazonaws.com/storage:latest
      env:
        AWS_REGION: eu-west-1
        AWS_DEFAULT_REGION: eu-west-1
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: arn:aws:iam::************:role/careemfoodJenkins
          role-duration-seconds: 3600
          aws-region: eu-west-1

      - name: Checkout
        uses: actions/checkout@v3

      - name: Scan DB users
        id: execute-create
        uses: ./.github/actions/scan-user-db-alter-grants/
        with:
          env: prod-mot
          google_json: ${{ secrets.GOOGLE_JSON }}
          
