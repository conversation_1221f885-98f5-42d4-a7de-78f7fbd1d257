import json
from typing import List, Dict

from .record import Record


class TaskResult:
    """
    A structured object to return task results, ensuring proper organization.
    Enforces a sheet name and structured records.
    """

    DEFAULT_COLUMNS = ["Status", "ProfileName", "DBInstanceName", 'Engine', 'C-Service', 'C-Team']

    # def __init__(self, success: bool, sheet_name: str, records: List[Record], skipped: bool = False, columns: List[str] = None):
    def __init__(self, sheet_name: str, columns: List[str] = None):
        self.sheet_name = sheet_name
        self.records: List[Dict] = []
        self.columns = self.DEFAULT_COLUMNS + (columns or [])
        # self.success = success
        # self.records = records
        # self.skipped = skipped

    def add_records(self, profile: str, engine: str, db_instance_name: str, service_tag: str, team_tag: str, new_records: List[Record], status: str = "✅"):
        for record in new_records:
            record_dict = record.to_dict()
            record_dict["Status"] = status
            record_dict["ProfileName"] = profile
            record_dict["DBInstanceName"] = db_instance_name
            record_dict["Engine"] = engine
            record_dict["C-Service"] = service_tag
            record_dict["C-Team"] = team_tag
            self.records.append(record_dict)

    def to_dict(self):
        return {
            "sheet_name": self.sheet_name,
            "records": self.records,
            "columns": self.columns
            # "records": [record.to_dict() for record in self.records],
            # "success": self.success,
            # "skipped": self.skipped,
        }

    def __repr__(self):
        return json.dumps(self.to_dict())
