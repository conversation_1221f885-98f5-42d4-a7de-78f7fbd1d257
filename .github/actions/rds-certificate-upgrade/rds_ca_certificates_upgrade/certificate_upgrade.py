import boto3
from pprint import pprint
import argparse
from rh_prod import RH_PROD
from rh_qa import RH_QA

def upgrade_rds_ca_certificate(db_instance_identifier, ca_certificate_identifier):
    rds_client = boto3.client('rds')
    
    try:
        response = rds_client.modify_db_instance(
            DBInstanceIdentifier=db_instance_identifier,
            CACertificateIdentifier=ca_certificate_identifier,
            ApplyImmediately=True
        )
        print("Upgrade initiated successfully")
    except Exception as e:
        print(f"Error upgrading RDS CA certificate: {e}")

# Specify your RDS instance identifier and the new CA certificate identifier
# for db_instance_identifier in RH_QA['databases']:
#     ca_certificate_identifier = 'rds-ca-rsa2048-g1'
#     upgrade_rds_ca_certificate(db_instance_identifier, ca_certificate_identifier)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--profile", required=True)
    parser.add_argument("--rds_instance", required=True)
    parser.add_argument("--ca_certificate_name", required=True)
    args = parser.parse_args()
    print("AWS Environment: ", args.profile)
    print("RDS INstance: ", args.rds_instance)
    print("CA Certificate: ", args.ca_certificate_name)
    upgrade_rds_ca_certificate(db_instance_identifier=args.rds_instance, ca_certificate_identifier=args.ca_certificate_name)
