name: create RDS snapshot Cross Account
run-name: Execute this workflow so we can create our RDS snapshots

on:
  workflow_dispatch:
    inputs:
      source_account_identifier:
        type: choice
        description: Which is the source account from which we want to copy
        required: true
        options:
          - dev-rh
          - staging-mot
          - prod-rh
          - prod-mot
      rds_identifier:
        type: string
        description: Name of the RDS from which we want to take snapshot
        required: true

jobs:
  create:
    name: creating RDS Snapshot
    runs-on: ${{fromJSON(vars.DB_RUNNERS)[inputs.source_account_identifier]}}
    env:
      AWS_REGION: eu-west-1
      AWS_DEFAULT_REGION: eu-west-1
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{fromJSON(vars.DEVX_PROFILES_ROLES)[inputs.source_account_identifier]}}
          role-duration-seconds: 3600
          aws-region: eu-west-1
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Checkout
        uses: actions/checkout@v3


      - name: create RDS snapshot
        id: execute-upgrade
        uses: ./.github/actions/create-rds-snapshot
        with:
          source_account_identifier: ${{ inputs.source_account_identifier }}
          rds_identifier: ${{inputs.rds_identifier}}