from typing import List, Dict

import boto3

from .database_connector import DatabaseConnector
from ..dto import Record, TaskResult


class DatabaseTask:
    """
    Base class for database-related tasks.

    Each task should implement three phases:
    1. fetch_data - Retrieve data from MySQL, PostgreSQL, DynamoDB, or another source.
    2. process_data - Process, filter, or transform the retrieved data.
    3. return_results - Format the processed data for output.

    Features:
    - Tasks can skip execution by overriding `should_execute`.
    - Database connection mode is read-only by default but can be modified.
    - Execution scope can be defined per instance or per cluster.
    - Must return a TaskResult object containing execution details.

    Attributes:
        execution_scope (str): Determines execution scope ('instance', 'cluster', or 'primary_only').
            - 'instance': Task runs on every instance in the cluster
            - 'cluster': Task runs on one instance per cluster (preferring read replicas)
            - 'primary_only': Task runs only on the primary instance of each cluster
        sheet_name (str): Name of the sheet for structured output.
        session (boto3.Session): AWS session object for accessing resources.
        primary_node (bool): Indicates if the task is running on the primary database node.
        read_only (bool): Whether the database connection is read-only.
        db_info (dict): Database connection details.
        profile (str): AWS profile name.
        data: Holds the fetched data before processing.
    """

    execution_scope: str = "instance"  # Can be "instance", "cluster", or "primary_only"
    sheet_name: str = "DefaultSheet"  # Default sheet name for structured output
    task_result: TaskResult = None
    session: boto3.Session = None
    primary_node: bool = None
    read_only: bool = True
    db_connector: DatabaseConnector = None
    db_info: dict = None
    profile: str = None
    results: List[Dict] = []

    def __init__(self, profile: str, boto3_session: boto3.Session, db_connector: DatabaseConnector, configs: Dict):
        """
        Initializes the DatabaseTask class.

        Args:
            profile (str): AWS profile name to use.
            boto3_session (boto3.Session): AWS session instance.
            db_connector (DatabaseConnector): Database connector instance.
        """
        self.configs = configs
        self.profile = profile
        self.session = boto3_session
        self.db_connector = db_connector
        self.db_info = db_connector.db_info

        # Get instance identifier for logging
        self.instance_id = self.db_info.get('instance_identifier', 'unknown') if self.db_info else 'unknown'
        self.task_name = self.__class__.__name__

        # Get reference to the terminating flag from the connector
        self.terminating_flag = getattr(db_connector, 'terminating_flag', None)

        # Use the logger from the DatabaseConnector
        self.logger = db_connector.logger

        if self.__class__.task_result is None:
            self.__class__.task_result = TaskResult(sheet_name=self.sheet_name, columns=self.columns())

    def should_execute(self) -> bool:
        """
        Determines whether the task should be executed.

        Returns:
            bool: True if the task should execute, False otherwise.
        """
        return True

    @classmethod
    def columns(cls) -> List[str]:
        """
        Specifies the columns that should be returned in the result.
        Must be implemented by subclasses.

        Raises:
            NotImplementedError: If not implemented in a subclass.
        """
        raise NotImplementedError("Columns method must be implemented")

    def fetch_data(self):
        """
        Fetches data from the database. Must be implemented by subclasses.

        Raises:
            NotImplementedError: If not implemented in a subclass.
        """
        raise NotImplementedError("Subclasses must implement fetch_data method")

    def records(self, data) -> List[Record]:
        """
        Converts raw data into a list of Record objects.

        Args:
            data (list[dict]): Raw data fetched from the database.

        Returns:
            List[Record]: A list of structured Record objects.
        """
        return [Record(**row) for row in data]

    def execute(self):
        """
        Executes the database task, following the defined phases.

        Returns:
            TaskResult: Object containing execution results and structured data.
        """
        # Check if termination was requested
        if self.terminating_flag is not None and self.terminating_flag:
            self.logger.info("Termination requested. Aborting task execution.")
            return

        if not self.should_execute():
            self.logger.info("Execution skipped by should_execute()")
            return

        # Safely get database info
        engine = self.db_info.get('engine', 'unknown') if self.db_info else 'unknown'
        instance_name = self.db_info.get('instance_identifier', 'unknown') if self.db_info else 'unknown'
        tags = self.get_instance_tags()

        self.logger.info("Starting fetch_data phase")
        try:
            # Check if termination was requested
            if self.terminating_flag is not None and self.terminating_flag:
                self.logger.info("Termination requested. Aborting fetch_data phase.")
                return

            data = self.fetch_data()

            # Check if termination was requested after fetch_data
            if self.terminating_flag is not None and self.terminating_flag:
                self.logger.info("Termination requested. Aborting record processing.")
                return

            self.logger.info(f"Fetched {len(data)} records")
            status = "✅"

            # If no records were returned, add a single empty record with success status
            if data:
                new_records = self.records(data)
            else:
                new_records = []

        except Exception as e:
            self.logger.error(f"Error in fetch_data: {e}")
            # Create an error record with empty values
            error_data = {column: "" for column in self.columns()}
            new_records = [Record(**error_data)]
            status = "❌"

        # Check if termination was requested before adding records
        if self.terminating_flag is not None and self.terminating_flag:
            self.logger.info("Termination requested. Aborting task completion.")
            return

        self.logger.info("Adding records to TaskResult")
        self.__class__.task_result.add_records(
            profile=self.profile,
            engine=engine,
            db_instance_name=instance_name,
            service_tag=tags.get("c-service", "Unknown"),
            team_tag=tags.get("c-team", "Unknown"),
            new_records=new_records,
            status=status
        )

    def _query(self, query: str, options=None):
        """
        Runs a query on the database using the provided db_info and read_only attributes.

        Args:
            query (str): The SQL query to execute.

        Returns:
            The result of the query execution.
        """
        # Check if termination was requested before executing the query
        if self.terminating_flag is not None and self.terminating_flag:
            self.logger.info("Termination requested. Aborting query execution.")
            return {"error": "Termination requested", "columns": [], "rows": []}

        self.logger.debug(f"Executing query: {query}")
        return self.db_connector.run_query(query, options)

    def get_instance_tags(self) -> Dict[str, str]:
        """
        Retrieves tags for the current database instance.
        """
        # Check if termination was requested
        if self.terminating_flag is not None and self.terminating_flag:
            self.logger.info("Termination requested. Skipping tag retrieval.")
            return {}

        self.logger.debug("Retrieving instance tags")
        try:
            if self.db_info and 'aws_info' in self.db_info and 'TagList' in self.db_info['aws_info']:
                return {tag['Key']: tag['Value'] for tag in self.db_info['aws_info']['TagList']}
            else:
                self.logger.warning("AWS info or TagList not available in db_info")
                return {}
        except Exception as e:
            self.logger.error(f"Error retrieving instance tags: {e}")
            return {}
