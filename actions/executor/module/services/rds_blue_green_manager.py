import copy
import logging
import time
import traceback
from functools import cached_property
from typing import Optional

from .warmup_service import WarmupService
from ..dto import WarmupConfig, ExecutionResult
from ..dto.execution_result.query import Query
from ..shared.dto.analysis import Analysis
from ..shared.dto.database import DatabaseInfo
from ..shared.dto.decision import ExecutionPlan
from ..shared.lib.database.database_connector import DatabaseConnector
from ..shared.lib.rds import RDSOperations
from ..shared.models import RDSInstance, BlueGreenDeployment, DBParameterGroup


class RDSBlueGreenManager:
    def __init__(self, db_info: DatabaseInfo, warmup_config: WarmupConfig = None):
        self.suffix = 'bluegreen-dba-automator'
        self.source_db_info = db_info
        self.blue_green_name = f"{db_info.instance}-bluegreen-{self.suffix}"
        self.temp_pg_name = f"{db_info.instance}-bluegreen-pg-{self.suffix}"
        self.source_rds: RDSInstance = RDSInstance.by_id(db_info.instance)
        # self.green: RDSInstance | None = None
        self.rds_operations = RDSOperations()
        self.bluegreen_deployment: Optional[BlueGreenDeployment] = None
        self.warmup_config = warmup_config

    @property
    def target_rds(self):
        return self.bluegreen_deployment.target_rds

    @cached_property
    def target_db_info(self):
        if self.target_rds.instance_dto.endpoint.Address:
            info = copy.deepcopy(self.source_db_info)
            info.rds_host = self.target_rds.instance_dto.endpoint.Address
            return info

    def create_temp_parameter_group(self) -> DBParameterGroup:
        logging.info("Creating temporary parameter group for blue/green deployment...")

        group = DBParameterGroup.by_name(self.source_rds.instance_dto.parameter_groups[0].name)
        new_group = group.clone(self.temp_pg_name, skip_if_exists=True)

        if self.source_db_info.engine in ['mysql', 'mariadb']:
            new_group.modify([{
                'ParameterName': 'read_only',
                'ParameterValue': '0',
                'ApplyMethod': 'immediate'
            }])
        elif self.source_db_info.engine in ['postgres']:
            new_group.modify([
                {
                    'ParameterName': 'rds.logical_replication',
                    'ParameterValue': '1',
                    'ApplyMethod': 'pending-reboot'
                },
                {
                    'ParameterName': 'default_transaction_read_only',
                    'ParameterValue': 'off',
                    'ApplyMethod': 'immediate'
                }
            ])
        else:
            raise Exception(f"Unsupported engine: {self.source_db_info.engine}")

        return new_group

    def create_bluegreen_deployment(self):
        self.bluegreen_deployment = BlueGreenDeployment.find_or_create(
            # id=self.blue_green_identifier,
            # target_engine_version=self.db_info.target_engine_version,
            name=f"{self.source_db_info.instance}-{self.suffix}",
            # param_group=self.temp_pg_name,
            source=self.source_rds.instance_dto
        )

    def find_bluegreen_deployment(self, bg_id: str = None):
        logging.info(f"Finding Blue/Green deployment {self.blue_green_name}...")
        if bg_id:
            self.bluegreen_deployment = BlueGreenDeployment.by_id(bg_id)
        else:
            self.bluegreen_deployment = BlueGreenDeployment.by_name(self.blue_green_name)
        logging.info(f"Blue/Green deployment {self.blue_green_name} found.")

    def wait_for_bluegreen_availability(self):
        logging.info(f"Waiting for Blue/Green deployment {self.blue_green_name} to become available...")
        self.bluegreen_deployment.wait_for_availability()

    def execute_queries(self, result: ExecutionResult, analysis: Analysis) -> ExecutionResult:
        logging.info("Executing queries on the Blue/Green target instance...")

        if not result.queries:
            logging.info("Initializing execution result queries list.")
            result.queries = [Query(sql=analyze_query.query) for analyze_query in analysis.queries_analysis]

        connector = DatabaseConnector(db=self.target_db_info, read_only=False)
        connector.connect()
        try:
            for query in result.queries:
                if query.time is not None:
                    logging.info(f"The query already executed: {query.sql}")
                    continue

                start_time = time.time()
                try:
                    logging.info(f"Executing query: {query.sql}")
                    query.result = str(connector.execute(query.sql))
                    query.time = time.time() - start_time
                    logging.info(f"Query executed successfully in {query.time:.2f} seconds.")

                except Exception as e:
                    query.error = str(e)
                    logging.error(f"Error executing query: {e}")
        finally:
            logging.info("Query execution completed.")
            connector.close()

        return result

    def switchover(self, on_start=None, on_complete=None):
        logging.info(f"Performing switchover to Blue/Green deployment {self.blue_green_name}...")
        if on_start: on_start()
        self.bluegreen_deployment.switchover()
        logging.info("Switchover to Blue/Green deployment completed.")
        if on_complete: on_complete()

    def cleanup(self):
        logging.info(f'Cleaning up resources...')
        try:
            self.rds_operations.delete_db_parameter_group(self.temp_pg_name)
            logging.info(f'Temporary parameter group {self.temp_pg_name} deleted.')
        except Exception as e:
            logging.error(f"An error occurred while deleting parameter group: {e}")
            logging.error(traceback.format_exc())

    def set_temp_parameter_group(self):
        logging.info("Setting temporary parameter group...")
        self.target_rds.set_parameter_group(self.temp_pg_name)
        logging.info("Temporary parameter group is set.")

    def restore_original_parameter_group(self):
        logging.info("Restoring original parameter group...")
        self.target_rds.set_parameter_group(self.source_rds.instance_dto.parameter_groups[0].name)
        logging.info("Original parameter group restored.")

    def wait_for_parameter_group_to_apply(self, name):
        self.bluegreen_deployment.target_rds.wait_for_parameter_group(name)

    def manage_bluegreen_and_execute(self,
                                     plan: ExecutionPlan,
                                     result: ExecutionResult,
                                     analysis: Analysis,
                                     on_creation=None,
                                     on_complete=None,
                                     on_execute=None
                                     ):
        try:
            if (plan.bg_id and
                    len(analysis.queries_analysis) == len(result.queries) and
                    all(query.time is not None for query in result.queries)
            ):
                logging.info("All queries were already executed. Skipping execution.")
                if on_complete:
                    on_complete()
                return result

            self.create_temp_parameter_group()
            self.create_bluegreen_deployment()
            if on_creation: on_creation(self.bluegreen_deployment.dto.id)
            self.wait_for_bluegreen_availability()
            self.set_temp_parameter_group()  # set temp parameter group to target rds

            self.wait_for_bluegreen_availability()
            self.target_rds.wait_for_availability()

            if on_execute: on_execute()
            self.execute_queries(result, analysis)

            self.restore_original_parameter_group()

            if on_complete: on_complete()
            return result
        except Exception as e:
            logging.error(f"An error occurred: {e}")
            logging.error(traceback.format_exc())
            raise
        finally:
            self.cleanup()

    def warmup(self):
        service = WarmupService(
            self.source_db_info,
            self.target_db_info,
            self.source_rds,
            self.target_rds,
            self.warmup_config
        )

        service.warmup_instance()
