RH_QA = {
    "databases": [	
    "location",	
    "location-platform-db",	
    "location-platform-rr",	
    "logistic-platform",	
    "lp-adapter",	
    "lp-planner",	
    "lp-shipment",	
    "map-store",	
    "micromobility",	
    "outpost",	
    "pact-broker",	
    "partner-analytics-service",	
    "partner-billing",	
    "partner-profile",	
    "payout-info",	
    "places-ingestion",	
    "poi-staging",	
    "qa-5-promotion",	
    "qa-6",	
    "qa-account-balance-service-target",	
    "qa-p2p",	
    "qa-subscription-batch-jobs",	
    "qa-supplier-management-db",
    "qa-supplygate",	
    "qa-supply-registry-db",	
    "qa-user-loyalty-programs",	
    "referral-platform",	
    "rh-k8s-argo",	
    "rides-platform"
    "rumi-rides",	
    "segment-builder",	
    "settlement-orchestrator",	
    "shipment-engine",	
    "shorturl",	
    "smartmobility",	
    "supplygate-prod-vpc-qa",	
    "supplygate-rule-engine",	
    "supply-journey",	
    "supply-profile",	
    "supply-zone-tracker",	
    "tech-blog-db",	
    "tech-blog-db-prod",	
    "transactions-history-db",	
    "trust-and-safety-qa",	
    "unified-profile",	
    "wallet-orchestrator",	
    "wusool"
    ] 
}