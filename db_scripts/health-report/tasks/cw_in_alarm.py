from typing import List, Dict

import boto3

from ..lib import DatabaseTask, DatabaseConnector


class CWInAlarmList(DatabaseTask):
    sheet_name = "In-Alarm"

    ALARM_DESCRIPTIONS = {
        "FreeableMemory": "Low memory can cause database crashes and slow performance.",
        "FreeStorageSpace": "Running out of storage can lead to data loss or service disruptions.",
        "CPUUtilization": "High CPU usage can cause slow queries and system instability.",
        "DatabaseConnections": "Too many connections may lead to service unavailability.",
        "DiskQueueDepth": "High disk queue depth indicates I/O bottlenecks affecting performance.",
        "ReadIOPS": "Unusual read IOPS can indicate excessive query load or performance issues.",
        "ReadLatency": "High read latency can slow down application response times.",
        "WriteIOPS": "Excessive write IOPS can indicate potential storage performance issues.",
        "WriteLatency": "High write latency can cause data writes to be significantly delayed."
    }
    SEVERE_ALARMS = {'FreeableMemory', 'FreeStorageSpace', 'CPUUtilization'}
    MEDIUM_ALARMS = {'DatabaseConnections', 'DiskQueueDepth', 'ReadIOPS', 'ReadLatency', 'WriteIOPS', 'WriteLatency'}

    def __init__(self, profile: str, boto3_session: boto3.Session, db_connector: DatabaseConnector, configs: Dict):
        super().__init__(profile, boto3_session, db_connector, configs)
        self.cloudwatch = self.session.client('cloudwatch')

    @classmethod
    def columns(cls) -> List[str]:
        return ["MetricName", "Severity", "State",  "Notes"]

    def fetch_data(self) -> List[Dict[str, str]]:
        instance = self.db_info.get('instance_identifier')
        self.logger.info(f"Checking in-alarm metrics for instance: {instance}")

        alarm_name_prefix = f"DBA_RDS_{instance}_METRIC_"
        all_alarms = []
        next_token = None

        while True:
            params = {
                "AlarmNamePrefix": alarm_name_prefix,
                "AlarmTypes": ["MetricAlarm"],
                "MaxRecords": 100
            }

            if next_token:
                params["NextToken"] = next_token

            response = self.cloudwatch.describe_alarms(**params)
            all_alarms.extend(response.get("MetricAlarms", []))
            next_token = response.get("NextToken")

            if not next_token:
                break

        in_alarm_data = []
        for alarm in all_alarms:
            if alarm.get("StateValue") == "ALARM":
                metric_name = alarm.get("MetricName", "Unknown")
                description = self.ALARM_DESCRIPTIONS.get(
                    metric_name,
                    "This metric is in an alarm state and needs attention."
                )

                in_alarm_data.append({
                    "MetricName": metric_name,
                    "Severity": self.get_severity(metric_name),
                    "State": "ALARM",
                    "Notes": description
                })

                self.logger.debug(f"Instance {instance} has metric in ALARM state: {metric_name}")

        return in_alarm_data

    def get_severity(self, metric_name: str) -> str:
        if metric_name in self.SEVERE_ALARMS:
            return "Severe"
        elif metric_name in self.MEDIUM_ALARMS:
            return "Medium"
        return "Unknown"
