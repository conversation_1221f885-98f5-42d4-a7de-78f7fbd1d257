from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional

import boto3


class InstanceSelector:
    """
    Selects the best RDS instance for processing based on real-time CloudWatch metrics.
    Prioritizes instances with the lowest CPU utilization.
    """

    def __init__(self, session: boto3.Session):
        """Initializes the InstanceSelector with a boto3 session."""
        self.session = session
        self.cloudwatch = session.client("cloudwatch")

    def get_cloudwatch_metric(self, instance_id: str, metric_name: str) -> float:
        """Fetches the latest CloudWatch metric value for a given instance."""
        response = self.cloudwatch.get_metric_statistics(
            Namespace="AWS/RDS",
            MetricName=metric_name,
            Dimensions=[{"Name": "DBInstanceIdentifier", "Value": instance_id}],
            StartTime=(datetime.now(timezone.utc) - timedelta(minutes=5)).isoformat(),
            EndTime=datetime.now(timezone.utc).isoformat(),
            Period=300,
            Statistics=["Average"]
        )

        datapoints = response.get("Datapoints", [])
        if not datapoints:
            return float("inf")  # Default to high value if no data is available

        return datapoints[-1]["Average"]

    def score_instance(self, instance: Dict) -> float:
        """Assigns a score to an instance based on real-time CloudWatch CPU utilization."""
        instance_id = instance["DBInstanceIdentifier"]
        cpu_utilization = self.get_cloudwatch_metric(instance_id, "CPUUtilization")

        score = 100 - cpu_utilization  # Lower CPU usage is better
        return score

    def get_best_read_replica(self, instances: List[Dict]) -> Optional[Dict]:
        read_replicas = [inst for inst in instances if inst.get("ReadReplicaSourceDBInstanceIdentifier")]

        if not read_replicas:
            return None  # No read replicas exist

        # Choose the read replica with the best score
        best_replica = max(read_replicas, key=self.score_instance)
        return best_replica

    def select_best_instance(self, instances: List[Dict]) -> Optional[Dict]:
        """
        Selects the best instance to process:
        - Prefer read replicas with the best score.
        - If no read replicas exist, process the primary instance with the best score.
        """
        best_replica = self.get_best_read_replica(instances)
        if best_replica:
            return best_replica

        # No read replicas found, select the best primary instance
        return max(instances, key=self.score_instance) if instances else None
