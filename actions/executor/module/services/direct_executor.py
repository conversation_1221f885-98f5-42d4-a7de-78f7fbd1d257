import logging
import time

from ..dto import ExecutionResult
from ..dto.execution_result.query import Query
from ..shared.dto.analysis import Analysis
from ..shared.dto.database import DatabaseInfo
from ..shared.dto.decision import ExecutionPlan
from ..shared.lib.database import DatabaseConnector


class DirectExecutor:
    def __init__(self, result: ExecutionResult, decision: ExecutionPlan, analysis: Analysis, db_info: DatabaseInfo):
        self.result = result
        self.decision = decision
        self.analysis = analysis
        self.db_info = db_info
        self.connector = DatabaseConnector(db=db_info, read_only=False, never_timeout=True)

    def execute(self, on_start=None, on_complete=None) -> ExecutionResult:
        logging.info("Executing queries on the target instance...")

        if not self.result.queries:
            logging.info("Initializing execution result queries list.")
            self.result.queries = [Query(sql=analyze_query.query) for analyze_query in self.analysis.queries_analysis]

        self.connector.connect()
        try:
            if on_start: on_start()
            logging.info("Starting query execution.")

            for query in self.result.queries:
                if query.time is not None:
                    logging.info(f"The query already executed: {query.sql}")
                    continue

                start_time = time.time()
                try:
                    logging.info(f"Executing query: {query.sql}")
                    query.result = str(self.connector.execute(query.sql))
                    query.time = time.time() - start_time
                    logging.info(f"Query executed successfully")
                except Exception as e:
                    query.error = str(e)
                    logging.error(f"Error executing query: {e}")

        finally:
            if on_complete: on_complete()
            logging.info("Query execution completed.")
            self.connector.close()

        return self.result
