import logging
import signal
from contextlib import contextmanager
from typing import Callable, Any

from ..exceptions import TimeoutException


class StoppableExecution:
    def __init__(self, timeout_duration: int):
        self.timeout_duration = timeout_duration
        self.timed_out = False

    @contextmanager
    def timeout_context(self):
        """Set up the timeout context using signals."""

        def handler(signum, frame):
            self.timed_out = True
            raise TimeoutException("Execution timed out")

        signal.signal(signal.SIGALRM, handler)
        signal.alarm(self.timeout_duration)
        try:
            yield
        finally:
            signal.alarm(0)

    def execute(self, func: Callable, *args, **kwargs) -> Any:
        """Executes a function with the timeout constraint."""
        self.timed_out = False
        try:
            with self.timeout_context():
                # Execute the provided function within the timeout context
                return func(*args, **kwargs)
        except TimeoutException as e:
            # If timed_out is True, raise TimeoutException
            logging.error("Execution timed out")
            raise TimeoutException("Execution exceeded the time limit") from e
        except Exception as e:
            # If another error occurs after a timeout, still prioritize TimeoutException
            if self.timed_out:
                logging.error(f"Execution timed out with an additional error {e}")
                raise TimeoutException("Execution exceeded the time limit") from e
            else:
                raise