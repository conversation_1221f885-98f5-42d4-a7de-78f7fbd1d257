name: Preparation Step
description: A custom action to prepare the workflow by scanning changed files and setting outputs.

outputs:
  sql_file:
    description: 'SQL file'
    value: ${{ steps.set-output.outputs.sql_file }}
  skip_workflow:
    description: 'If should skip the workflow'
    value: ${{ steps.set-output.outputs.skip_workflow }}

runs:
  using: 'composite'
  steps:
    - name: Scan changed Files
      uses: tj-actions/changed-files@v45
      id: changed_files
      with:
        files: |
          **/*.sql

    - name: Set outputs
      uses: actions/github-script@v7
      id: set-output
      env:
        added_files: ${{ steps.changed_files.outputs.added_files }}
        modified_files: ${{ steps.changed_files.outputs.modified_files }}
        deleted_files: ${{ steps.changed_files.outputs.deleted_files }}
      with:
        script: |
          const addedFiles = process.env.added_files ? process.env.added_files.split(',') : [];
          const modifiedFiles = process.env.modified_files ? process.env.modified_files.split(',') : [];
          const deletedFiles = process.env.deleted_files ? process.env.deleted_files.split(',') : [];

          console.log('Added files:', addedFiles);
          console.log('Modified files:', modifiedFiles);
          console.log('Deleted files:', deletedFiles);

          if (modifiedFiles.length > 0) {
              core.setFailed('Error: Migration file modification is not allowed.');
          } else if (deletedFiles.length > 0) {
              core.setFailed('Error: Deleting migration files is not allowed.');
          } else if (addedFiles.length > 1) {
              core.setFailed('Error: Only one SQL file is allowed in the PR.');
          } else if (addedFiles.length === 1) {
              core.setOutput("skip_workflow", 'false');
              core.setOutput("sql_file", addedFiles[0]);
          } else {
              core.setOutput("skip_workflow", 'true');
          }