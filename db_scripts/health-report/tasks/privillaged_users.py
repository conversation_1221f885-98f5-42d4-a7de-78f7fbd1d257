import re
from typing import List, Dict
import boto3
from botocore.exceptions import BotoCoreError, ClientError

from ..lib import DatabaseTask


class PrivilegedUsersAudit(DatabaseTask):
    sheet_name = "Privileged Users (Excl. Service)"
    execution_scope = "cluster"
    # read_only = True  # Explicitly set to True since we only need read access

    @classmethod
    def columns(cls) -> List[str]:
        return ["Grantee", "Privilege", "Database", "Schema", "Table"]

    def should_execute(self) -> bool:
        """Check if this task should be executed on this instance."""
        instance_id = self.db_info.get("instance_identifier", "")

        # Skip problematic instances
        problematic_instances = ["confluence-care-db-new", "confluence-care-new"]
        if instance_id in problematic_instances:
            self.logger.info(f"Skipping PrivilegedUsersAudit on known problematic instance: {instance_id}")
            return False

        # Check instance status
        if self.db_info.get("aws_info"):
            status = self.db_info["aws_info"].get("DBInstanceStatus", "").lower()
            if status != "available":
                self.logger.info(f"Skipping PrivilegedUsersAudit on instance with status {status}: {instance_id}")
                return False

        return True

    def fetch_data(self) -> List[Dict[str, str]]:
        self.logger.info("Starting fetch_data for PrivilegedUsersAudit")
        engine = self.db_info.get("engine")
        data = []

        # System filters
        mysql_system_users = {"mysql.sys", "rdsadmin", "repl"}
        mysql_system_schemas = {"mysql", "information_schema", "performance_schema", "sys"}
        postgres_system_users = {"postgres", "rdsadmin"}
        postgres_system_schemas = {"pg_catalog", "information_schema", "pg_toast"}

        # Admin-level privilege keywords
        admin_privileges = {
            "ALL PRIVILEGES", "SUPER", "RELOAD", "SHUTDOWN", "PROCESS",
            "FILE", "GRANT OPTION", "CREATE", "DROP", "ALTER", "REFERENCES",
            "TRIGGER", "CREATE USER"
        }

        # Class-level cache for secrets per AWS profile
        if not hasattr(PrivilegedUsersAudit, '_secret_names_cache'):
            PrivilegedUsersAudit._secret_names_cache = {}

        # Fetch service user identifiers from AWS Secrets Manager with caching per profile
        def fetch_secret_names_from_aws() -> set:
            # Use the AWS profile as the cache key
            profile_name = self.profile

            # Check if we already have cached secrets for this profile
            if profile_name in PrivilegedUsersAudit._secret_names_cache:
                self.logger.info(f"Using cached secrets for profile: {profile_name}")
                return PrivilegedUsersAudit._secret_names_cache[profile_name]

            try:
                # Fetch all secrets without filtering
                secrets_manager = self.session.client("secretsmanager")
                secret_names = set()

                # Use pagination to handle large numbers of secrets
                paginator = secrets_manager.get_paginator("list_secrets")
                for page in paginator.paginate(MaxResults=100):
                    for secret in page.get("SecretList", []):
                        secret_names.add(secret.get("Name", ""))

                # Cache the results for this profile
                PrivilegedUsersAudit._secret_names_cache[profile_name] = secret_names
                self.logger.info(f"Cached {len(secret_names)} secrets for profile: {profile_name}")
                return secret_names
            except (BotoCoreError, ClientError) as e:
                self.logger.warning(f"Failed to fetch secrets from Secrets Manager: {e}")
                return set()

        secret_names = fetch_secret_names_from_aws()

        if not secret_names:
            self.logger.warning("No secrets fetched. Service user check will rely only on name prefix 'srv_'.")

        # Cache service user check results for this instance
        service_user_cache = {}

        def is_service_user(user: str) -> bool:
            # Check cache first for performance
            if user in service_user_cache:
                return service_user_cache[user]

            # Primary check: is this user in our secrets?
            # This is the most reliable way to identify service users
            if user in secret_names:
                service_user_cache[user] = True
                return True

            # Fallback check: common naming patterns for service users
            # This is a heuristic approach when secrets don't contain the user
            user_lower = user.lower()

            # Common service user indicators
            excluded_patterns = ["srv", "service", "dynatrace", "vividcortex", "rds_superuser_role@%","careemuser", "db.portal", "system", "app"]
            if any(pattern in user_lower for pattern in excluded_patterns):
                service_user_cache[user] = True
                return True

            # Check for common separators that indicate service accounts
            if "_" in user or "-" in user:
                # Service accounts often have underscores or hyphens and numbers
                if any(c.isdigit() for c in user):
                    service_user_cache[user] = True
                    return True

            # Not identified as a service user
            service_user_cache[user] = False
            return False

        # MySQL logic
        if engine == "mysql":
            self.logger.info("Fetching MySQL users and privileges")

            # Original approach: query each user's grants individually
            users_query = "SELECT user, host FROM mysql.user"
            users_result = self._query(users_query)

            # Check if there was an error with the query
            if "error" in users_result:
                self.logger.error(f"Error fetching MySQL users: {users_result['error']}")
                raise Exception(f"Database query failed: {users_result['error']}")

            # Check if the query returned a valid result
            if not users_result or not isinstance(users_result, dict) or 'rows' not in users_result:
                self.logger.warning(f"Failed to fetch MySQL users for {self.db_info.get('instance_identifier')}")
                return data

            for user, host in users_result['rows']:
                if user in mysql_system_users or is_service_user(user):
                    continue

                # Use a more efficient query with a timeout
                grants_query = f"SHOW GRANTS FOR '{user}'@'{host}'"
                grants_result = self._query(grants_query, options={"timeout": 10})

                # Check if there was an error with the query
                if "error" in grants_result:
                    self.logger.warning(f"Error fetching grants for user {user}@{host}: {grants_result['error']}")
                    continue

                # Check if the query returned a valid result
                if not grants_result or not isinstance(grants_result, dict) or 'rows' not in grants_result:
                    self.logger.warning(f"Failed to fetch grants for user {user}@{host}")
                    continue

                grants_rows = grants_result['rows']

                for grant in grants_rows:
                    grant_str = grant[0]
                    self.logger.debug(f"Processing grant string: {grant_str}")
                    # Extract privileges from the grant string
                    privilege_match = re.search(r"GRANT\s+(.+?)\s+ON", grant_str, re.IGNORECASE)
                    if not privilege_match:
                        self.logger.debug(f"No privilege match found in: {grant_str}")
                        continue

                    privileges = [p.strip().upper() for p in privilege_match.group(1).split(",")]
                    relevant_privs = [p for p in privileges if p in admin_privileges]
                    if not relevant_privs:
                        continue

                    # Extract the database and table information from the grant string
                    table_schema, table_name = ("N/A", "N/A")

                    # Check for the problematic format first (ON * TO)
                    if "SLAVE, REPLICATION CLIENT, CREATE, CREATE ROUTINE, ALTER ROUTINE, CREATE USER, EVENT, TRIGGER, CREATE ROLE, DROP ROLE ON *" in grant_str:
                        # This is the exact problematic format you mentioned
                        table_schema = "*"
                        table_name = "*"
                        self.logger.info(f"Matched exact problematic format")
                    elif re.search(r"\bON\s+\*\s+TO\b", grant_str, re.IGNORECASE):
                        table_schema = "*"
                        table_name = "*"
                        self.logger.info(f"Matched 'ON * TO' format: {grant_str}")
                    # Check for *.* format
                    elif re.search(r"\bON\s+\*\.\*\s+TO\b", grant_str, re.IGNORECASE):
                        table_schema = "*"
                        table_name = "*"
                        self.logger.info(f"Matched 'ON *.* TO' format: {grant_str}")
                    else:
                        # Standard format with database.table
                        db_table_match = re.search(r"ON\s+(?:`?([^`\s\.]+)`?\.`?([^`\s]+|\*)`?|`?([^`\s\.]+)`?)\s+TO", grant_str, re.IGNORECASE)
                        if db_table_match:
                            if db_table_match.group(1) is not None and db_table_match.group(2) is not None:
                                # Format: database.table or database.*
                                table_schema = db_table_match.group(1)
                                table_name = db_table_match.group(2)
                                self.logger.info(f"Matched database.table format: {table_schema}.{table_name}")
                            elif db_table_match.group(3) is not None:
                                # Format: database (without table)
                                table_schema = db_table_match.group(3)
                                self.logger.info(f"Matched database format: {table_schema}")
                        else:
                            self.logger.warning(f"Could not parse schema/table from grant string: {grant_str}")

                    # Skip system schemas
                    if table_schema in mysql_system_schemas:
                        self.logger.info(f"Skipping system schema: {table_schema}")
                        continue

                    for priv in relevant_privs:
                        data.append({
                            "Grantee": f"{user}@{host}",
                            "Privilege": priv,
                            "Database": "N/A",
                            "Schema": table_schema,
                            "Table": table_name
                        })

        # PostgreSQL logic
        elif engine == "postgres":
            self.logger.info("Fetching PostgreSQL users and privileges")

            # First query: Get table privileges
            users_query = """
                SELECT grantee, privilege_type, table_catalog, table_schema, table_name
                FROM information_schema.role_table_grants
                WHERE table_schema NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
            """
            users_result = self._query(users_query, options={"timeout": 15})

            # Check if there was an error with the query
            if "error" in users_result:
                self.logger.error(f"Error fetching PostgreSQL users: {users_result['error']}")
                raise Exception(f"Database query failed: {users_result['error']}")

            # Check if the query returned a valid result
            if not users_result or not isinstance(users_result, dict) or 'rows' not in users_result:
                self.logger.warning(f"Failed to fetch PostgreSQL users for {self.db_info.get('instance_identifier')}")
                return data

            for grantee, privilege, db, schema, table in users_result['rows']:
                if (
                    grantee in postgres_system_users or
                    is_service_user(grantee) or
                    schema in postgres_system_schemas or
                    db in postgres_system_schemas
                ):
                    continue

                if privilege.upper() not in admin_privileges:
                    continue

                data.append({
                    "Grantee": grantee,
                    "Privilege": privilege,
                    "Database": db,
                    "Schema": schema,
                    "Table": table
                })

            # Second query: Get namespace privileges
            ns_privileges_query = """
                SELECT r.usename AS grantor,
                       e.usename AS grantee,
                       nspname AS schema,
                       privilege_type,
                       is_grantable
                FROM pg_namespace
                         JOIN LATERAL (SELECT * FROM aclexplode(nspacl)) a ON true
                         JOIN pg_user e ON a.grantee = e.usesysid
                         JOIN pg_user r ON a.grantor = r.usesysid
                WHERE nspname NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
            """
            ns_privileges_result = self._query(ns_privileges_query, options={"timeout": 15})

            # Check if there was an error with the query
            if "error" in ns_privileges_result:
                self.logger.warning(f"Error fetching PostgreSQL namespace privileges: {ns_privileges_result['error']}")
                # Continue with the data we have so far instead of failing completely
                return data

            # Check if the query returned a valid result
            if not ns_privileges_result or not isinstance(ns_privileges_result, dict) or 'rows' not in ns_privileges_result:
                self.logger.warning(f"Failed to fetch PostgreSQL namespace privileges for {self.db_info.get('instance_identifier')}")
                return data

            for grantor, grantee, schema, privilege, is_grantable in ns_privileges_result['rows']:
                if (
                    grantee in postgres_system_users or
                    is_service_user(grantee) or
                    schema in postgres_system_schemas
                ):
                    continue

                if privilege.upper() not in admin_privileges:
                    continue

                data.append({
                    "Grantee": grantee,
                    "Privilege": privilege,
                    "Database": "N/A",
                    "Schema": schema,
                    "Table": "N/A"
                })

        self.logger.info("Completed fetch_data for PrivilegedUsersAudit")
        return data