from typing import List, Optional

from pydantic import BaseModel


class ManagerInputs(BaseModel):
    token: Optional[str] = None
    repository: Optional[str] = None
    issue_number: Optional[int] = None
    comment_id: Optional[int] = None
    body: Optional[str] = None
    body_path: Optional[str] = None
    edit_mode: Optional[str] = 'append'
    append_separator: Optional[str] = 'newline'
    reactions: Optional[List[str]] = None
    reactions_edit_mode: Optional[str] = 'append'

    class Config:
        json_schema_extra = {
            "example": {
                "token": "ghp_exampleToken",
                "repository": "owner/repo",
                "issue_number": 123,
                "comment_id": 123,
                "body": "some text",
                "body_path": "path/to/file",
                "edit_mode": "create",
                "append_separator": "separator",
                "reactions": ["+1"],
                "reactions_edit_mode": "replace"
            }
        }
