from db_scripts.right_sizing.ResourceChecker.cpu import CPUResourceChecker
from db_scripts.right_sizing.ResourceChecker import MemoryResourceChecker
from db_scripts.right_sizing.ResourceChecker.connection import ConnectionResourceChecker


class ResourceProvisioner:

    def __init__(self, provisioning_type):
        self.provisioning_type = provisioning_type

    def check(self, stats):
        if self.provisioning_type == 'CPU':
            return CPUResourceChecker(stats).check()  # This will be the object
        elif self.provisioning_type == 'MEM':
            return MemoryResourceChecker(stats).check()  # This will be the object
        elif self.provisioning_type == 'CONN':
            return ConnectionResourceChecker(stats).check()  # This will be the object
