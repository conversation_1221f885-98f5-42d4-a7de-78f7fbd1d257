from boto3 import client
import logging

class Client:

    def __init__(self):
        self.account_id = ""
        self.role = ""

    def get_sts_session_info(self, account_id=None, role=None):
        account_id = account_id or self.account_id
        role = role or self.role
        sts_connection = client('sts')
        sts_session = sts_connection.assume_role(
            RoleArn=f"arn:aws:iam::{account_id}:role/{role}",
            RoleSessionName="cross_acct_lambda"
        )
        logging.info(f"Assumed role successfully: arn:aws:iam::{account_id}:role/{role}")
        return sts_session['Credentials']['AccessKeyId'], sts_session['Credentials']['SecretAccessKey'], \
            sts_session['Credentials']['SessionToken']

    def get_session_client(self, client_type):
        ACCESS_KEY, SECRET_KEY, SESSION_TOKEN = self.get_sts_session_info(account_id=self.account_id)
        return client(
            client_type,
            aws_access_key_id=ACCESS_KEY,
            aws_secret_access_key=SECRET_KEY,
            aws_session_token=SESSION_TOKEN,
        )

    def get_default_client(self, client_type):
        return client(client_type)
