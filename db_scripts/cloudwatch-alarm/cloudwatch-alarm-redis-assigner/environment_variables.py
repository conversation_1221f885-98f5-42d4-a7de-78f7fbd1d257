import os

BUCKET_NAME = os.environ.get('BUCKET_NAME', 'dba-team-khi')

FILE_PATH = os.environ.get('FILE_PATH', 'conf.yaml')

MIN_THRESHOLD_FREEABLE = float(os.environ.get('MIN_THRESHOLD_FREEABLE', 500))

MAX_THRESHOLD_FREEABLE = float(os.environ.get('MAX_THRESHOLD_FREEABLE', 100))

THRESHOLD_PERCENT_FREEABLE = float(os.environ.get('THRESHOLD_PERCENT_FREEABLE', 0.8))

THRESHOLD_PERCENT_DATABASE = float(os.environ.get('THRESHOLD_PERCENT_DATABASE_CONNECTIONS', 0.15))

THRESHOLD_PERCENT_STORAGE = float(os.environ.get('THRESHOLD_PERCENT_STORAGE', 0.05))

THRESHOLD_WRITE_IOPS=float(os.environ.get('THRESHOLD_WRITE_IOPS', 0.6))

THRESHOLD_READ_IOPS=float(os.environ.get('THRESHOLD_READ_IOPS', 0.6))

ALARM_PREFIX = 'DBA_REDIS_'

MAX_ALLOWED_CONNECTIONS = 65000
