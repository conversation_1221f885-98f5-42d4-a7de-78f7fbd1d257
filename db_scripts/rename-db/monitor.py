import threading
import time
import logging
import pymysql

class DBMonitor:
    def __init__(self, db_credentials, record_name, check_interval=2):
        self.db_credentials = db_credentials
        self.db_credentials['host'] = record_name
        self.check_interval = check_interval
        self.stop_event = threading.Event()
        self.logger = logging.getLogger('DBMonitor')

    def keep_alive_connection(self):
        while not self.stop_event.is_set():
            try:
                connection = pymysql.connect(**self.db_credentials)
                if connection.open:
                    self.logger.info(f"Background connection to {self.db_credentials['host']} is active")
                    time.sleep(self.check_interval)
                else:
                    self.logger.warning(f"Background connection to {self.db_credentials['host']} failed")
            except pymysql.MySQLError as e:
                self.logger.warning(f"Error connecting to {self.db_credentials['host']}: {e}")
                time.sleep(self.check_interval)
            finally:
                if connection and connection.open:
                    connection.close()

    def start(self):
        self.thread = threading.Thread(target=self.keep_alive_connection, daemon=True)
        self.thread.start()

    def stop(self):
        self.stop_event.set()
        self.thread.join()