import json
import logging
import os
from types import SimpleNamespace
from client import Client
from env_variables import SECRET_ID
from slack_confg import SlackConfig
from dms_enum import DMSAlarmType
class Config:
    identifier = None
    slack_app_name = ''
    slack: SlackConfig = None
    sd_account_id = None
    name_space = None
    redis_instance_details = None
    dms_alarm_type: DMSAlarmType = None
    dms_resource_id = ''
    aws_region = ''
    def load_configs(self, account_id, name_space, identifier, aws_region):
        logging.info("Fetching Configurations")
        # self.set_logging_config()
        Config.slack = SlackConfig(account_id=account_id, name_space=name_space)
        Config.secrets = self.get_secrets_from_secret_manager(SECRET_ID)
        Config.slack_token = Config.secrets["slack_token"]
        Config.vo_hook = Config.secrets["victorops_hook"]
        Config.sd_account_id = os.environ['SERVICE_DIRECTORY_ACCOUNT_ID']
        Config.identifier = identifier
        Config.account_id = account_id
        Config.name_space = name_space
        Config.aws_region = aws_region
        Config.dms_alarm_type = ''
        Config.dms_resource_id = ''
        Config.redis_instance_details = ''
        Config.redis_type = ''
        Config.default_vo_route = os.environ['DEFAULT_VO_ROUTE']
        Config.default_slack_group = os.environ['DEFAULT_SLACK_GROUP']
        logging.info("Fetch Complete")

        Config.service_directory = SimpleNamespace(
            bucket=os.environ['SERVICE_DIRECTORY_BUCKET'],
            file=os.environ['SERVICE_DIRECTORY_FILE']
        )

        if name_space == 'RDS' or name_space == 'DMS':
            Config.slack_app_name = 'Cloud Watch Alert'
        else:
            Config.slack_app_name = 'Redis Events Alert'
            
        

    def set_logging_config(self):
        root = logging.getLogger()
        if root.handlers:
            for handler in root.handlers:
                root.removeHandler(handler)
        logging.basicConfig(
            format='%(asctime)s %(levelname)-8s %(message)s',
            level=logging.INFO,
            datefmt='%Y-%m-%d %H:%M:%S')

    def get_secrets_from_secret_manager(self, secret_id):
        logging.info(f"Fetching secrets from secret manager for secret id: {secret_id}")
        secret_manager = Client().get_default_client('secretsmanager')
        secrets = secret_manager.get_secret_value(SecretId=secret_id)
        return json.loads(secrets['SecretString'])
