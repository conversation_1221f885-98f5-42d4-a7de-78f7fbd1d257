import boto3
import time
from clients import  Client
import threading
import sys

import sys

if len(sys.argv) != 3:
    print("Usage: python3 file.py arg1 arg2 ")
    sys.exit(1)


aws_account=''
aws_account_name = sys.argv[1]
snapshot_name = sys.argv[2]
rds_client = boto3.client('rds')
VpcSecurityGroupIds=[]
DBSubnetGroupName=''

if aws_account_name == 'dev-rh':
    aws_account = '************'
    VpcSecurityGroupIds=['sg-ddce24bb',]
    DBSubnetGroupName='default'

if aws_account_name == 'prod-rh':
    aws_account = '************'
    VpcSecurityGroupIds = ['sg-60b0f105']
    DBSubnetGroupName = 'vpc-prod-rds'

if aws_account_name == 'staging-mot':
    aws_account = '************'
    VpcSecurityGroupIds = ['sg-02dabab245de426ae']
    DBSubnetGroupName = 'rds_pvt_group'

if aws_account_name == 'prod-mot':
    aws_account = '************'
    VpcSecurityGroupIds = ['sg-09fb6a4726c614762']
    DBSubnetGroupName = 'microservicesdb-mydbsubnetgroup-u6jyuhnn09jb'





instance_identifier = snapshot_name+'-restored'



# Check if the instance already exists
try:
    response = rds_client.describe_db_instances(DBInstanceIdentifier=instance_identifier)
    if len(response['DBInstances']) > 0:
        # Instance exists, delete it first
        print(f"Instance '{instance_identifier}' already exists. Deleting it...")
        rds_client.delete_db_instance(
            DBInstanceIdentifier=instance_identifier,
            SkipFinalSnapshot=True  # Change to False if you want a final snapshot
        )

        # Wait for the instance to be deleted
        print(f"Waiting for instance '{instance_identifier}' to be deleted...")
        waiter = rds_client.get_waiter('db_instance_deleted')
        waiter.wait(DBInstanceIdentifier=instance_identifier)
        print(f"Instance '{instance_identifier}' deleted successfully.")
except rds_client.exceptions.DBInstanceNotFoundFault:
    # Instance doesn't exist, no need to delete
    print(f"Instance '{instance_identifier}' not found. Proceeding to restore...")


response = rds_client.restore_db_instance_from_db_snapshot(
    DBInstanceIdentifier=instance_identifier,
    DBSnapshotIdentifier=snapshot_name,
    VpcSecurityGroupIds=VpcSecurityGroupIds,
    DBSubnetGroupName=DBSubnetGroupName,
    PubliclyAccessible=False,
)

print(response)



