analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48252928, "performance_schema": false, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48252928, "performance_schema": false, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48252928, "performance_schema": false, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48252928, "performance_schema": false, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "payout-info", "content": {"db_size_bytes": 48269312, "performance_schema": true, "db_size_text": "48.3 MB"}}, "queries_analysis": [{"query": "UPDATE payment_method SET status = 1\nWHERE\n  id = 131", "analysis": {"plan_text": "+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table          | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | payment_method |              | range  | PRIMARY         | PRIMARY |         8 | const |      1 |        100 | Using where |\n+------+---------------+----------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "payment_method", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 579, "table_size_text": "278.5 kB"}}}]}
analysis={"database_analysis": {"name": "screen_be", "content": {"db_size_bytes": 12550947, "db_size_text": "12.6 MB"}}, "queries_analysis": [{"query": "ALTER TABLE public.searchs ADD COLUMN description TEXT", "analysis": {}}, {"query": "CREATE TABLE public.audit_logs (\n  id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,\n  action TEXT NOT NULL,\n  user_id TEXT NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  details JSONB\n)", "analysis": {}}, {"query": "UPDATE public.users SET state = 'inactive'\nWHERE\n  NOT deleted_at IS NULL", "analysis": {"plan_text": "Update on users  (cost=0.00..12.10 rows=0 width=0)\n  ->  Seq Scan on users  (cost=0.00..12.10 rows=209 width=38)\n        Filter: (deleted_at IS NOT NULL)\nPlanning Time: 0.078 ms", "plan_results": {"estimated_scanned_rows": 209, "estimated_created_rows": null, "estimated_updated_rows": 0, "estimated_deleted_rows": null, "planning_time": 3.893}}}, {"query": "DELETE FROM public.projects\nWHERE\n  state = 'archived' AND deleted_at < CURRENT_TIMESTAMP - INTERVAL '1 YEAR'", "analysis": {"plan_text": "Delete on projects  (cost=0.14..8.17 rows=0 width=0)\n  ->  Index Scan using idx_projects_state on projects  (cost=0.14..8.17 rows=1 width=6)\n        Index Cond: (state = 'archived'::text)\n        Filter: (deleted_at < (CURRENT_TIMESTAMP - '1 year'::interval))\nPlanning Time: 0.094 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": 0, "planning_time": 8.621}}}, {"query": "SELECT\n  u.name AS user_name,\n  p.name AS project_name,\n  r.name AS role_name\nFROM public.users AS u\nJOIN public.projects AS p\n  ON u.project = p.project\nJOIN public.roles AS r\n  ON p.project = r.project\nWHERE\n  u.project = 'default'", "analysis": {"plan_text": "Nested Loop  (cost=0.43..24.51 rows=1 width=96)\n  ->  Nested Loop  (cost=0.29..16.34 rows=1 width=96)\n        ->  Index Scan using idx_users_project on users u  (cost=0.14..8.16 rows=1 width=64)\n              Index Cond: (project = 'default'::text)\n        ->  Index Scan using idx_projects_project on projects p  (cost=0.14..8.16 rows=1 width=64)\n              Index Cond: (project = 'default'::text)\n  ->  Index Scan using idx_roles_project on roles r  (cost=0.14..8.16 rows=1 width=64)\n        Index Cond: (project = 'default'::text)\nPlanning Time: 0.184 ms", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 4.963}}}, {"query": "SELECT\n  id,\n  name,\n  data ->> 'key' AS key_value\nFROM public.searchs\nWHERE\n  data @> '{\"key\": \"value\"}'", "analysis": {"plan_text": "Seq Scan on searchs  (cost=0.00..12.63 rows=2 width=96)\n  Filter: (data @> '{\"key\": \"value\"}'::jsonb)\nPlanning Time: 0.089 ms", "plan_results": {"estimated_scanned_rows": 2, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 4.183}}}, {"query": "CREATE INDEX idx_users_created_at ON public.users(created_at)", "analysis": {}}], "tables_analysis": [{"table": "searchs", "analysis": {"stats": {"table_size_bytes": 57344, "row_count": -1, "table_size_text": "57.3 kB"}}}, {"table": "users", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}, {"table": "projects", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}, {"table": "roles", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}]}
analysis={"database_analysis": {"name": "screen_be", "content": {"db_size_bytes": 12550947, "db_size_text": "12.6 MB"}}, "queries_analysis": [{"query": "ALTER TABLE public.searchs ADD COLUMN description TEXT", "analysis": {}}, {"query": "CREATE TABLE public.audit_logs (\n  id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,\n  action TEXT NOT NULL,\n  user_id TEXT NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  details JSONB\n)", "analysis": {}}, {"query": "UPDATE public.users SET state = 'inactive'\nWHERE\n  NOT deleted_at IS NULL", "analysis": {"plan_text": "Update on users  (cost=0.00..12.10 rows=0 width=0)\n  ->  Seq Scan on users  (cost=0.00..12.10 rows=209 width=38)\n        Filter: (deleted_at IS NOT NULL)\nPlanning Time: 0.065 ms", "plan_results": {"estimated_scanned_rows": 209, "estimated_created_rows": null, "estimated_updated_rows": 0, "estimated_deleted_rows": null, "planning_time": 0.26}}}, {"query": "DELETE FROM public.projects\nWHERE\n  state = 'archived' AND deleted_at < CURRENT_TIMESTAMP - INTERVAL '1 YEAR'", "analysis": {"plan_text": "Delete on projects  (cost=0.14..8.17 rows=0 width=0)\n  ->  Index Scan using idx_projects_state on projects  (cost=0.14..8.17 rows=1 width=6)\n        Index Cond: (state = 'archived'::text)\n        Filter: (deleted_at < (CURRENT_TIMESTAMP - '1 year'::interval))\nPlanning Time: 0.083 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": 0, "planning_time": 0.565}}}, {"query": "SELECT\n  u.name AS user_name,\n  p.name AS project_name,\n  r.name AS role_name\nFROM public.users AS u\nJOIN public.projects AS p\n  ON u.project = p.project\nJOIN public.roles AS r\n  ON p.project = r.project\nWHERE\n  u.project = 'default'", "analysis": {"plan_text": "Nested Loop  (cost=0.43..24.51 rows=1 width=96)\n  ->  Nested Loop  (cost=0.29..16.34 rows=1 width=96)\n        ->  Index Scan using idx_users_project on users u  (cost=0.14..8.16 rows=1 width=64)\n              Index Cond: (project = 'default'::text)\n        ->  Index Scan using idx_projects_project on projects p  (cost=0.14..8.16 rows=1 width=64)\n              Index Cond: (project = 'default'::text)\n  ->  Index Scan using idx_roles_project on roles r  (cost=0.14..8.16 rows=1 width=64)\n        Index Cond: (project = 'default'::text)\nPlanning Time: 0.313 ms", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.451}}}, {"query": "SELECT\n  id,\n  name,\n  data ->> 'key' AS key_value\nFROM public.searchs\nWHERE\n  data @> '{\"key\": \"value\"}'", "analysis": {"plan_text": "Seq Scan on searchs  (cost=0.00..12.63 rows=2 width=96)\n  Filter: (data @> '{\"key\": \"value\"}'::jsonb)\nPlanning Time: 0.067 ms", "plan_results": {"estimated_scanned_rows": 2, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.215}}}, {"query": "CREATE INDEX idx_users_created_at ON public.users(created_at)", "analysis": {}}], "tables_analysis": [{"table": "searchs", "analysis": {"stats": {"table_size_bytes": 57344, "row_count": -1, "table_size_text": "57.3 kB"}}}, {"table": "users", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}, {"table": "projects", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}, {"table": "roles", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}]}
analysis={"database_analysis": {"name": "screen_be", "content": {"db_size_bytes": 12550947, "db_size_text": "12.6 MB"}}, "queries_analysis": [{"query": "ALTER TABLE public.searchs ADD COLUMN description TEXT", "analysis": {}}, {"query": "CREATE TABLE public.audit_logs (\n  id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,\n  action TEXT NOT NULL,\n  user_id TEXT NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  details JSONB\n)", "analysis": {}}, {"query": "UPDATE public.users SET state = 'inactive'\nWHERE\n  NOT deleted_at IS NULL", "analysis": {"plan_text": "Update on users  (cost=0.00..12.10 rows=0 width=0)\n  ->  Seq Scan on users  (cost=0.00..12.10 rows=209 width=38)\n        Filter: (deleted_at IS NOT NULL)\nPlanning Time: 0.063 ms", "plan_results": {"estimated_scanned_rows": 209, "estimated_created_rows": null, "estimated_updated_rows": 0, "estimated_deleted_rows": null, "planning_time": 0.259}}}, {"query": "DELETE FROM public.projects\nWHERE\n  state = 'archived' AND deleted_at < CURRENT_TIMESTAMP - INTERVAL '1 YEAR'", "analysis": {"plan_text": "Delete on projects  (cost=0.14..8.17 rows=0 width=0)\n  ->  Index Scan using idx_projects_state on projects  (cost=0.14..8.17 rows=1 width=6)\n        Index Cond: (state = 'archived'::text)\n        Filter: (deleted_at < (CURRENT_TIMESTAMP - '1 year'::interval))\nPlanning Time: 0.082 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": 0, "planning_time": 0.321}}}, {"query": "SELECT\n  u.name AS user_name,\n  p.name AS project_name,\n  r.name AS role_name\nFROM public.users AS u\nJOIN public.projects AS p\n  ON u.project = p.project\nJOIN public.roles AS r\n  ON p.project = r.project\nWHERE\n  u.project = 'default'", "analysis": {"plan_text": "Nested Loop  (cost=0.43..24.51 rows=1 width=96)\n  ->  Nested Loop  (cost=0.29..16.34 rows=1 width=96)\n        ->  Index Scan using idx_users_project on users u  (cost=0.14..8.16 rows=1 width=64)\n              Index Cond: (project = 'default'::text)\n        ->  Index Scan using idx_projects_project on projects p  (cost=0.14..8.16 rows=1 width=64)\n              Index Cond: (project = 'default'::text)\n  ->  Index Scan using idx_roles_project on roles r  (cost=0.14..8.16 rows=1 width=64)\n        Index Cond: (project = 'default'::text)\nPlanning Time: 0.162 ms", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.472}}}, {"query": "SELECT\n  id,\n  name,\n  data ->> 'key' AS key_value\nFROM public.searchs\nWHERE\n  data @> '{\"key\": \"value\"}'", "analysis": {"plan_text": "Seq Scan on searchs  (cost=0.00..12.63 rows=2 width=96)\n  Filter: (data @> '{\"key\": \"value\"}'::jsonb)\nPlanning Time: 0.072 ms", "plan_results": {"estimated_scanned_rows": 2, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.361}}}, {"query": "CREATE INDEX idx_users_created_at ON public.users(created_at)", "analysis": {}}], "tables_analysis": [{"table": "searchs", "analysis": {"stats": {"table_size_bytes": 57344, "row_count": -1, "table_size_text": "57.3 kB"}}}, {"table": "users", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}, {"table": "projects", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}, {"table": "roles", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": -1, "table_size_text": "114.7 kB"}}}]}
analysis={"database_analysis": {"name": "screen_be", "content": {"db_size_bytes": 12550947, "db_size_text": "12.6 MB"}}, "queries_analysis": [{"query": "ALTER TABLE public.searchs ADD COLUMN description TEXT", "analysis": {}}, {"query": "CREATE TABLE public.audit_logs (\n  id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,\n  action TEXT NOT NULL,\n  user_id TEXT NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  details JSONB\n)", "analysis": {}}, {"query": "UPDATE public.users SET state = 'inactive'\nWHERE\n  NOT deleted_at IS NULL", "analysis": {"plan_text": "Update on users  (cost=0.00..12.10 rows=0 width=0)\n  ->  Seq Scan on users  (cost=0.00..12.10 rows=209 width=38)\n        Filter: (deleted_at IS NOT NULL)\nPlanning Time: 0.062 ms", "plan_results": {"estimated_scanned_rows": 209, "estimated_created_rows": null, "estimated_updated_rows": 0, "estimated_deleted_rows": null, "planning_time": 0.262}}}, {"query": "DELETE FROM public.projects\nWHERE\n  state = 'archived' AND deleted_at < CURRENT_TIMESTAMP - INTERVAL '1 YEAR'", "analysis": {"plan_text": "Delete on projects  (cost=0.14..8.17 rows=0 width=0)\n  ->  Index Scan using idx_projects_state on projects  (cost=0.14..8.17 rows=1 width=6)\n        Index Cond: (state = 'archived'::text)\n        Filter: (deleted_at < (CURRENT_TIMESTAMP - '1 year'::interval))\nPlanning Time: 0.087 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": 0, "planning_time": 0.416}}}, {"query": "SELECT\n  u.name AS user_name,\n  p.name AS project_name,\n  r.name AS role_name\nFROM public.users AS u\nJOIN public.projects AS p\n  ON u.project = p.project\nJOIN public.roles AS r\n  ON p.project = r.project\nWHERE\n  u.project = 'default'", "analysis": {"plan_text": "Nested Loop  (cost=0.43..24.51 rows=1 width=96)\n  ->  Nested Loop  (cost=0.29..16.34 rows=1 width=96)\n        ->  Index Scan using idx_users_project on users u  (cost=0.14..8.16 rows=1 width=64)\n              Index Cond: (project = 'default'::text)\n        ->  Index Scan using idx_projects_project on projects p  (cost=0.14..8.16 rows=1 width=64)\n              Index Cond: (project = 'default'::text)\n  ->  Index Scan using idx_roles_project on roles r  (cost=0.14..8.16 rows=1 width=64)\n        Index Cond: (project = 'default'::text)\nPlanning Time: 0.193 ms", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.488}}}, {"query": "SELECT\n  id,\n  name,\n  data ->> 'key' AS key_value\nFROM public.searchs\nWHERE\n  data @> '{\"key\": \"value\"}'", "analysis": {"plan_text": "Seq Scan on searchs  (cost=0.00..0.00 rows=1 width=96)\n  Filter: (data @> '{\"key\": \"value\"}'::jsonb)\nPlanning Time: 0.104 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.207}}}, {"query": "CREATE INDEX idx_users_created_at ON public.users(created_at)", "analysis": {}}], "tables_analysis": [{"table": "searchs", "analysis": {"stats": {"table_size_bytes": 57344, "row_count": 0, "table_size_text": "57.3 kB"}}}, {"table": "users", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 2, "table_size_text": "114.7 kB"}}}, {"table": "projects", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 1, "table_size_text": "114.7 kB"}}}, {"table": "roles", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 2, "table_size_text": "114.7 kB"}}}]}
analysis={"database_analysis": {"name": "screen_be", "content": {"db_size_bytes": 12550947, "replication_enabled": false, "db_size_text": "12.6 MB"}}, "queries_analysis": [{"query": "ALTER TABLE public.searchs ADD COLUMN description TEXT", "analysis": {}}, {"query": "CREATE TABLE public.audit_logs (\n  id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,\n  action TEXT NOT NULL,\n  user_id TEXT NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  details JSONB\n)", "analysis": {}}, {"query": "UPDATE public.users SET state = 'inactive'\nWHERE\n  NOT deleted_at IS NULL", "analysis": {"plan_text": "Update on users  (cost=0.00..1.02 rows=0 width=0)\n  ->  Seq Scan on users  (cost=0.00..1.02 rows=2 width=38)\n        Filter: (deleted_at IS NOT NULL)\nPlanning Time: 0.063 ms", "plan_results": {"estimated_scanned_rows": 2, "estimated_created_rows": null, "estimated_updated_rows": 0, "estimated_deleted_rows": null, "planning_time": 0.232}}}, {"query": "DELETE FROM public.projects\nWHERE\n  state = 'archived' AND deleted_at < CURRENT_TIMESTAMP - INTERVAL '1 YEAR'", "analysis": {"plan_text": "Delete on projects  (cost=0.00..1.02 rows=0 width=0)\n  ->  Seq Scan on projects  (cost=0.00..1.02 rows=1 width=6)\n        Filter: ((state = 'archived'::text) AND (deleted_at < (CURRENT_TIMESTAMP - '1 year'::interval)))\nPlanning Time: 0.084 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": 0, "planning_time": 0.277}}}, {"query": "SELECT\n  u.name AS user_name,\n  p.name AS project_name,\n  r.name AS role_name\nFROM public.users AS u\nJOIN public.projects AS p\n  ON u.project = p.project\nJOIN public.roles AS r\n  ON p.project = r.project\nWHERE\n  u.project = 'default'", "analysis": {"plan_text": "Nested Loop  (cost=0.00..3.08 rows=1 width=96)\n  ->  Nested Loop  (cost=0.00..2.05 rows=1 width=96)\n        ->  Seq Scan on users u  (cost=0.00..1.02 rows=1 width=64)\n              Filter: (project = 'default'::text)\n        ->  Seq Scan on projects p  (cost=0.00..1.01 rows=1 width=64)\n              Filter: (project = 'default'::text)\n  ->  Seq Scan on roles r  (cost=0.00..1.02 rows=1 width=64)\n        Filter: (project = 'default'::text)\nPlanning Time: 0.216 ms", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.45}}}, {"query": "SELECT\n  id,\n  name,\n  data ->> 'key' AS key_value\nFROM public.searchs\nWHERE\n  data @> '{\"key\": \"value\"}'", "analysis": {"plan_text": "Seq Scan on searchs  (cost=0.00..0.00 rows=1 width=96)\n  Filter: (data @> '{\"key\": \"value\"}'::jsonb)\nPlanning Time: 0.066 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.21}}}, {"query": "CREATE INDEX idx_users_created_at ON public.users(created_at)", "analysis": {}}], "tables_analysis": [{"table": "searchs", "analysis": {"stats": {"table_size_bytes": 57344, "row_count": 0, "table_size_text": "57.3 kB"}}}, {"table": "users", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 2, "table_size_text": "114.7 kB"}}}, {"table": "projects", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 1, "table_size_text": "114.7 kB"}}}, {"table": "roles", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 2, "table_size_text": "114.7 kB"}}}]}
analysis={"database_analysis": {"name": "screen_be", "content": {"db_size_bytes": 12550947, "replication_enabled": false, "db_size_text": "12.6 MB"}}, "queries_analysis": [{"query": "ALTER TABLE public.searchs ADD COLUMN description TEXT", "analysis": {}}, {"query": "CREATE TABLE public.audit_logs (\n  id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,\n  action TEXT NOT NULL,\n  user_id TEXT NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  details JSONB\n)", "analysis": {}}, {"query": "UPDATE public.users SET state = 'inactive'\nWHERE\n  NOT deleted_at IS NULL", "analysis": {"plan_text": "Update on users  (cost=0.00..1.02 rows=0 width=0)\n  ->  Seq Scan on users  (cost=0.00..1.02 rows=2 width=38)\n        Filter: (deleted_at IS NOT NULL)\nPlanning Time: 0.060 ms", "plan_results": {"estimated_scanned_rows": 2, "estimated_created_rows": null, "estimated_updated_rows": 0, "estimated_deleted_rows": null, "planning_time": 0.272}}}, {"query": "DELETE FROM public.projects\nWHERE\n  state = 'archived' AND deleted_at < CURRENT_TIMESTAMP - INTERVAL '1 YEAR'", "analysis": {"plan_text": "Delete on projects  (cost=0.00..1.02 rows=0 width=0)\n  ->  Seq Scan on projects  (cost=0.00..1.02 rows=1 width=6)\n        Filter: ((state = 'archived'::text) AND (deleted_at < (CURRENT_TIMESTAMP - '1 year'::interval)))\nPlanning Time: 0.112 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": 0, "planning_time": 0.526}}}, {"query": "SELECT\n  u.name AS user_name,\n  p.name AS project_name,\n  r.name AS role_name\nFROM public.users AS u\nJOIN public.projects AS p\n  ON u.project = p.project\nJOIN public.roles AS r\n  ON p.project = r.project\nWHERE\n  u.project = 'default'", "analysis": {"plan_text": "Nested Loop  (cost=0.00..3.08 rows=1 width=96)\n  ->  Nested Loop  (cost=0.00..2.05 rows=1 width=96)\n        ->  Seq Scan on users u  (cost=0.00..1.02 rows=1 width=64)\n              Filter: (project = 'default'::text)\n        ->  Seq Scan on projects p  (cost=0.00..1.01 rows=1 width=64)\n              Filter: (project = 'default'::text)\n  ->  Seq Scan on roles r  (cost=0.00..1.02 rows=1 width=64)\n        Filter: (project = 'default'::text)\nPlanning Time: 0.156 ms", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.464}}}, {"query": "SELECT\n  id,\n  name,\n  data ->> 'key' AS key_value\nFROM public.searchs\nWHERE\n  data @> '{\"key\": \"value\"}'", "analysis": {"plan_text": "Seq Scan on searchs  (cost=0.00..0.00 rows=1 width=96)\n  Filter: (data @> '{\"key\": \"value\"}'::jsonb)\nPlanning Time: 0.623 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.293}}}, {"query": "CREATE INDEX idx_users_created_at ON public.users(created_at)", "analysis": {}}], "tables_analysis": [{"table": "searchs", "analysis": {"stats": {"table_size_bytes": 57344, "row_count": 0, "table_size_text": "57.3 kB"}}}, {"table": "users", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 2, "table_size_text": "114.7 kB"}}}, {"table": "projects", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 1, "table_size_text": "114.7 kB"}}}, {"table": "roles", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 2, "table_size_text": "114.7 kB"}}}]}
analysis={"database_analysis": {"name": "screen_be", "content": {"db_size_bytes": 12550947, "replication_enabled": false, "db_size_text": "12.6 MB"}}, "queries_analysis": [{"query": "ALTER TABLE public.searchs ADD COLUMN description TEXT", "analysis": {}}, {"query": "CREATE TABLE public.audit_logs (\n  id INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,\n  action TEXT NOT NULL,\n  user_id TEXT NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  details JSONB\n)", "analysis": {}}, {"query": "UPDATE public.users SET state = 'inactive'\nWHERE\n  NOT deleted_at IS NULL", "analysis": {"plan_text": "Update on users  (cost=0.00..1.02 rows=0 width=0)\n  ->  Seq Scan on users  (cost=0.00..1.02 rows=2 width=38)\n        Filter: (deleted_at IS NOT NULL)\nPlanning Time: 0.132 ms", "plan_results": {"estimated_scanned_rows": 2, "estimated_created_rows": null, "estimated_updated_rows": 0, "estimated_deleted_rows": null, "planning_time": 0.491}}}, {"query": "DELETE FROM public.projects\nWHERE\n  state = 'archived' AND deleted_at < CURRENT_TIMESTAMP - INTERVAL '1 YEAR'", "analysis": {"plan_text": "Delete on projects  (cost=0.00..1.02 rows=0 width=0)\n  ->  Seq Scan on projects  (cost=0.00..1.02 rows=1 width=6)\n        Filter: ((state = 'archived'::text) AND (deleted_at < (CURRENT_TIMESTAMP - '1 year'::interval)))\nPlanning Time: 0.121 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": 0, "planning_time": 0.28}}}, {"query": "SELECT\n  u.name AS user_name,\n  p.name AS project_name,\n  r.name AS role_name\nFROM public.users AS u\nJOIN public.projects AS p\n  ON u.project = p.project\nJOIN public.roles AS r\n  ON p.project = r.project\nWHERE\n  u.project = 'default'", "analysis": {"plan_text": "Nested Loop  (cost=0.00..3.08 rows=1 width=96)\n  ->  Nested Loop  (cost=0.00..2.05 rows=1 width=96)\n        ->  Seq Scan on users u  (cost=0.00..1.02 rows=1 width=64)\n              Filter: (project = 'default'::text)\n        ->  Seq Scan on projects p  (cost=0.00..1.01 rows=1 width=64)\n              Filter: (project = 'default'::text)\n  ->  Seq Scan on roles r  (cost=0.00..1.02 rows=1 width=64)\n        Filter: (project = 'default'::text)\nPlanning Time: 0.156 ms", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.487}}}, {"query": "SELECT\n  id,\n  name,\n  data ->> 'key' AS key_value\nFROM public.searchs\nWHERE\n  data @> '{\"key\": \"value\"}'", "analysis": {"plan_text": "Seq Scan on searchs  (cost=0.00..0.00 rows=1 width=96)\n  Filter: (data @> '{\"key\": \"value\"}'::jsonb)\nPlanning Time: 0.066 ms", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": 0.219}}}, {"query": "CREATE INDEX idx_users_created_at ON public.users(created_at)", "analysis": {}}], "tables_analysis": [{"table": "searchs", "analysis": {"stats": {"table_size_bytes": 57344, "row_count": 0, "table_size_text": "57.3 kB"}}}, {"table": "users", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 2, "table_size_text": "114.7 kB"}}}, {"table": "projects", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 1, "table_size_text": "114.7 kB"}}}, {"table": "roles", "analysis": {"stats": {"table_size_bytes": 114688, "row_count": 2, "table_size_text": "114.7 kB"}}}]}
analysis={"database_analysis": {"name": "CareemUtility", "content": {"db_size_bytes": 2449470404018, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.4 TB"}}, "queries_analysis": [{"query": "ALTER TABLE driver_guarantee\n    ADD COLUMN is_payout_rejected TINYINT DEFAULT NULL COMMENT '[NOT-PII]',\n    ADD INDEX idx_is_payout_rejected (is_payout_rejected)", "analysis": {}}], "tables_analysis": []}
analysis={"database_analysis": {"name": "CareemUtility", "content": {"db_size_bytes": 2449470404018, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.4 TB"}}, "queries_analysis": [{"query": "ALTER TABLE driver_guarantee\n    ADD COLUMN is_payout_rejected TINYINT DEFAULT NULL COMMENT '[NOT-PII]',\n    ADD INDEX idx_is_payout_rejected (is_payout_rejected)", "analysis": {}}], "tables_analysis": [{"table": "driver_guarantee", "analysis": {"stats": {"table_size_bytes": 216538218496, "row_count": 676592759, "table_size_text": "216.5 GB"}}}]}
analysis={"database_analysis": {"name": "CareemUtility", "content": {"db_size_bytes": 2453648143794, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "ALTER TABLE driver_guarantee\n    ADD COLUMN is_payout_rejected_2 TINYINT DEFAULT NULL COMMENT '[NOT-PII]',\n    ADD INDEX idx_is_payout_rejected_2 (is_payout_rejected)", "analysis": {}}], "tables_analysis": [{"table": "driver_guarantee", "analysis": {"stats": {"table_size_bytes": 216564432896, "row_count": 629626508, "table_size_text": "216.6 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": null, "name": "CareemUtility", "content": {"db_size_bytes": 2461748983218, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "ALTER TABLE driver_guarantee\n    ADD COLUMN is_payout_rejected TINYINT DEFAULT NULL COMMENT '[NOT-PII]',\n    ADD INDEX idx_is_payout_rejected (is_payout_rejected)", "analysis": {}}], "tables_analysis": [{"table": "driver_guarantee", "analysis": {"stats": {"table_size_bytes": 224471728128, "row_count": 617326421, "table_size_text": "224.5 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "careem-utility-new", "name": "CareemUtility", "content": {"db_size_bytes": 2461773100466, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "ALTER TABLE driver_guarantee\n    ADD COLUMN is_payout_rejected TINYINT DEFAULT NULL COMMENT '[NOT-PII]',\n    ADD INDEX idx_is_payout_rejected (is_payout_rejected)", "analysis": {}}], "tables_analysis": [{"table": "driver_guarantee", "analysis": {"stats": {"table_size_bytes": 224471728128, "row_count": 616119920, "table_size_text": "224.5 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "orders-new", "name": "orders", "content": {"db_size_bytes": 740512647168, "performance_schema": true, "replication_enabled": true, "db_size_text": "740.5 GB"}}, "queries_analysis": [{"query": "ALTER TABLE order_promotions_v2 ADD   INDEX order_promotions_v2_created_at_index (created_at)", "analysis": {}}], "tables_analysis": [{"table": "order_promotions_v2", "analysis": {"stats": {"table_size_bytes": 9989767168, "row_count": 41514280, "table_size_text": "10.0 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "orders-new", "name": "orders", "content": {"db_size_bytes": 740525230080, "performance_schema": true, "replication_enabled": true, "db_size_text": "740.5 GB"}}, "queries_analysis": [{"query": "ALTER TABLE order_promotions_v2 ADD   INDEX order_promotions_v2_created_at_index (created_at)", "analysis": {}}], "tables_analysis": [{"table": "order_promotions_v2", "analysis": {"stats": {"table_size_bytes": 9989767168, "row_count": 41570342, "table_size_text": "10.0 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "rides-platform", "name": "rides_platform", "content": {"db_size_bytes": 24100864, "performance_schema": true, "replication_enabled": false, "db_size_text": "24.1 MB"}}, "queries_analysis": [{"query": "ALTER TABLE ride DROP COLUMN activity_id", "analysis": {}}], "tables_analysis": [{"table": "ride", "analysis": {"stats": {"table_size_bytes": 3702784, "row_count": 4414, "table_size_text": "3.7 MB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "careem-utility-new", "name": "CaptainRewardsV2", "content": {"db_size_bytes": 2461775230386, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1745, 1750, 1755, 1760, 1765, 1770, 1746, 1751, 1756, 1761, 1766, 1771, 1747, 1752, 1757, 1762, 1767, 1772, 1748, 1753, 1758, 1763, 1768, 1773)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     24 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 24, "estimated_created_rows": null, "estimated_updated_rows": 24, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2099,2956,2877,2900,2958,2896,8,2955,2679,1,712,2957,2676,2741,3004,2880,2193,264,2945,3052,335,2,2685,3044,2878,3006,2755,2755,2872,3025,2840,2682,2895,2288,2928,2853'\nWHERE\n  id IN (1807, 1808, 1821)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      3 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": 3, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2944,2864,476,2928,2816,712,2338,3006,3033,3025,3019,3044,3045,3051,3052,3050,3049,3017,3004,2872,2853,2840,2755,2686,2685,2684,2682,2681,2680,2679,2117,3061,2755'\nWHERE\n  id IN (1822, 1823, 1832, 1857)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      4 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 4, "estimated_created_rows": null, "estimated_updated_rows": 4, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,3007,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1944,1283,1331,1110,1286,2899,338,1605'\nWHERE\n  id IN (1824, 1828, 1833, 1837, 1849, 1841, 1853, 1845, 1825, 1829, 1834, 1838, 1850, 1842, 1854, 1846, 1826, 1830, 1835, 1839, 1851, 1843, 1855, 1847, 1827, 1831, 1836, 1840, 1852, 1844, 1856, 1848)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     32 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 32, "estimated_created_rows": null, "estimated_updated_rows": 32, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1104, 1105, 1106, 1783, 1784, 1785, 1809, 1810, 1786, 1787, 1788, 1811, 1812, 1789, 1790, 1791, 1813, 1814, 1792, 1793, 1794, 1815, 1816, 1795, 1796, 1797, 1817, 1818, 1798, 1799, 1800, 1819, 1820)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     33 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 33, "estimated_created_rows": null, "estimated_updated_rows": 33, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,76,71,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,2679,1,70,712,77,2957,2676,2741,3004,2880,2193,264,2945,3007,3052,335,2,2685,3044,1342,2878,3006,2755,2755,2872'\nWHERE\n  id IN (1806, 1805, 1804, 1803, 1802, 1801)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      6 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 6, "estimated_created_rows": null, "estimated_updated_rows": 6, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "benefit_configuration", "analysis": {"stats": {"table_size_bytes": 475136, "row_count": 1020, "table_size_text": "475.1 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "careem-utility-new", "name": "CaptainRewardsV2", "content": {"db_size_bytes": 2461775230386, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1745, 1750, 1755, 1760, 1765, 1770, 1746, 1751, 1756, 1761, 1766, 1771, 1747, 1752, 1757, 1762, 1767, 1772, 1748, 1753, 1758, 1763, 1768, 1773)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     24 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 24, "estimated_created_rows": null, "estimated_updated_rows": 24, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2099,2956,2877,2900,2958,2896,8,2955,2679,1,712,2957,2676,2741,3004,2880,2193,264,2945,3052,335,2,2685,3044,2878,3006,2755,2755,2872,3025,2840,2682,2895,2288,2928,2853'\nWHERE\n  id IN (1807, 1808, 1821)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      3 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": 3, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2944,2864,476,2928,2816,712,2338,3006,3033,3025,3019,3044,3045,3051,3052,3050,3049,3017,3004,2872,2853,2840,2755,2686,2685,2684,2682,2681,2680,2679,2117,3061,2755'\nWHERE\n  id IN (1822, 1823, 1832, 1857)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      4 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 4, "estimated_created_rows": null, "estimated_updated_rows": 4, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,3007,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1944,1283,1331,1110,1286,2899,338,1605'\nWHERE\n  id IN (1824, 1828, 1833, 1837, 1849, 1841, 1853, 1845, 1825, 1829, 1834, 1838, 1850, 1842, 1854, 1846, 1826, 1830, 1835, 1839, 1851, 1843, 1855, 1847, 1827, 1831, 1836, 1840, 1852, 1844, 1856, 1848)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     32 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 32, "estimated_created_rows": null, "estimated_updated_rows": 32, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1104, 1105, 1106, 1783, 1784, 1785, 1809, 1810, 1786, 1787, 1788, 1811, 1812, 1789, 1790, 1791, 1813, 1814, 1792, 1793, 1794, 1815, 1816, 1795, 1796, 1797, 1817, 1818, 1798, 1799, 1800, 1819, 1820)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     33 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 33, "estimated_created_rows": null, "estimated_updated_rows": 33, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,76,71,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,2679,1,70,712,77,2957,2676,2741,3004,2880,2193,264,2945,3007,3052,335,2,2685,3044,1342,2878,3006,2755,2755,2872'\nWHERE\n  id IN (1806, 1805, 1804, 1803, 1802, 1801)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      6 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 6, "estimated_created_rows": null, "estimated_updated_rows": 6, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "benefit_configuration", "analysis": {"stats": {"table_size_bytes": 475136, "row_count": 1020, "table_size_text": "475.1 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "careem-utility-new", "name": "CaptainRewardsV2", "content": {"db_size_bytes": 2461775230386, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1745, 1750, 1755, 1760, 1765, 1770, 1746, 1751, 1756, 1761, 1766, 1771, 1747, 1752, 1757, 1762, 1767, 1772, 1748, 1753, 1758, 1763, 1768, 1773)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     24 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 24, "estimated_created_rows": null, "estimated_updated_rows": 24, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2099,2956,2877,2900,2958,2896,8,2955,2679,1,712,2957,2676,2741,3004,2880,2193,264,2945,3052,335,2,2685,3044,2878,3006,2755,2755,2872,3025,2840,2682,2895,2288,2928,2853'\nWHERE\n  id IN (1807, 1808, 1821)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      3 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": 3, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2944,2864,476,2928,2816,712,2338,3006,3033,3025,3019,3044,3045,3051,3052,3050,3049,3017,3004,2872,2853,2840,2755,2686,2685,2684,2682,2681,2680,2679,2117,3061,2755'\nWHERE\n  id IN (1822, 1823, 1832, 1857)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      4 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 4, "estimated_created_rows": null, "estimated_updated_rows": 4, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,3007,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1944,1283,1331,1110,1286,2899,338,1605'\nWHERE\n  id IN (1824, 1828, 1833, 1837, 1849, 1841, 1853, 1845, 1825, 1829, 1834, 1838, 1850, 1842, 1854, 1846, 1826, 1830, 1835, 1839, 1851, 1843, 1855, 1847, 1827, 1831, 1836, 1840, 1852, 1844, 1856, 1848)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     32 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 32, "estimated_created_rows": null, "estimated_updated_rows": 32, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1104, 1105, 1106, 1783, 1784, 1785, 1809, 1810, 1786, 1787, 1788, 1811, 1812, 1789, 1790, 1791, 1813, 1814, 1792, 1793, 1794, 1815, 1816, 1795, 1796, 1797, 1817, 1818, 1798, 1799, 1800, 1819, 1820)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     33 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 33, "estimated_created_rows": null, "estimated_updated_rows": 33, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,76,71,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,2679,1,70,712,77,2957,2676,2741,3004,2880,2193,264,2945,3007,3052,335,2,2685,3044,1342,2878,3006,2755,2755,2872'\nWHERE\n  id IN (1806, 1805, 1804, 1803, 1802, 1801)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      6 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 6, "estimated_created_rows": null, "estimated_updated_rows": 6, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "benefit_configuration", "analysis": {"stats": {"table_size_bytes": 475136, "row_count": 1020, "table_size_text": "475.1 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "careem-utility-new", "name": "CaptainRewardsV2", "content": {"db_size_bytes": 2461775230386, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1745, 1750, 1755, 1760, 1765, 1770, 1746, 1751, 1756, 1761, 1766, 1771, 1747, 1752, 1757, 1762, 1767, 1772, 1748, 1753, 1758, 1763, 1768, 1773)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     24 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 24, "estimated_created_rows": null, "estimated_updated_rows": 24, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2099,2956,2877,2900,2958,2896,8,2955,2679,1,712,2957,2676,2741,3004,2880,2193,264,2945,3052,335,2,2685,3044,2878,3006,2755,2755,2872,3025,2840,2682,2895,2288,2928,2853'\nWHERE\n  id IN (1807, 1808, 1821)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      3 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": 3, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2944,2864,476,2928,2816,712,2338,3006,3033,3025,3019,3044,3045,3051,3052,3050,3049,3017,3004,2872,2853,2840,2755,2686,2685,2684,2682,2681,2680,2679,2117,3061,2755'\nWHERE\n  id IN (1822, 1823, 1832, 1857)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      4 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 4, "estimated_created_rows": null, "estimated_updated_rows": 4, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,3007,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1944,1283,1331,1110,1286,2899,338,1605'\nWHERE\n  id IN (1824, 1828, 1833, 1837, 1849, 1841, 1853, 1845, 1825, 1829, 1834, 1838, 1850, 1842, 1854, 1846, 1826, 1830, 1835, 1839, 1851, 1843, 1855, 1847, 1827, 1831, 1836, 1840, 1852, 1844, 1856, 1848)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     32 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 32, "estimated_created_rows": null, "estimated_updated_rows": 32, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1104, 1105, 1106, 1783, 1784, 1785, 1809, 1810, 1786, 1787, 1788, 1811, 1812, 1789, 1790, 1791, 1813, 1814, 1792, 1793, 1794, 1815, 1816, 1795, 1796, 1797, 1817, 1818, 1798, 1799, 1800, 1819, 1820)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     33 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 33, "estimated_created_rows": null, "estimated_updated_rows": 33, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,76,71,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,2679,1,70,712,77,2957,2676,2741,3004,2880,2193,264,2945,3007,3052,335,2,2685,3044,1342,2878,3006,2755,2755,2872'\nWHERE\n  id IN (1806, 1805, 1804, 1803, 1802, 1801)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      6 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 6, "estimated_created_rows": null, "estimated_updated_rows": 6, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "benefit_configuration", "analysis": {"stats": {"table_size_bytes": 475136, "row_count": 1020, "table_size_text": "475.1 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "UPDATE content.currencies SET label_localized = JSON_SET(label_localized, '$.ar', '\u00d8\u00b1\u00d9\u008a\u00d8\u00a7\u00d9\u0084')\nWHERE\n  id = 2", "analysis": {"plan_text": "+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table      | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+============+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | currencies |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "currencies", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 14, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "\\nUPDATE content.currencies\\nSET label_localized = JSON_SET(label_localized, '$.ar', '\u0631\u064a\u0627\u0644')\\nWHERE id = 2;", "analysis": {}}, {"query": "\\n", "analysis": {}}], "tables_analysis": []}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "UPDATE content.currencies SET label_localized = JSON_SET(label_localized, '$.ar', '\u0631\u064a\u0627\u0644')\nWHERE\n  id = 2", "analysis": {"plan_text": "+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table      | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+============+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | currencies |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "currencies", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 14, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "UPDATE content.currencies SET label_localized = JSON_SET(label_localized, '$.ar', '\u0631\u064a\u0627\u0644')\nWHERE\n  id = 2", "analysis": {"plan_text": "+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table      | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+============+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | currencies |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "currencies", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 14, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "hdl-db", "name": "hdl", "content": {"db_size_bytes": 118675949568, "performance_schema": true, "replication_enabled": true, "db_size_text": "118.7 GB"}}, "queries_analysis": [{"query": "ALTER TABLE hdl_orders ADD COLUMN captain_id BIGINT UNSIGNED NULL COMMENT '[NOT-PII]'", "analysis": {}}, {"query": "ALTER TABLE hdl_orders ADD   INDEX idx_order_id (order_id)", "analysis": {}}, {"query": "ALTER TABLE hdl_merchants ADD   CONSTRAINT fk_hdl_id FOREIGN KEY (hdl_id) REFERENCES hdl_zone (\n    id\n  )", "analysis": {}}], "tables_analysis": [{"table": "hdl_orders", "analysis": {"stats": {"table_size_bytes": 65536, "row_count": 0, "table_size_text": "65.5 kB"}}}, {"table": "hdl_zone", "analysis": {"stats": {"table_size_bytes": 278528, "row_count": 182, "table_size_text": "278.5 kB"}}}, {"table": "hdl_merchants", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 0, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "careem-utility-new", "name": "CaptainRewardsV2", "content": {"db_size_bytes": 2461785765298, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1745, 1750, 1755, 1760, 1765, 1770, 1746, 1751, 1756, 1761, 1766, 1771, 1747, 1752, 1757, 1762, 1767, 1772, 1748, 1753, 1758, 1763, 1768, 1773)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     24 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 24, "estimated_created_rows": null, "estimated_updated_rows": 24, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2099,2956,2877,2900,2958,2896,8,2955,2679,1,712,2957,2676,2741,3004,2880,2193,264,2945,3052,335,2,2685,3044,2878,3006,2755,2755,2872,3025,2840,2682,2895,2288,2928,2853'\nWHERE\n  id IN (1807, 1808, 1821)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      3 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": 3, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2944,2864,476,2928,2816,712,2338,3006,3033,3025,3019,3044,3045,3051,3052,3050,3049,3017,3004,2872,2853,2840,2755,2686,2685,2684,2682,2681,2680,2679,2117,3061,2755'\nWHERE\n  id IN (1822, 1823, 1832, 1857)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      4 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 4, "estimated_created_rows": null, "estimated_updated_rows": 4, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,3007,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1944,1283,1331,1110,1286,2899,338,1605'\nWHERE\n  id IN (1824, 1828, 1833, 1837, 1849, 1841, 1853, 1845, 1825, 1829, 1834, 1838, 1850, 1842, 1854, 1846, 1826, 1830, 1835, 1839, 1851, 1843, 1855, 1847, 1827, 1831, 1836, 1840, 1852, 1844, 1856, 1848)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     32 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 32, "estimated_created_rows": null, "estimated_updated_rows": 32, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1104, 1105, 1106, 1783, 1784, 1785, 1809, 1810, 1786, 1787, 1788, 1811, 1812, 1789, 1790, 1791, 1813, 1814, 1792, 1793, 1794, 1815, 1816, 1795, 1796, 1797, 1817, 1818, 1798, 1799, 1800, 1819, 1820)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     33 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 33, "estimated_created_rows": null, "estimated_updated_rows": 33, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,76,71,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,2679,1,70,712,77,2957,2676,2741,3004,2880,2193,264,2945,3007,3052,335,2,2685,3044,1342,2878,3006,2755,2755,2872'\nWHERE\n  id IN (1806, 1805, 1804, 1803, 1802, 1801)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      6 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 6, "estimated_created_rows": null, "estimated_updated_rows": 6, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "benefit_configuration", "analysis": {"stats": {"table_size_bytes": 475136, "row_count": 1020, "table_size_text": "475.1 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "careem-utility-new", "name": "CaptainRewardsV2", "content": {"db_size_bytes": 2461785765298, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1745, 1750, 1755, 1760, 1765, 1770, 1746, 1751, 1756, 1761, 1766, 1771, 1747, 1752, 1757, 1762, 1767, 1772, 1748, 1753, 1758, 1763, 1768, 1773)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     24 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 24, "estimated_created_rows": null, "estimated_updated_rows": 24, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2099,2956,2877,2900,2958,2896,8,2955,2679,1,712,2957,2676,2741,3004,2880,2193,264,2945,3052,335,2,2685,3044,2878,3006,2755,2755,2872,3025,2840,2682,2895,2288,2928,2853'\nWHERE\n  id IN (1807, 1808, 1821)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      3 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": 3, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2944,2864,476,2928,2816,712,2338,3006,3033,3025,3019,3044,3045,3051,3052,3050,3049,3017,3004,2872,2853,2840,2755,2686,2685,2684,2682,2681,2680,2679,2117,3061,2755'\nWHERE\n  id IN (1822, 1823, 1832, 1857)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      4 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 4, "estimated_created_rows": null, "estimated_updated_rows": 4, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,3007,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1944,1283,1331,1110,1286,2899,338,1605'\nWHERE\n  id IN (1824, 1828, 1833, 1837, 1849, 1841, 1853, 1845, 1825, 1829, 1834, 1838, 1850, 1842, 1854, 1846, 1826, 1830, 1835, 1839, 1851, 1843, 1855, 1847, 1827, 1831, 1836, 1840, 1852, 1844, 1856, 1848)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     32 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 32, "estimated_created_rows": null, "estimated_updated_rows": 32, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1104, 1105, 1106, 1783, 1784, 1785, 1809, 1810, 1786, 1787, 1788, 1811, 1812, 1789, 1790, 1791, 1813, 1814, 1792, 1793, 1794, 1815, 1816, 1795, 1796, 1797, 1817, 1818, 1798, 1799, 1800, 1819, 1820)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     33 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 33, "estimated_created_rows": null, "estimated_updated_rows": 33, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,76,71,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,2679,1,70,712,77,2957,2676,2741,3004,2880,2193,264,2945,3007,3052,335,2,2685,3044,1342,2878,3006,2755,2755,2872'\nWHERE\n  id IN (1806, 1805, 1804, 1803, 1802, 1801)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      6 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 6, "estimated_created_rows": null, "estimated_updated_rows": 6, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "benefit_configuration", "analysis": {"stats": {"table_size_bytes": 475136, "row_count": 1020, "table_size_text": "475.1 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "careem-utility-new", "name": "CaptainRewardsV2", "content": {"db_size_bytes": 2461785765298, "performance_schema": true, "replication_enabled": true, "db_size_text": "2.5 TB"}}, "queries_analysis": [{"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1745, 1750, 1755, 1760, 1765, 1770, 1746, 1751, 1756, 1761, 1766, 1771, 1747, 1752, 1757, 1762, 1767, 1772, 1748, 1753, 1758, 1763, 1768, 1773)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     24 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 24, "estimated_created_rows": null, "estimated_updated_rows": 24, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2099,2956,2877,2900,2958,2896,8,2955,2679,1,712,2957,2676,2741,3004,2880,2193,264,2945,3052,335,2,2685,3044,2878,3006,2755,2755,2872,3025,2840,2682,2895,2288,2928,2853'\nWHERE\n  id IN (1807, 1808, 1821)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      3 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 3, "estimated_created_rows": null, "estimated_updated_rows": 3, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2944,2864,476,2928,2816,712,2338,3006,3033,3025,3019,3044,3045,3051,3052,3050,3049,3017,3004,2872,2853,2840,2755,2686,2685,2684,2682,2681,2680,2679,2117,3061,2755'\nWHERE\n  id IN (1822, 1823, 1832, 1857)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      4 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 4, "estimated_created_rows": null, "estimated_updated_rows": 4, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,3007,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1944,1283,1331,1110,1286,2899,338,1605'\nWHERE\n  id IN (1824, 1828, 1833, 1837, 1849, 1841, 1853, 1845, 1825, 1829, 1834, 1838, 1850, 1842, 1854, 1846, 1826, 1830, 1835, 1839, 1851, 1843, 1855, 1847, 1827, 1831, 1836, 1840, 1852, 1844, 1856, 1848)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     32 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 32, "estimated_created_rows": null, "estimated_updated_rows": 32, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2907,2901,2876,76,71,2740,2823,2777,2842,1882,2675,2879,1098,2099,2956,2960,2877,2900,2958,2896,8,2955,1,70,77,2957,2676,2741,2880,2193,264,2945,335,2,1342,2878,2895,2288,743,2677,2898,266,6,2785,1283,1331,1110,1286,2899,338,1605,2854,3,2946,2784'\nWHERE\n  id IN (1104, 1105, 1106, 1783, 1784, 1785, 1809, 1810, 1786, 1787, 1788, 1811, 1812, 1789, 1790, 1791, 1813, 1814, 1792, 1793, 1794, 1815, 1816, 1795, 1796, 1797, 1817, 1818, 1798, 1799, 1800, 1819, 1820)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |     33 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 33, "estimated_created_rows": null, "estimated_updated_rows": 33, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE CaptainRewardsV2.benefit_configuration SET context_id = '2822,2864,2907,2117,2901,3033,2876,76,71,2740,476,2823,2777,2842,2338,1882,2675,2879,1098,2857,2099,2956,2960,2877,2900,2958,2896,8,2955,2679,1,70,712,77,2957,2676,2741,3004,2880,2193,264,2945,3007,3052,335,2,2685,3044,1342,2878,3006,2755,2755,2872'\nWHERE\n  id IN (1806, 1805, 1804, 1803, 1802, 1801)", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=======================+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | benefit_configuration |              | range  | PRIMARY         | PRIMARY |         4 | const |      6 |        100 | Using where |\n+------+---------------+-----------------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 6, "estimated_created_rows": null, "estimated_updated_rows": 6, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "benefit_configuration", "analysis": {"stats": {"table_size_bytes": 475136, "row_count": 1020, "table_size_text": "475.1 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "rides-platform", "name": "rides_platform", "content": {"db_size_bytes": 24100864, "performance_schema": true, "replication_enabled": false, "db_size_text": "24.1 MB"}}, "queries_analysis": [{"query": "ALTER TABLE ride DROP COLUMN activity_id", "analysis": {}}], "tables_analysis": [{"table": "ride", "analysis": {"stats": {"table_size_bytes": 3702784, "row_count": 4617, "table_size_text": "3.7 MB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "orders-new", "name": "orders", "content": {"db_size_bytes": 746472605696, "performance_schema": true, "replication_enabled": true, "db_size_text": "746.5 GB"}}, "queries_analysis": [{"query": "ALTER TABLE order_promotions_v2 ADD   INDEX order_promotions_v2_created_at_index (created_at)", "analysis": {}}], "tables_analysis": [{"table": "order_promotions_v2", "analysis": {"stats": {"table_size_bytes": 10859020288, "row_count": 42086951, "table_size_text": "10.9 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "orders-new", "name": "orders", "content": {"db_size_bytes": 746612066304, "performance_schema": true, "replication_enabled": true, "db_size_text": "746.6 GB"}}, "queries_analysis": [{"query": "ALTER TABLE order_promotions_v2 ADD   INDEX order_promotions_v2_created_at_index (created_at)", "analysis": {}}], "tables_analysis": [{"table": "order_promotions_v2", "analysis": {"stats": {"table_size_bytes": 10859020288, "row_count": 42199633, "table_size_text": "10.9 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "UPDATE content.currencies SET label_localized = JSON_SET(label_localized, '$.ar', '\u0631\u064a\u0627\u0644')\nWHERE\n  id = 2", "analysis": {"plan_text": "+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table      | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+============+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | currencies |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "currencies", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 14, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "UPDATE content.currencies SET label_localized = JSON_SET(label_localized, '$.ar', '\u0631\u064a\u0627\u0644')\nWHERE\n  id = 2", "analysis": {"plan_text": "+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table      | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+============+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | currencies |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "currencies", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 14, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "UPDATE content.currencies SET label_localized = JSON_SET(label_localized, '$.ar', '\u0631\u064a\u0627\u0644')\nWHERE\n  id = 2", "analysis": {"plan_text": "+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table      | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+============+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | currencies |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "currencies", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 14, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "UPDATE content.currencies SET label_localized = JSON_SET(label_localized, '$.ar', '\u0631\u064a\u0627\u0644')\nWHERE\n  id = 2", "analysis": {"plan_text": "+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table      | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+============+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | currencies |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "currencies", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 14, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-content-master-db", "name": "content", "content": {"db_size_bytes": 14256128, "performance_schema": true, "replication_enabled": true, "db_size_text": "14.3 MB"}}, "queries_analysis": [{"query": "UPDATE content.currencies SET label_localized = JSON_SET(label_localized, '$.ar', '\u0631\u064a\u0627\u0644')\nWHERE\n  id = 2", "analysis": {"plan_text": "+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table      | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+============+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | currencies |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+------------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "currencies", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 14, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "prod-auth-master-db", "name": "auth", "content": {"db_size_bytes": 236225939456, "performance_schema": true, "replication_enabled": true, "db_size_text": "236.2 GB"}}, "queries_analysis": [{"query": "UPDATE users SET email = CONCAT('duplicate.', email)\nWHERE\n  id IN (22603239)", "analysis": {"plan_text": "+------+---------------+---------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table   | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=========+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | users   |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+---------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "UPDATE users SET careem_user_id = NULL\nWHERE\n  id IN (22603239)", "analysis": {"plan_text": "+------+---------------+---------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+\n|   id | select_type   | table   | partitions   | type   | possible_keys   | key     |   key_len | ref   |   rows |   filtered | Extra       |\n+======+===============+=========+==============+========+=================+=========+===========+=======+========+============+=============+\n|    1 | UPDATE        | users   |              | range  | PRIMARY         | PRIMARY |         4 | const |      1 |        100 | Using where |\n+------+---------------+---------+--------------+--------+-----------------+---------+-----------+-------+--------+------------+-------------+", "plan_results": {"estimated_scanned_rows": 1, "estimated_created_rows": null, "estimated_updated_rows": 1, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "users", "analysis": {"stats": {"table_size_bytes": ***********, "row_count": ********, "table_size_text": "34.2 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "captain-blocking-db", "name": "cbs", "content": {"db_size_bytes": **********, "performance_schema": true, "replication_enabled": false, "db_size_text": "3.7 GB"}}, "queries_analysis": [{"query": "INSERT INTO cbs.service_access_token (\n  access_token,\n  service_name,\n  enabled\n)\nVALUES\n  (\n    'JEM1tmbPlM2tkzH56yCUItazZMdx03gULk1IUQka',\n    'captain-account-deletion-service',\n    1\n  )", "analysis": {"plan_text": "+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_access_token |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user (\n  username,\n  service_access_token_id\n)\nVALUES\n  ('captain-account-deletion-service', 16)", "analysis": {"plan_text": "+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table        | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+==============+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+--------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 1)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}, {"query": "INSERT INTO cbs.service_user_status_update_permission (\n  service_user_id,\n  status_update_permission_id\n)\nVALUES\n  (16, 4)", "analysis": {"plan_text": "+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | service_user_status_update_permission |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+---------------------------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "service_access_token", "analysis": {"stats": {"table_size_bytes": 32768, "row_count": 16, "table_size_text": "32.8 kB"}}}, {"table": "service_user", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 16, "table_size_text": "49.2 kB"}}}, {"table": "service_user_status_update_permission", "analysis": {"stats": {"table_size_bytes": 49152, "row_count": 70, "table_size_text": "49.2 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "housekeeper", "name": "housekeeper", "content": {"db_size_bytes": 1850487267, "replication_enabled": false, "db_size_text": "1.9 GB"}}, "queries_analysis": [{"query": "CREATE INDEX CONCURRENTLY idx_order_events_status_updated_at_id ON order_events(status, updated_at, id)", "analysis": {}}], "tables_analysis": [{"table": "order_events", "analysis": {"stats": {"table_size_bytes": 1841807360, "row_count": 3455735, "table_size_text": "1.8 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "housekeeper", "name": "housekeeper", "content": {"db_size_bytes": 1850487267, "replication_enabled": false, "db_size_text": "1.9 GB"}}, "queries_analysis": [{"query": "CREATE INDEX CONCURRENTLY idx_order_events_status_updated_at_id ON order_events(status, updated_at, id)", "analysis": {}}], "tables_analysis": [{"table": "order_events", "analysis": {"stats": {"table_size_bytes": 1841807360, "row_count": 3454173, "table_size_text": "1.8 GB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "housekeeper", "name": "housekeeper", "content": {"db_size_bytes": 1850798563, "replication_enabled": false, "db_size_text": "1.9 GB"}}, "queries_analysis": [{"query": "CREATE INDEX CONCURRENTLY idx_order_events_status_updated_at_id ON order_events(status, updated_at, id)", "analysis": {}}], "tables_analysis": []}
analysis={"database_analysis": {"rds_instance_name": "unified-profile", "name": "profiles", "content": {"db_size_bytes": 399380840448, "performance_schema": true, "replication_enabled": false, "db_size_text": "399.4 GB"}}, "queries_analysis": [{"query": "ALTER TABLE profiles.`identity`\n\tADD INDEX created_at_idx (`created_at`),\n\tADD INDEX updated_at_idx (`updated_at`)", "analysis": {}}, {"query": "ALTER TABLE profiles.`external_identifier` ADD   INDEX created_at_idx (`created_at`)", "analysis": {}}, {"query": "ALTER TABLE profiles.`credentials` ADD   INDEX created_at_idx (`created_at`)", "analysis": {}}, {"query": "ALTER TABLE profiles.`identity_device`\n\tADD INDEX created_at_idx (`created_at`),\n\tADD INDEX updated_at_idx (`updated_at`)", "analysis": {}}, {"query": "ALTER TABLE profiles.`attribute` ADD   INDEX created_at_idx (`created_at`)", "analysis": {}}, {"query": "ALTER TABLE profiles.`identity_outbox` ADD   INDEX updated_at_idx (`updated_at`)", "analysis": {}}], "tables_analysis": [{"table": "identity", "analysis": {"stats": {"table_size_bytes": 48731750400, "row_count": 77750555, "table_size_text": "48.7 GB"}}}, {"table": "external_identifier", "analysis": {"stats": {"table_size_bytes": 43075289088, "row_count": 95085614, "table_size_text": "43.1 GB"}}}, {"table": "credentials", "analysis": {"stats": {"table_size_bytes": 26336772096, "row_count": 72523852, "table_size_text": "26.3 GB"}}}, {"table": "identity_device", "analysis": {"stats": {"table_size_bytes": 16053551104, "row_count": 26357616, "table_size_text": "16.1 GB"}}}, {"table": "attribute", "analysis": {"stats": {"table_size_bytes": 20024967168, "row_count": 38370120, "table_size_text": "20.0 GB"}}}, {"table": "identity_outbox", "analysis": {"stats": {"table_size_bytes": 678395904, "row_count": 2426516, "table_size_text": "678.4 MB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "partner-billing", "name": "partner_billing", "content": {"db_size_bytes": 78514356224, "performance_schema": true, "replication_enabled": false, "db_size_text": "78.5 GB"}}, "queries_analysis": [{"query": "ALTER TABLE billing_entry DROP INDEX idx_transaction_date_tenant_be,   ALGORITHM=INPLACE,\n  LOCK=NONE", "analysis": {}}, {"query": "ALTER TABLE billing_entry ADD   INDEX idx_tenant_trx_date_bill_type_bill_id (tenant, transaction_date, billable_type, billable_id),   ALGORITHM=INPLACE,\n  LOCK=NONE", "analysis": {}}, {"query": "INSERT INTO flyway_schema_history (\n  installed_rank,\n  version,\n  description,\n  type,\n  script,\n  checksum,\n  installed_by,\n  installed_on,\n  execution_time,\n  success\n)\nVALUES\n  (\n    32,\n    '32',\n    'drop index create new index billing entry',\n    'SQL',\n    'V32__drop_index_create_new_index_billing_entry.sql',\n    48392993,\n    'srv_partner_billing_rw',\n    '2025-04-21 18:34:38',\n    371,\n    1\n  )", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | flyway_schema_history |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+-----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "billing_entry", "analysis": {"stats": {"table_size_bytes": 74421338112, "row_count": 34863590, "table_size_text": "74.4 GB"}}}, {"table": "flyway_schema_history", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 31, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "partner-billing", "name": "partner_billing", "content": {"db_size_bytes": 78516453376, "performance_schema": true, "replication_enabled": false, "db_size_text": "78.5 GB"}}, "queries_analysis": [{"query": "ALTER TABLE billing_entry DROP INDEX idx_transaction_date_tenant_be,   ALGORITHM=INPLACE,\n  LOCK=NONE", "analysis": {}}, {"query": "ALTER TABLE billing_entry ADD   INDEX idx_tenant_trx_date_bill_type_bill_id (tenant, transaction_date, billable_type, billable_id),   ALGORITHM=INPLACE,\n  LOCK=NONE", "analysis": {}}, {"query": "INSERT INTO flyway_schema_history (\n  installed_rank,\n  version,\n  description,\n  type,\n  script,\n  checksum,\n  installed_by,\n  installed_on,\n  execution_time,\n  success\n)\nVALUES\n  (\n    32,\n    '32',\n    'drop index create new index billing entry',\n    'SQL',\n    'V32__drop_index_create_new_index_billing_entry.sql',\n    48392993,\n    'srv_partner_billing_rw',\n    '2025-04-21 18:34:38',\n    371,\n    1\n  )", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | flyway_schema_history |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+-----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "billing_entry", "analysis": {"stats": {"table_size_bytes": 74421338112, "row_count": 36179253, "table_size_text": "74.4 GB"}}}, {"table": "flyway_schema_history", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 31, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "partner-billing", "name": "partner_billing", "content": {"db_size_bytes": 78516453376, "performance_schema": true, "replication_enabled": false, "db_size_text": "78.5 GB"}}, "queries_analysis": [{"query": "ALTER TABLE billing_entry DROP INDEX idx_transaction_date_tenant_be,   ALGORITHM=INPLACE,\n  LOCK=NONE", "analysis": {}}, {"query": "ALTER TABLE billing_entry ADD   INDEX idx_tenant_trx_date_bill_type_bill_id (tenant, transaction_date, billable_type, billable_id),   ALGORITHM=INPLACE,\n  LOCK=NONE", "analysis": {}}, {"query": "INSERT INTO flyway_schema_history (\n  installed_rank,\n  version,\n  description,\n  type,\n  script,\n  checksum,\n  installed_by,\n  installed_on,\n  execution_time,\n  success\n)\nVALUES\n  (\n    32,\n    '32',\n    'drop index create new index billing entry',\n    'SQL',\n    'V32__drop_index_create_new_index_billing_entry.sql',\n    48392993,\n    'srv_partner_billing_rw',\n    '2025-04-21 18:34:38',\n    371,\n    1\n  )", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | flyway_schema_history |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+-----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "billing_entry", "analysis": {"stats": {"table_size_bytes": 74422386688, "row_count": 33383898, "table_size_text": "74.4 GB"}}}, {"table": "flyway_schema_history", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 31, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "partner-billing", "name": "partner_billing", "content": {"db_size_bytes": 78517501952, "performance_schema": true, "replication_enabled": false, "db_size_text": "78.5 GB"}}, "queries_analysis": [{"query": "ALTER TABLE billing_entry DROP INDEX idx_transaction_date_tenant_be,   ALGORITHM=INPLACE,\n  LOCK=NONE", "analysis": {}}, {"query": "ALTER TABLE billing_entry ADD   INDEX idx_tenant_trx_date_bill_type_bill_id (tenant, transaction_date, billable_type, billable_id),   ALGORITHM=INPLACE,\n  LOCK=NONE", "analysis": {}}, {"query": "INSERT INTO flyway_schema_history (\n  installed_rank,\n  version,\n  description,\n  type,\n  script,\n  checksum,\n  installed_by,\n  installed_on,\n  execution_time,\n  success\n)\nVALUES\n  (\n    32,\n    '32',\n    'drop index create new index billing entry',\n    'SQL',\n    'V32__drop_index_create_new_index_billing_entry.sql',\n    48392993,\n    'srv_partner_billing_rw',\n    '2025-04-21 18:34:38',\n    371,\n    1\n  )", "analysis": {"plan_text": "+------+---------------+-----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+\n|   id | select_type   | table                 | partitions   | type   | possible_keys   | key   | key_len   | ref   | rows   | filtered   | Extra   |\n+======+===============+=======================+==============+========+=================+=======+===========+=======+========+============+=========+\n|    1 | INSERT        | flyway_schema_history |              | ALL    |                 |       |           |       |        |            |         |\n+------+---------------+-----------------------+--------------+--------+-----------------+-------+-----------+-------+--------+------------+---------+", "plan_results": {"estimated_scanned_rows": 0, "estimated_created_rows": 0, "estimated_updated_rows": null, "estimated_deleted_rows": null, "planning_time": null}}}], "tables_analysis": [{"table": "billing_entry", "analysis": {"stats": {"table_size_bytes": 74422386688, "row_count": 35028724, "table_size_text": "74.4 GB"}}}, {"table": "flyway_schema_history", "analysis": {"stats": {"table_size_bytes": 16384, "row_count": 31, "table_size_text": "16.4 kB"}}}]}
analysis={"database_analysis": {"rds_instance_name": "lp-planner", "name": "prod_lp_planner", "content": {"db_size_bytes": 13186662400, "performance_schema": true, "replication_enabled": true, "db_size_text": "13.2 GB"}}, "queries_analysis": [{"query": "CREATE INDEX idx_plan_product_city_status_scheduled_time ON plan(product_id, city_id, status, scheduled_time)", "analysis": {}}], "tables_analysis": []}
analysis={"database_analysis": {"rds_instance_name": "lp-planner", "name": "prod_lp_planner", "content": {"db_size_bytes": 13186662400, "performance_schema": true, "replication_enabled": true, "db_size_text": "13.2 GB"}}, "queries_analysis": [{"query": "CREATE INDEX idx_plan_product_city_status_scheduled_time ON plan(product_id, city_id, status, scheduled_time)", "analysis": {}}], "tables_analysis": []}
analysis={"database_analysis": {"rds_instance_name": "lp-planner", "name": "prod_lp_planner", "content": {"db_size_bytes": 13186662400, "performance_schema": true, "replication_enabled": true, "db_size_text": "13.2 GB"}}, "queries_analysis": [{"query": "CREATE INDEX idx_plan_product_city_status_scheduled_time ON plan(product_id, city_id, status, scheduled_time)", "analysis": {}}], "tables_analysis": []}
analysis={"database_analysis": {"rds_instance_name": "lp-planner", "name": "prod_lp_planner", "content": {"db_size_bytes": 13186662400, "performance_schema": true, "replication_enabled": true, "db_size_text": "13.2 GB"}}, "queries_analysis": [{"query": "CREATE INDEX idx_plan_product_city_status_scheduled_time ON plan(product_id, city_id, status, scheduled_time)", "analysis": {}}], "tables_analysis": []}
analysis={"database_analysis": {"rds_instance_name": "lp-planner", "name": "prod_lp_planner", "content": {"db_size_bytes": 13186662400, "performance_schema": true, "replication_enabled": true, "db_size_text": "13.2 GB"}}, "queries_analysis": [{"query": "CREATE INDEX idx_plan_product_city_status_scheduled_time ON plan(product_id, city_id, status, scheduled_time)", "analysis": {}}], "tables_analysis": []}
analysis={"database_analysis": {"rds_instance_name": "lp-planner", "name": "dev_lp_planner", "content": {"db_size_bytes": 944603136, "performance_schema": false, "replication_enabled": false, "db_size_text": "944.6 MB"}}, "queries_analysis": [{"query": "ALTER TABLE `plan` ADD INDEX idx_plan_product_city_status ON plan(product_id, city_id, status)", "analysis": {}}], "tables_analysis": [{"table": "plan", "analysis": {"stats": {"table_size_bytes": 188563456, "row_count": 869341, "table_size_text": "188.6 MB"}}}]}
