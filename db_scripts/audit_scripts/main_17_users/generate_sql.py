import mysql.connector

# Database connection details
config = {
    'user': 'user',
    'password': 'pass',
    'host': 'host',  # e.g., 'localhost'
    'database': 'mysql'  # We are querying the mysql system database
}

# SQL query to execute
sql_query = "SELECT CONCAT( 'SHOW GRANTS FOR ''', User , '''@''', Host, ''';') FROM mysql.`user`;"

# Output file
output_file = "users.sql"

try:
    # Establish the connection
    conn = mysql.connector.connect(**config)
    # Create a cursor object
    cursor = conn.cursor()
    # Execute the SQL query
    cursor.execute(sql_query)
    # Fetch all the results
    results = cursor.fetchall()
    # Open the output file in write mode
    with open(output_file, 'w') as file:
        # Iterate over the results and write to the file
        for row in results:
            file.write(f"{row[0]}\n")
    print(f"Results successfully written to {output_file}")
except mysql.connector.Error as err:
    print(f"Error: {err}")