from dataclasses import dataclass
from functools import cached_property
from typing import List, Dict

from .db_parameter_group_dto import DBParameterGroupDTO
from .endpoint import Endpoint


@dataclass
class RDSInstanceDTO:
    raw: Dict

    @cached_property
    def instance_identifier(self) -> str:
        return self.raw['DBInstanceIdentifier']

    @cached_property
    def status(self) -> str:
        return self.raw['DBInstanceStatus']

    @cached_property
    def parameter_groups(self) -> List[DBParameterGroupDTO]:
        return [DBParameterGroupDTO(raw=pg) for pg in self.raw['DBParameterGroups']]

    @cached_property
    def endpoint(self) -> Endpoint:
        return Endpoint(**self.raw['Endpoint'])

    @cached_property
    def engine(self) -> str:
        return self.raw['Engine']

    @cached_property
    def engine_version(self) -> str:
        return self.raw['EngineVersion']

    @cached_property
    def instance_class(self) -> str:
        return self.raw['DBInstanceClass']

    @cached_property
    def instance_arn(self) -> str:
        return self.raw['DBInstanceArn']

    @cached_property
    def read_replica_source(self) -> str:
        return self.raw.get('ReadReplicaSourceDBInstanceIdentifier')

    @cached_property
    def pg_family(self) -> str:
        engine = self.engine
        engine_version = self.engine_version
        major_version = engine_version.split('.')[0]
        if engine == 'mysql' and not engine_version.endswith('.0'):
            return f"{engine}{major_version}.0"
        return f"{engine}{major_version}"
