import logging
import sys

import yaml
from pathlib2 import Path


class Config:
    aws_profiles = None
    dynamodb_table_name = None
    secret_name = None

    def load_configs(self, config_file="configurations.yaml"):
        logging.info(f"Fetching Configurations from {config_file}")

        entry_point_path = Path(sys.argv[0]).resolve().parent
        config_path = entry_point_path / config_file

        logging.info(f"Loading configuration from {config_path}")

        try:
            with open(config_path, 'r') as f:
                configurations = yaml.load(f, Loader=yaml.FullLoader)
                Config.dynamodb_table_name = configurations.get("dynamodb_table_name")
                Config.secret_name = configurations.get("secret_name")
                Config.aws_profiles = configurations.get("aws_profiles", [])
                logging.info(f"Loaded {len(Config.aws_profiles)} profiles from configuration")
        except Exception as e:
            logging.error(f"Failed to load configuration file {config_path}: {e}")
            raise
