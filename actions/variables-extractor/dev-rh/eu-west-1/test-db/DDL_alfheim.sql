--endpoint = test-db-pg.cgevunlz3bfz.eu-west-1.rds.amazonaws.com
--database=screen_be


ALTER TABLE public.searchs
    ADD COLUMN description text;


CREATE TABLE public.audit_logs
(
    id        serial PRIMARY KEY,
    action    text NOT NULL,
    user_id   text NOT NULL,
    timestamp timestamp with time zone DEFAULT now(),
    details   jsonb
);


UPDATE public.users
SET state = 'inactive'
WHERE deleted_at IS NOT NULL;


DELETE
FROM public.projects
WHERE state = 'archived'
  AND deleted_at < now() - interval '1 year';


SELECT u.name AS user_name, p.name AS project_name, r.name AS role_name
FROM public.users u
         JOIN public.projects p ON u.project = p.project
         JOIN public.roles r ON p.project = r.project
WHERE u.project = 'default';


SELECT id, name, data ->> 'key' AS key_value
FROM public.searchs
WHERE data @> '{"key": "value"}';


CREATE INDEX idx_users_created_at
    ON public.users (created_at);


