import argparse
import json
import os


class ArgumentParser:
    def __init__(self):
        self.parser = argparse.ArgumentParser(description='Analyzer CLI')
        self.parser.add_argument('--env', default=os.environ.get('ENV', ''), help='Environment name')

        self.parser.add_argument('--sql-content', default=os.environ.get('SQL_CONTENT'), help='SQL content to analyze')
        self.parser.add_argument('--engine', default=os.environ.get('ENGINE'), help='Database engine')
        self.parser.add_argument('--database', default=os.environ.get('DATABASE'), help='Database name')
        self.parser.add_argument('--instance', default=os.environ.get('INSTANCE'), help='Instance name')
        self.parser.add_argument('--rds-host', default=os.environ.get('RDS_HOST'), help='RDS host')
        self.parser.add_argument('--username', default=os.environ.get('USERNAME'), help='Database username')
        self.parser.add_argument('--password', default=os.environ.get('PASSWORD'), help='Database password')
        self.parser.add_argument('--schema', default=os.environ.get('SCHEMA'), help='Database schema')
        self.parser.add_argument('--schemas', default=json.loads(os.environ.get('SCHEMAS', '[]')),
                                 help='Database schemas')

    def parse_args(self):
        return self.parser.parse_args()
