import concurrent.futures
import datetime
from datetime import time
import itertools
import pickle
import time
import requests
import logging
from ec2_gateway import EC2Metadata
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


VIVIDCORTEX_AUTH = "Bearer 0Twdh9jh2HSTxZjRzKFLKDNAd16o2Hau"
VIVIDCORTEX_QUERY_LINK = "https://careem.app.vividcortex.com/Default/queries/"
QUERY_SAMPLES_API = "https://app.vividcortex.com/api/v2/queries/samples"
QUERY_METRICS_API = "https://app.vividcortex.com/api/v2/metrics/series"
QUERY_DATA_API = "https://app.vividcortex.com/api/v2/queries"
QUERY_HOST_API = "https://app.vividcortex.com/api/v2/hosts"

EXCLUDE_QUERY_LIST = ['explain', 'show full fields','show']

HOST_MAPPER = {
    37: 'main-db.careem-engineering.com',
    158: 'main-read-db.careem-engineering.com',
    159: 'microservices-read-db.careem-engineering.com',
    160: 'solutions-db.careem-engineering.com',
    214: 'bi-db.careem-engineering.com',
    161: 'supply-read-db.careem-engineering.com',
    171: 'location-read-db.careem-engineering.com',
    123: 'bi-db.careem-engineering.com',
    196: 'workbench-db.careem-engineering.com'
}


class QueryStats:
    def __init__(self):
        self.id = ""
        self.date_time = ""
        self.database_name = ""
        self.table_name = ""
        self.origin_ip = ""
        self.link = ""
        self.digest = ""
        self.count = ""
        self.row_fetch = ""
        self.row_examined = ""
        self.environment_name = ""
        self.team_name = ""
        self.user_name = ""
        self.timestamp = ""
        self.is_truncated = ""


def construct_vividcortex_link(query_id):
    return VIVIDCORTEX_QUERY_LINK + query_id


def convert_to_unix_timestamp_from(date):
    return time.mktime(date.timetuple())


def convert_to_base10_from(base16_id):
    return int(base16_id, 16)


def get_samples_from(content_id):
    params = (
        ('query', content_id),
    )
    query_samples = get_vividcortex_content_against(QUERY_SAMPLES_API, params)
    return query_samples


def get_start_and_end_unix_time_by_delta(days):
    start_date = datetime.datetime.combine(datetime.date.today() - datetime.timedelta(days=days),
                                           datetime.datetime.now().time())
    end_date = datetime.datetime.combine(datetime.date.today(), datetime.datetime.now().time())
    start_unix_time = str(convert_to_unix_timestamp_from(start_date)).split('.')[0]
    end_unix_time = str(convert_to_unix_timestamp_from(end_date)).split('.')[0]
    return start_unix_time, end_unix_time


def get_vividcortex_content_against(api_url, custom_params):
    headers = {
        'Authorization': VIVIDCORTEX_AUTH,
        'Accept': 'application/json',
        'X-Indent': 'true',
    }

    params = get_vividcortex_api_params()
    params += custom_params
    response = requests.get(api_url, headers=headers, params=params,verify=False) #,verify=False
    if response.status_code != 200:
        return get_vividcortex_content_against(api_url, custom_params)
    return response.json()


def get_vividcortex_api_params():
    start_unix_time, end_unix_time = get_start_and_end_unix_time_by_delta(4)
    
    params = (
        ('from', '1685923200'), #start_unix_time),
        ('until', '1686009599'), #end_unix_time),
        ('limit', 20000), 
        ('offset', 0),
        ('host','37') #'123,37,158,159,196')
    )
    return params


def get_all_queries_data_against(table_name):
    queries_data = []
    for query_filter in create_filter_for_vividcortex_query_against(table_name):
        params = (
            ('filter', query_filter),
        )
        response_data = get_vividcortex_content_against(QUERY_DATA_API, params)
        if response_data:
            queries_data.append(response_data['data'])

    return list(itertools.chain(*queries_data))


def get_query_count_against(query_id, host_id):
    params = (
        ('metrics', F"host.queries.q.{query_id}.tput"),
        ('host', host_id),
    )
    query_count = get_vividcortex_content_against(QUERY_METRICS_API, params)
    if query_count:
        query_count_series = query_count.get('data')[0].get('elements')[0].get('series')
        return sum(query_count_series)


def get_query_row_fetch_count_against(query_id, host_id):
    params = (
        ('metrics', F"host.queries.q.{query_id}.rows_sent.tput"),
        ('host', host_id),
    )
    query_row_fetch_count = get_vividcortex_content_against(QUERY_METRICS_API, params)
    if query_row_fetch_count:
        query_count_series = query_row_fetch_count.get('data')[0].get('elements')[0].get('series')
        return sum(query_count_series)


def get_query_row_examined_count_against(query_id, host_id):
    params = (
        ('metrics', F"host.queries.q.{query_id}.rows_examined.tput"),
        ('host', host_id),
    )
    query_row_fetch_count = get_vividcortex_content_against(QUERY_METRICS_API, params)
    if query_row_fetch_count:
        query_count_series = query_row_fetch_count.get('data')[0].get('elements')[0].get('series')
        return sum(query_count_series)


def get_query_count_against_without_samples(query_id):
    params = (
        ('metrics', F"host.queries.q.{query_id}.tput"),
    )
    query_count = get_vividcortex_content_against(QUERY_METRICS_API, params)
    if query_count:
        query_count_series = query_count.get('data')[0].get('elements')[0].get('series')
        return sum(query_count_series)


def get_query_row_fetch_count_against_without_samples(query_id):
    params = (
        ('metrics', F"host.queries.q.{query_id}.rows_sent.tput"),
    )
    query_row_fetch_count = get_vividcortex_content_against(QUERY_METRICS_API, params)
    if query_row_fetch_count:
        query_count_series = query_row_fetch_count.get('data')[0].get('elements')[0].get('series')
        return sum(query_count_series)


def get_query_row_examined_count_against_without_samples(query_id):
    params = (
        ('metrics', F"host.queries.q.{query_id}.rows_examined.tput"),
    )
    query_row_fetch_count = get_vividcortex_content_against(QUERY_METRICS_API, params)
    if query_row_fetch_count:
        query_count_series = query_row_fetch_count.get('data')[0].get('elements')[0].get('series')
        return sum(query_count_series)


def process_query_stats_in_parallel_against(configuration_table):
    logging.info(f"Fetch query stats for {configuration_table}")
    queries = get_all_queries_data_against(configuration_table)

    vividcortex_query_stats = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
        futures = {executor.submit(process_query_stats_against, data, configuration_table) for data in queries}
        for future in concurrent.futures.as_completed(futures):
            data = future.result()
            vividcortex_stats_result = pickle.loads(data)
            if vividcortex_stats_result:
                vividcortex_query_stats.append(vividcortex_stats_result)
    return pickle.dumps(list(itertools.chain(*vividcortex_query_stats)))


def get_queries_stats_in_parallel_against(configuration_tables):
    vividcortex_stats = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
        futures = {executor.submit(process_query_stats_in_parallel_against, table) for table in configuration_tables}
    for future in concurrent.futures.as_completed(futures):
        data = future.result()
        vividcortex_stats_result = pickle.loads(data)
        if vividcortex_stats_result:
            vividcortex_stats.append(vividcortex_stats_result)

    return list(itertools.chain(*vividcortex_stats))


def get_query_stats_tuple_list_against(query_stats):
    query_stats_tuple_list = []
    for stats in query_stats:
        query_stats_tuple_list.append(
            (stats.date_time, stats.database_name, stats.table_name, stats.id, stats.digest, stats.link,
             stats.count, stats.row_fetch, stats.row_examined, stats.user_name, stats.environment_name, stats.origin_ip, stats.team_name))
    return query_stats_tuple_list


def create_filter_for_vividcortex_query_against(table_name):
    filter_for_query = [
        f"insert into `{table_name}`",
        f"update `{table_name}`",
        f"delete `{table_name}`",
        f"from `{table_name}`",
        f"from {table_name}",
        f"join `{table_name}`",
        f"join {table_name}"
    ]

    return filter_for_query


def process_query_stats_against(query, configuration_table):
    logging.info(f"Process DATA {query}")
    query_stats = []

    query_digest = query.get('digest').lower()
    if not query_digest.startswith(tuple(EXCLUDE_QUERY_LIST)):
        base_10_id = convert_to_base10_from(query['id'])
        query_samples = get_samples_from(base_10_id)
        vividcortex_query_stats, all_origin_ip = process_query_samples_against(configuration_table, query,
                                                                               query_samples)
        query_stats.append(get_query_stats_with_service_names_against(all_origin_ip,
                                                                      vividcortex_query_stats))

    return pickle.dumps(list(itertools.chain(*query_stats)))


def process_query_samples_against(configuration_table, query, query_samples):
    vividcortex_query_stats = []
    all_origin_ip = []
    if query_samples:
        for samples in query_samples:
            if samples.get('host') in HOST_MAPPER:
                if samples.get('origin') not in all_origin_ip:
                    all_origin_ip.append(samples.get('origin'))
                    query_stats = create_query_stats_object(configuration_table, query, samples)
                    vividcortex_query_stats.append(query_stats)
    else:
        query_stats = create_query_stats_object_without_samples(configuration_table, query)
        vividcortex_query_stats.append(query_stats)

    return vividcortex_query_stats, all_origin_ip


def get_query_stats_with_service_names_against(all_origin_ip, vividcortex_query_stats):
    ec2metadata = EC2Metadata()
    ec2_stats = ec2metadata.fetch_ec2_instances_against(all_origin_ip)
    for object_id, query_stats in enumerate(vividcortex_query_stats):
        for stats in ec2_stats:
            if stats.ip == query_stats.origin_ip and stats.launch_time < query_stats.timestamp:
                query_stats.team_name = stats.team_name
                query_stats.environment_name = stats.environment_name
                vividcortex_query_stats[object_id] = query_stats
                break
    return vividcortex_query_stats


def create_query_stats_object(configuration_table, query, samples):
    query_stats = QueryStats()
    query_stats.timestamp = datetime.datetime.utcfromtimestamp(samples.get('ts')).strftime(
        '%Y-%m-%d %H:%M:%S')
    query_stats.digest = query.get('digest')
    query_stats.origin_ip = samples.get('origin')
    query_stats.user_name = samples.get('user')
    query_stats.id = query['id']
    query_stats.count = int(get_query_count_against(query['id'], samples.get('host')))
    query_stats.row_fetch = int(get_query_row_fetch_count_against(query['id'], samples.get('host')))
    query_stats.row_examined = int(get_query_row_examined_count_against(query['id'], samples.get('host')))
    query_stats.table_name = configuration_table
    #query_stats.date_time = datetime.datetime.now().date()
    query_stats.date_time = datetime.datetime.utcfromtimestamp(samples.get('ts')).strftime('%Y-%m-%d 00:00:00')
    query_stats.link = construct_vividcortex_link(query['id'])
    query_stats.database_name = HOST_MAPPER.get(samples.get('host')).split('.')[0]
    return query_stats


def create_query_stats_object_without_samples(configuration_table, query):
    query_stats = QueryStats()
    query_stats.digest = query.get('digest')
    query_stats.id = query['id']
    query_stats.count = int(get_query_count_against_without_samples(query['id']))
    query_stats.row_fetch = int(get_query_row_fetch_count_against_without_samples(query['id']))
    query_stats.row_examined = int(get_query_row_examined_count_against_without_samples(query['id']))
    query_stats.table_name = configuration_table
    query_stats.date_time = datetime.datetime.now().date()
    query_stats.link = construct_vividcortex_link(query['id'])
    return query_stats
