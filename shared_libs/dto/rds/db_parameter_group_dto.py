from dataclasses import dataclass, field
from functools import cached_property


@dataclass
class DBParameterGroupDTO:
    raw: dict = field(default_factory=dict)

    @cached_property
    def name(self) -> str:
        return self.raw['DBParameterGroupName']

    @cached_property
    def status(self) -> str:
        return self.raw['ParameterApplyStatus']

    @cached_property
    def family(self) -> str:
        return self.raw['DBParameterGroupFamily']
