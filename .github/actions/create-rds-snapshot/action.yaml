name: create RDS snapshot - Execute
description: create RDS snapshot - Execute

inputs:
  source_account_identifier:
    description: Which is the source account from which we want to copy
    required: true
  rds_identifier:
    description: Name of the RDS from which we wnat to take snapshot
    required: true

runs:
  using: composite
  steps:
    - name: Run python script
      shell: bash
      working-directory: ${{ github.action_path }}
      id: execute
      run: ./run-python-script.sh ${{ inputs.source_account_identifier }}  ${{ inputs.rds_identifier }}
      env:
        source_account_identifier: ${{ inputs.source_account_identifier }}
        rds_identifier: ${{ inputs.rds_identifier }}
