import re
from typing import List, Dict

from ..lib.database_task import DatabaseTask


class PKLimits(DatabaseTask):
    """
    Task to find Primary Keys that are approaching their limit in MySQL and PostgreSQL.
    """

    sheet_name = "PK Limits"
    execution_scope = "cluster"

    THRESHOLD = 0.80

    @classmethod
    def columns(cls) -> List[str]:
        """
        Defines the columns that will be returned.
        """
        return [
            "Database", "Schema", "Table", "Column",
            "Data Type", "Next Value", "Max Value", "Remaining"
        ]

    # Function to normalize MySQL numeric column types
    # This removes the display width (e.g. "tinyint(1)" -> "tinyint")
    @staticmethod
    def normalize_mysql_type(column_type):
        """
        Normalizes MySQL numeric column types, removing display width.
        """
        match = re.match(r"(\w+)(?:\(\d+\))?( unsigned)?", column_type.lower())
        if match:
            base_type = match.group(1)
            unsigned = match.group(2)
            return f"{base_type} unsigned" if unsigned else base_type
        return column_type.lower()

    def should_execute(self) -> bool:
        """
        Determines whether the task should execute. Runs for MySQL and PostgreSQL instances.
        """
        return self.db_info.get("engine") in {"mysql", "postgres"}

    def fetch_data(self) -> List[Dict[str, str]]:
        """
        Queries the database to retrieve Primary Keys approaching limits.
        """
        engine = self.db_info.get("engine")
        if engine not in {"mysql", "postgres"}:
            self.logger.info(f"Skipping unsupported instance: {self.db_info['instance_identifier']}")
            return []

        self.logger.info(f"Fetching PK limits from {engine} instance: {self.db_info['instance_identifier']}")

        if engine == "mysql":
            query = """
                SELECT
                    c.TABLE_SCHEMA AS database_name,
                    c.TABLE_SCHEMA AS schema_name,
                    c.TABLE_NAME AS table_name,
                    c.COLUMN_NAME AS column_name,
                    c.COLUMN_TYPE AS data_type,
                    t.AUTO_INCREMENT AS next_value
                FROM information_schema.COLUMNS c
                         JOIN information_schema.TABLES t
                              ON c.TABLE_SCHEMA = t.TABLE_SCHEMA
                                  AND c.TABLE_NAME = t.TABLE_NAME
                WHERE c.TABLE_SCHEMA NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
                  AND c.DATA_TYPE IN ('tinyint', 'smallint', 'mediumint', 'int', 'bigint')
                  AND c.COLUMN_KEY = 'PRI'
                  AND (
                          SELECT COUNT(*)
                          FROM information_schema.KEY_COLUMN_USAGE k
                          WHERE k.TABLE_SCHEMA = c.TABLE_SCHEMA
                            AND k.TABLE_NAME = c.TABLE_NAME
                            AND k.CONSTRAINT_NAME = 'PRIMARY'
                      ) = 1;  -- Ensures that the table has only one column as the primary key"""
        else:  # PostgreSQL
            query = """
                SELECT
                    c.table_catalog AS "database_name",
                    c.table_schema AS "schema_name",
                    c.table_name AS "table_name",
                    c.column_name AS "column_name",
                    c.data_type AS "data_type",
                    pg_sequences.last_value AS "next_value"
                FROM information_schema.columns c
                JOIN information_schema.key_column_usage kcu
                    ON c.table_name = kcu.table_name
                    AND c.column_name = kcu.column_name
                    AND c.table_schema = kcu.table_schema
                JOIN information_schema.table_constraints tc
                    ON kcu.constraint_name = tc.constraint_name
                    AND kcu.table_schema = tc.table_schema
                LEFT JOIN pg_sequences
                    ON pg_sequences.schemaname = c.table_schema
                    AND pg_sequences.sequencename = c.column_name || '_seq'
                WHERE c.data_type IN ('smallint', 'integer', 'bigint')
                  AND c.table_schema NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
                  AND c.table_schema NOT LIKE 'pg_temp_%'
                  AND c.table_schema NOT LIKE 'pg_toast_temp_%'
                  AND tc.constraint_type = 'PRIMARY KEY';
            """

        result = self._query(query)

        # Check if there was an error with the query
        if "error" in result:
            self.logger.error(f"Error fetching PK limits: {result['error']}")
            raise Exception(f"Database query failed: {result['error']}")

        if not result or not result.get("rows"):
            return []

        columns = result.get("columns", [])
        rows = result.get("rows", [])
        processed_data = [dict(zip(columns, row)) for row in rows]
        data = []

        tags = self.get_instance_tags()

        int_limits = {
            "mysql": {
                "tinyint": 127,
                "tinyint unsigned": 255,
                "smallint": 32767,
                "smallint unsigned": 65535,
                "mediumint": 8388607,
                "mediumint unsigned": 16777215,
                "int": 2147483647,
                "int unsigned": 4294967295,
                "bigint": 2 ** 63 - 1,
                "bigint unsigned": 2 ** 64 - 1
            },
            "postgres": {
                "smallint": 32767,
                "integer": 2147483647,
                "bigint": 2 ** 63 - 1
            }
        }

        for row in processed_data:
            data_type = self.normalize_mysql_type(row["data_type"].lower())
            max_value = int_limits.get(engine, {}).get(data_type)
            next_value = row.get("next_value")

            if next_value is None or max_value is None:
                self.logger.warning(f"Skipping row due to missing values: {row}")
                continue

            remaining = max_value - next_value if max_value and next_value is not None else None
            if remaining is None or next_value < int(max_value * self.THRESHOLD):
                continue

            data.append({
                "Database": row["database_name"],
                "Schema": row["schema_name"],
                "Table": row["table_name"],
                "Column": row["column_name"],
                "Data Type": data_type,
                "Next Value": next_value,
                "Max Value": max_value,
                "Remaining": remaining
            })

        return data
