import argparse
import requests
import os

def get_input(name):
    return os.getenv(name)

def get_branch_name(octokit, owner, repo):
    target_branch = get_input('TARGET_BRANCH')
    if target_branch:
        return target_branch

    pull_request = os.getenv('GITHUB_CONTEXT')
    if pull_request:
        return pull_request['payload']['pull_request']['head']['ref']

    return get_default_branch(octokit, owner, repo)

def get_default_branch(octokit, owner, repo):
    response = octokit.get(f'https://api.github.com/repos/{owner}/{repo}')
    response.raise_for_status()
    return response.json()['default_branch']

def re_run_workflow(octokit, owner, repo, run_id):
    response = octokit.post(f'https://api.github.com/repos/{owner}/{repo}/actions/runs/{run_id}/rerun')
    response.raise_for_status()

def main():
    parser = argparse.ArgumentParser(description='GitHub Actions Re-run Workflows')
    parser.add_argument('--check-names', required=True, help='Comma-separated list of check names')
    parser.add_argument('--github-token', required=True, help='GitHub token')
    args = parser.parse_args()

    check_names = args.check_names.split(', ')
    token = args.github_token
    headers = {'Authorization': f'token {token}'}
    octokit = requests.Session()
    octokit.headers.update(headers)

    owner = os.getenv('GITHUB_REPOSITORY_OWNER')
    repo = os.getenv('GITHUB_REPOSITORY_NAME')

    branch = get_branch_name(octokit, owner, repo)
    print(f'Branch: {branch}')

    response = octokit.get(f'https://api.github.com/repos/{owner}/{repo}/commits/{branch}/check-runs')
    response.raise_for_status()
    checks_result = response.json()

    for check_name in check_names:
        check_run = next((check_run for check_run in checks_result['check_runs'] if check_run['name'].strip() == check_name.strip()), None)

        if check_run:
            workflow_id = check_run['details_url'].split('/')[-3]

            try:
                re_run_workflow(octokit, owner, repo, workflow_id)
                print(f'"{check_name}" workflow has been triggered again.')

            except requests.exceptions.HTTPError as error:
                if 'This workflow is already running' in str(error):
                    print(f'Warning: "{check_name}" workflow is already running.')
                else:
                    raise error
        else:
            print(f'"{check_name}" check not found.')

if __name__ == '__main__':
    main()