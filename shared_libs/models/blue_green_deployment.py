import logging
import time
from functools import cached_property

import boto3

from .rds_instance import RDSInstance
from ..dto.rds import RDSInstanceDTO
from ..dto.rds.bluegreen import BlueGreenDeploymentDTO
from ..lib.aws.helper import aws_command_handler


class BlueGreenDeployment:
    def __init__(self, dto: BlueGreenDeploymentDTO):
        self.id = dto.id
        self.dto: BlueGreenDeploymentDTO = dto

    @classmethod
    def by_name(cls, deployment_name: str):
        def describe_deployment():
            return boto3.client('rds').describe_blue_green_deployments(
                Filters=[{
                    'Name': 'blue-green-deployment-name',
                    'Values': [deployment_name]
                }]
            )

        response = aws_command_handler(describe_deployment)
        info = response['BlueGreenDeployments'][0]
        dto = BlueGreenDeploymentDTO(deployment_info=info)
        return cls(dto)

    @classmethod
    def by_dto(cls, dto: BlueGreenDeploymentDTO):
        return cls(dto)

    @classmethod
    def by_dict(cls, deployment_dict: dict):
        return cls(BlueGreenDeploymentDTO(deployment_info=deployment_dict))

    @classmethod
    def find_or_create(cls, name: str, source: RDSInstanceDTO, param_group: str = None):
        rds_client = boto3.client('rds')

        def create_or_copy_deployment():
            params = {'BlueGreenDeploymentName': name, 'Source': source.instance_arn}
            if param_group:
                params['TargetDBParameterGroupName'] = param_group
            return rds_client.create_blue_green_deployment(**params)

        try:
            data = aws_command_handler(create_or_copy_deployment)
            logging.info(f"Created Blue/Green deployment: {name}")
            return cls.by_dict(data['BlueGreenDeployment'])
        except rds_client.exceptions.BlueGreenDeploymentAlreadyExistsFault:
            logging.info(f"Blue/Green deployment {name} already exists. Retrieving existing deployment.")
            return BlueGreenDeployment.by_name(name)

    @classmethod
    def by_id(cls, id):
        def describe_deployment():
            return boto3.client('rds').describe_blue_green_deployments(
                Filters=[{
                    'Name': 'blue-green-deployment-identifier',
                    'Values': [id]
                }]
            )

        data = aws_command_handler(describe_deployment)
        return cls(BlueGreenDeploymentDTO(deployment_info=data['BlueGreenDeployments'][0]))

    @cached_property
    def target_rds(self) -> RDSInstance:
        return RDSInstance.by_id(self.dto.target)

    @cached_property
    def source_rds(self) -> RDSInstance:
        return RDSInstance.by_id(self.dto.source)

    def update_info(self):
        def describe_deployment():
            return boto3.client('rds').describe_blue_green_deployments(
                Filters=[{
                    'Name': 'blue-green-deployment-identifier',
                    'Values': [self.id]
                }]
            )

        data = aws_command_handler(describe_deployment)
        self.dto = BlueGreenDeploymentDTO(deployment_info=data['BlueGreenDeployments'][0])

    def wait_for_availability(self):
        logging.info(f"Waiting Blue/Green deployment '{self.id}' to become available...")
        while True:
            self.update_info()
            if self.dto.status == 'PROVISIONING' or self.dto.status == 'SWITCHOVER_IN_PROGRESS':
                time.sleep(5)
                continue
            elif self.dto.status == 'AVAILABLE':
                logging.info(f"Blue/Green deployment {self.id} is now available.")
                break
            elif self.dto.status == 'SWITCHOVER_COMPLETED':
                logging.info(f"Blue/Green deployment {self.id} is completed.")
                break
            elif self.dto.status == 'FAILED':
                raise Exception(f"Blue/Green deployment {self.id} failed.")
            else:
                raise Exception(f"Unrecognized status '{self.dto.status}'")

    def switchover(self):
        def switchover_deployment():
            return boto3.client('rds').switchover_blue_green_deployment(
                BlueGreenDeploymentIdentifier=self.id
            )

        result = aws_command_handler(switchover_deployment)
        deployment = BlueGreenDeployment.by_dict(result['BlueGreenDeployment'])
        deployment.wait_for_availability()
        return deployment

    def delete(self):
        def delete_deployment():
            return boto3.client('rds').delete_blue_green_deployment(
                BlueGreenDeploymentIdentifier=self.id
            )

        aws_command_handler(delete_deployment)
        logging.info(f"Deleted Blue/Green deployment {self.id}.")
