import datetime
import time

import boto3
from db_scripts.ri_scripts import DatabaseConnection
from db_scripts.ri_scripts import rds_instance

client = boto3.client('ce')
db = DatabaseConnection()


def add_db_records(rds_instances):
    for rds_instance in rds_instances:
        query = F"INSERT INTO `rds_ri_utilization`(last_updated, reservation_id, instance_count, instance_type, db_engine, utilisation) VALUES ('{rds_instance.timestamp}', '{rds_instance.reservation_name}', '{rds_instance.instance_count}', '{rds_instance.instance_type}', '{rds_instance.engine}', '{rds_instance.utilisation}') ON DUPLICATE KEY UPDATE utilisation='{rds_instance.utilisation}', last_updated='{rds_instance.timestamp}';"
        with db.get_connection() as conn:
            conn.execute(query)


def extract_rds_ri_info(item):
    now = time.strftime('%Y-%m-%d')
    reservation_name = item['Attributes']['leaseId']
    instance_count = item['Attributes']['numberOfInstances']
    instance_type = item['Attributes']['instanceType']
    engine = item['Attributes']['platform']
    utilisation = item['Utilization']['UtilizationPercentage']
    return rds_instance.RDSInstance(engine, instance_count, instance_type, now, reservation_name, utilisation)


def get_week_date_info():
    today = datetime.date.today()
    time_start = str(today - datetime.timedelta(days=8))
    time_end = str(today - datetime.timedelta(days=1))
    return time_end, time_start


def main():
    response = get_rds_ri_utilization()
    rds_instances = get_rds_instances(response)
    add_db_records(rds_instances)


def get_rds_instances(response):
    rds_instances = []
    for item in response['UtilizationsByTime'][0]['Groups']:
        rds_instance = extract_rds_ri_info(item)
        rds_instances.append(rds_instance)
    return rds_instances


def get_rds_ri_utilization():
    time_end, time_start = get_week_date_info()
    return client.get_reservation_utilization(
        TimePeriod={
            'Start': time_start,
            'End': time_end
        },
        Filter={

            "And": [
                {
                    'Dimensions': {
                        'Key': 'SERVICE',
                        'Values': [
                            'Amazon Relational Database Service',
                        ],

                    }
                },
                {
                    'Dimensions': {
                        'Key': 'REGION',
                        'Values': [
                            'eu-west-1',
                        ],

                    }
                },

            ]
        },
        GroupBy=[
            {
                "Type": "DIMENSION",
                "Key": "SUBSCRIPTION_ID"
            }
        ]
    )


if __name__ == "__main__":
    main()
