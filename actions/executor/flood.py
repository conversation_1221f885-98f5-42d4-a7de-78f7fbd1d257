import time
import logging
import pymysql
from pymysql import MySQLError
from concurrent.futures import ThreadPoolExecutor
import random

# Configuration variables from info object
info = {
    'database': 'payout-info',
    'endpoint': 'test-db.cgevunlz3bfz.eu-west-1.rds.amazonaws.com',
    'engine': 'mysql',
    'instance': 'test-db',
    'password': 'r7EP6giP',
    'rds_host': 'test-db.cgevunlz3bfz.eu-west-1.rds.amazonaws.com',
    'schema': 'payout-info',
    'schemas': ['cash_control', 'cash_in_transaction_log', 'invoices', 'maxwell', 'payment_transaction', 'payout-info', 'payouts', 'test'],
    'username': 'careemuser'
}

DB_HOST = info['rds_host']
DB_USER = info['username']
DB_PASSWORD = info['password']
DB_NAME = info['database']
DELAY = 1  # Delay between queries in seconds
NUM_THREADS = 1000  # Number of concurrent threads

def create_connection():
    """Create a database connection and select the schema."""
    try:
        connection = pymysql.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        with connection.cursor() as cursor:
            cursor.execute(f"USE `{info['schema']}`")
        logging.info("Connected to the database and schema selected")
        return connection
    except MySQLError as e:
        logging.error(f"Error while connecting to MySQL: {e}")
        return None

def generate_random_query():
    """Generate a random query with random filters and conditions."""

#     return """
# SELECT SLEEP(10);
#     """

    conditions = [
        "pr.payment_amount > 1000",
        "pr.payment_currency = 'USD'",
        "b.status = 1",
        "po.currency = 'USD'",
        "po.country = 'US'",
        "pr.payment_date > '2023-01-01'",
        "pr.payment_date < '2023-12-31'"
    ]
    selected_conditions = random.sample(conditions, k=random.randint(1, len(conditions)))
    where_clause = " AND ".join(selected_conditions)

    query = f"""
    SELECT
        pr.id AS payout_request_id,
        pr.payment_amount,
        pr.payment_currency,
        pr.country,
        pm.id AS payment_method_id,
        pm.payout_option_id,
        po.name AS payout_option_name,
        po.type AS payout_option_type,
        po.currency AS payout_option_currency,
        po.country AS payout_option_country,
        b.name AS bank_name,
        b.display_name AS bank_display_name,
        b.code AS bank_code,
        b.currency AS bank_currency,
        b.country AS bank_country,
        (
            SELECT COUNT(*)
            FROM payout_request pr2
            WHERE pr2.country = pr.country
        ) AS request_count_per_country,
        (
            SELECT AVG(payment_amount)
            FROM payout_request pr3
            WHERE pr3.payout_option_id = po.id
        ) AS avg_payment_amount_per_option
    FROM
        payout_request pr
    JOIN
        payment_method pm ON pr.payout_option_id = pm.payout_option_id
    JOIN
        payout_option po ON pm.payout_option_id = po.id
    JOIN
        bank b ON po.country = b.country
    WHERE
        {where_clause}
    ORDER BY
        pr.payment_date DESC;
    """
    return query

def execute_query():
    """Execute a query on the database."""
    connection = create_connection()
    if connection is None:
        return

    query = generate_random_query()
    try:
        with connection.cursor() as cursor:
            cursor.execute(query)
            cursor.fetchall()  # Fetch all results to ensure the query is executed
            logging.info("Query executed successfully")
    except MySQLError as e:
        logging.error(f"Error executing query: {e}")
    finally:
        connection.close()

def main():
    """Main function to flood the database with queries."""
    with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
        try:
            while True:
                futures = [executor.submit(execute_query) for _ in range(NUM_THREADS)]
                for future in futures:
                    future.result()  # Wait for all threads to complete
                time.sleep(DELAY)
        except KeyboardInterrupt:
            logging.info("Script interrupted by user. Exiting.")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()