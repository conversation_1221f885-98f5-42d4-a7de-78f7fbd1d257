import logging
import os

from .lib import ArgumentParser
from .lib.functions import *
from .shared.configs import Config
from .shared.lib import github


def main():
    Config.configure_logging()
    arg_parser = ArgumentParser()
    args = arg_parser.parse_args()

    logging.debug(f"Pattern: {args.path_pattern}")
    logging.debug(f"Workspace: {args.workspace}")
    logging.debug(f"Input File: {args.input_file}")

    if not args.input_file:
        raise ValueError("No input file provided")

    logging.info(f"Reading input file: {args.input_file}")
    variables, sql_content = extract_variables_from_sql('/'.join([args.workspace, args.input_file]))
    logging.debug(f"Extracted Variables: {variables}")
    logging.debug(f"SQL Content: {sql_content}")

    path_variables = extract_variables_from_path(args.input_file, args.path_pattern)
    logging.debug(f"Path Variables: {path_variables}")
    variables.update(path_variables)

    escaped_sql_content = escape_multiline_content(sql_content)
    logging.debug(f"Escaped SQL Content: {escaped_sql_content}")

    output_file = os.getenv('GITHUB_OUTPUT')
    if not output_file:
        raise ValueError("No output file provided")

    logging.debug(f"Variables: {variables}")

    for key, value in variables.items():
        github.add_output(key, value)

    github.add_output('sql_content', escaped_sql_content)

    logging.info("Process completed successfully")


if __name__ == "__main__":
    main()
