RDS_INSTANCE_NAME := $(rds_instance_name)
REDIS_INSTANCE_NAME := $(redis_instance_name)
PROFILE := $(profile)

encrypt-rds-requirements:
	# TODO: should not be needed, updating git chmod index should be enough to fix it
	chmod +x db_scripts/database_encryption/*
	pip3 install -r db_scripts/database_encryption/ops/requirements.txt

encrypt-rds-with-external-replication-prod-rh: encrypt-rds-requirements
	python3 db_scripts/database_encryption/main_with_external_replication.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="prod"

encrypt-rds-without-external-replication-prod-rh: encrypt-rds-requirements
	python3 db_scripts/database_encryption/main_without_external_replication.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="prod"

encrypt-rds-with-external-replication-dev-rh: encrypt-rds-requirements
	python3 db_scripts/database_encryption/main_with_external_replication.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="qa"

encrypt-rds-without-external-replication-dev-rh: encrypt-rds-requirements
	python3 db_scripts/database_encryption/main_without_external_replication.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="qa"

encrypt-rds-without-external-replication-staging-mot: encrypt-rds-requirements
	python3 db_scripts/database_encryption/main_without_external_replication.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="cnqa"

encrypt-rds-with-external-replication-prod-mot: encrypt-rds-requirements
	python3 db_scripts/database_encryption/main_with_external_replication.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="cnprod"

encrypt-postgres-rds-with-external-replication-prod-mot: encrypt-rds-requirements
	python3 db_scripts/database_encryption/postgres_with_dms.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="cnprod"

encrypt-postgres-rds-with-external-replication-prod-rh: encrypt-rds-requirements
	python3 db_scripts/database_encryption/postgres_with_dms.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="prod"

encrypt-postgres-rds-with-external-replication-dev-rh: encrypt-rds-requirements
	python3 db_scripts/database_encryption/postgres_with_dms.py --db_identifier=$(RDS_INSTANCE_NAME) --profile="qa"

deploy-alerts:
	cd db_scripts/cloudwatch-alarm/cloudwatch-alarm-assigner-storage-v2 && \
	pip3 install -r requirements.txt && \
	python3 cloudwatch-alarm-assigner-storage.py --db_identifier=$(RDS_INSTANCE_NAME) --profile=$(PROFILE)

deploy-all-redis-alarms:
	cd db_scripts/cloudwatch-alarm/cloudwatch-alarm-redis-assigner && \
	pip3 install -r requirements.txt && \
	python3 main-all.py

deploy-redis-alarm:
	cd db_scripts/cloudwatch-alarm/cloudwatch-alarm-redis-assigner && \
	pip3 install -r requirements.txt && \
	python3 main-one.py --redis-identifier=$(REDIS_INSTANCE_NAME) --profile=$(PROFILE)
