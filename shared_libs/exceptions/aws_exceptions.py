"""
AWS Exceptions Module
This module contains custom exceptions for handling AWS-related errors.
"""

# -----------------------------------------------------------
# Base Custom AWS Exception
# -----------------------------------------------------------

class AwsBaseException(Exception):
    """Base exception for AWS-related errors."""
    def __init__(self, message, aws_response=None):
        super().__init__(message)
        self.aws_response = aws_response

# -----------------------------------------------------------
# Request Limit Exceeded Exception
# -----------------------------------------------------------

class AwsRequestLimitExceeded(AwsBaseException):
    """Exception raised when the AWS request limit is exceeded."""
    pass

# -----------------------------------------------------------
# DB Parameter Group Exceptions
# -----------------------------------------------------------

class AwsDBParameterGroupAlreadyExists(AwsBaseException):
    """Exception raised when the DB parameter group already exists."""
    pass

class AwsDBParameterGroupNotFound(AwsBaseException):
    """Exception raised when the DB parameter group is not found."""
    pass

# -----------------------------------------------------------
# DB Instance Exceptions
# -----------------------------------------------------------

class AwsDBInstanceNotFound(AwsBaseException):
    """Exception raised when the DB instance is not found."""
    pass

class AwsInvalidDBInstanceState(AwsBaseException):
    """Exception raised when the DB instance is in an invalid state."""
    pass

# -----------------------------------------------------------
# Blue/Green Deployment Exceptions
# -----------------------------------------------------------

class AwsBlueGreenDeploymentNotFound(AwsBaseException):
    """Exception raised when the Blue/Green deployment is not found."""
    pass