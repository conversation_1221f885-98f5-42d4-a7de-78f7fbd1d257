import argparse
import os


class ArgumentParser:
    def __init__(self):
        self.parser = argparse.ArgumentParser(description='Executor')
        self.parser.add_argument('--encryption-key', default=os.environ.get('ENCRYPTION_KEY'),
                                 help='Encryption key')
        self.parser.add_argument('--devx-gh-token', default=os.environ.get('DEVX_GH_TOKEN'),
                                 help='DEVX_GH_TOKEN environment variable retrieved')
        self.parser.add_argument('--env', default=os.environ.get('ENV'),
                                 help='Environment')

        # GitHub inputs
        self.parser.add_argument('--github-token', default=os.environ.get('GITHUB_TOKEN'),
                                 help='GITHUB_TOKEN or a repo scoped PAT.')
        self.parser.add_argument('--repository', default=os.environ.get('REPOSITORY'),
                                 help='The full name of the repository containing the issue or pull request.')
        self.parser.add_argument('--issue-number', default=os.environ.get('ISSUE_NUMBER'),
                                 help='The number of the issue or pull request.')
        self.parser.add_argument('--comment-author', default=os.environ.get('COMMENT_AUTHOR'),
                                 help='The GitHub user name of the comment author.')

        self.parser.add_argument('--required-approvers', default=os.environ.get('REQUIRED_APPROVERS'),
                                 help='The GitHub user names required for approval.')

    def parse_args(self):
        args = self.parser.parse_args()
        if args.required_approvers: args.required_approvers = [approver.strip() for approver in args.required_approvers.split(',')]
        return args
