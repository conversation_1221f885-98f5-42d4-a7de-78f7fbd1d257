import logging
import os
from functools import cached_property

from typing_extensions import Optional

from .github.team_reader import GitHubTeamReader
from ..dto.decision import ExecutionPlan
from ..dto.github import GitHubProps, CommentManagerProps, FinderInputs, ManagerInputs, Comment
from ..lib import jinja
from ..services.github import GitHubCommentManager, GitHubCommentFinder, GitHubApprovalsReader


class ApprovalRequiredService:
    def __init__(self, plan: ExecutionPlan, github_props: GitHubProps, required_approvers: set, devx_gh_token: str = None):
        self.plan = plan
        self.github_props = github_props
        self._jinja = jinja.env('templates')
        self.comment_id = None
        self.required_approvers = required_approvers if required_approvers is not None else []
        self.devx_gh_token = devx_gh_token

        self.get_comment()

    @cached_property
    def _comment_finder(self):
        return GitHubCommentFinder(inputs=self.finder_props)

    @cached_property
    def finder_props(self):
        return FinderInputs(**{
            **self.github_props.model_dump(),
            "body_includes": "## :warning: Approvals Required",
            "direction": "last"
        })

    @cached_property
    def _comment_manager(self):
        self.get_comment()
        manager_props = CommentManagerProps(
            comment_id=self.comment_id,
            edit_mode='replace',
            # reactions=["+1"],
            # reactions_edit_mode='replace'
        )
        inputs = ManagerInputs(**{**self.github_props.model_dump(), **manager_props.model_dump()})
        return GitHubCommentManager(inputs=inputs)

    @cached_property
    def _approvals_reader(self):
        return GitHubApprovalsReader(github_props=self.github_props)

    @cached_property
    def _team_reader(self):
        return GitHubTeamReader(self.github_props, self.devx_gh_token)

    @property
    def _all_members(self):
        return self._team_reader.get_all_members(self.required_approvers)

    def get_comment(self) -> Optional[Comment]:
        comment = self._comment_finder.find_comment()
        if not comment: return None

        self.comment_id = comment.id
        return comment

    def check_approvals(self) -> tuple[bool, set[str]]:
        """
        Check if the required approvers have approved the PR.
        :return: A tuple of a boolean indicating if at least one approver has approved the PR and a set of approvers
        """

        current_approvers = self._approvals_reader.get_approvers(self.github_props.issue_number)
        required_approvers = self._all_members

        intersection = required_approvers.intersection(current_approvers)

        return len(intersection) > 0, intersection

    def get_content(self, current_approvers: set):
        return self._jinja.get_template('required_approvals.md.jinja2').render(
            plan=self.plan,
            approvers=self._all_members,
            current_approvers=current_approvers
        )

    def create_or_update_comment(self, current_approvers: set):
        logging.info(f"Updating the required approvals comment...")
        self.comment_id = self._comment_manager.create_or_update_comment(self.get_content(current_approvers))
        self._comment_manager.inputs.comment_id = self.comment_id

    def save_to_file(self):
        with open('required_approvals.md', 'w') as file:
            file.write(self.get_content(self._all_members))
