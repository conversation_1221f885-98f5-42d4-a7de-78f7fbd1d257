name: Health Report
run-name: Generate RDS Health Report

on:
  schedule:
    - cron: '0 4 * * 5'
  workflow_dispatch:

jobs:
  run-shared-workflow:
    name: Shared
    strategy:
      matrix:
        profile: [ prod-rh, prod-mot ]
    uses: careem/storage-shared-workflows/.github/workflows/health-report.yml@master
    with:
      profile: ${{ matrix.profile }}
    secrets:
      google-json: ${{ secrets.GOOGLE_JSON }}
