import logging

import boto3
from botocore.exceptions import ClientError

from ..lib.aws.helper import aws_command_handler


class L4ProxyUpdater:
    def __init__(self, rds_identifier):
        self.rds_identifier = rds_identifier
        self.lambda_client = boto3.client('lambda')
        self.function_name = f"{rds_identifier}-db-ip-update-function"

    def trigger_function(self):
        def invoke_function():
            return self.lambda_client.invoke(FunctionName=self.function_name)

        try:
            logging.info(f"Attempting to trigger Lambda function: {self.function_name}")
            aws_command_handler(invoke_function)
            logging.info(f"Successfully triggered Lambda function: {self.function_name}")
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                logging.error(f"Lambda function {self.function_name} does not exist.")
            else:
                logging.error(f"Error while triggering Lambda function: {e}")
            return False

    def find_function_name(self):
        def describe_function():
            return self.lambda_client.get_function(FunctionName=self.function_name)

        try:
            response = aws_command_handler(describe_function)
            return response['Configuration']['FunctionName']
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                logging.warning(f"No Lambda function found with name: {self.function_name}")
            else:
                logging.error(f"Error while describing Lambda function: {e}")
            return None
