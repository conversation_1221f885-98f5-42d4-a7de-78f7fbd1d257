import mysql.connector

# Database connection details
config = {
    'user': 'user',
    'password': 'pass',
    'host': 'host',  # e.g., 'localhost'
    'database': 'mysql'  # We are querying the mysql system database
}

# Output file
input_file = "users.sql"
output_file = "grants_more.sql"

try:
    # Establish the connection
    conn = mysql.connector.connect(**config)
    # Create a cursor object
    cursor = conn.cursor()
    # Open the output file in write mode
    # Users Grants List
    grants_list = []
    ignore_list = []
    with open(input_file, 'r') as file:
        # Iterate over the results and write to the file
        for row in file:
            cursor.execute(row)
            result = cursor.fetchall()
            if len(result) > 2:
                ignore_list.append(result)
            else:
                grants_list.append(result)
        with open(output_file, 'w') as file:
            for row in ignore_list:
                file.write(f"{row}\n")
    print(f"Results successfully written to {output_file}")
except mysql.connector.Error as err:
    print(f"Error: {err}")