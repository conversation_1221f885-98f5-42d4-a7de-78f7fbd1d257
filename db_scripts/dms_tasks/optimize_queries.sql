OPTIMIZE TABLE action;
OPTIMIZE TABLE address;
OPTIMIZE TABLE adma_alert;
OPTIMIZE TABLE adma_alert_receipient;
OPTIMIZE TABLE adma_configuration_update_log;
OPTIMIZE TABLE adma_feature_override;
OPTIMIZE TABLE adma_login;
OPTIMIZE TABLE adma_message;
OPTIMIZE TABLE adma_message_receipient;
OPTIMIZE TABLE adma_reset_log;
OPTIMIZE TABLE agent_sms_limit;
OPTIMIZE TABLE app_configuration;
OPTIMIZE TABLE app_version;
OPTIMIZE TABLE application;
OPTIMIZE TABLE application_token;
OPTIMIZE TABLE automated_booking_dispatch_configuration;
OPTIMIZE TABLE automated_booking_normal_dispatch;
OPTIMIZE TABLE bank_bin_telr_txn_class;
OPTIMIZE TABLE bank_card_bin_data;
OPTIMIZE TABLE black_out_date;
OPTIMIZE TABLE blocked_email;
OPTIMIZE TABLE booking;
OPTIMIZE TABLE booking_activity;
OPTIMIZE TABLE booking_activity_uid_seq;
OPTIMIZE TABLE booking_cancellation_reason;
OPTIMIZE TABLE booking_comment;
OPTIMIZE TABLE booking_configuration;
OPTIMIZE TABLE booking_detail;
OPTIMIZE TABLE booking_detail_uid_seq;
OPTIMIZE TABLE booking_dispatch;
OPTIMIZE TABLE booking_dispatch_configuration;
OPTIMIZE TABLE booking_dispatch_configuration_parameter;
OPTIMIZE TABLE booking_dispatch_configuration_template;
OPTIMIZE TABLE booking_dispatch_configuration_template_has_check_relaxation;
OPTIMIZE TABLE booking_extra;
OPTIMIZE TABLE booking_intent;
OPTIMIZE TABLE booking_location;
OPTIMIZE TABLE booking_location_seq;
OPTIMIZE TABLE booking_location_uid_seq;
OPTIMIZE TABLE booking_offer;
OPTIMIZE TABLE booking_partner;
OPTIMIZE TABLE booking_partner_service_area;
OPTIMIZE TABLE booking_pooling;
OPTIMIZE TABLE booking_request;
OPTIMIZE TABLE booking_snooze;
OPTIMIZE TABLE booking_tracking_detail;
OPTIMIZE TABLE booking_tracking_detail_uid_seq;
OPTIMIZE TABLE booking_uid_seq;
OPTIMIZE TABLE booking_waypoint;
OPTIMIZE TABLE captain_closed;
OPTIMIZE TABLE captain_dispute_history;
OPTIMIZE TABLE captain_wallet;
OPTIMIZE TABLE captain_wallet_configuration;
OPTIMIZE TABLE car;
OPTIMIZE TABLE car_driver;
OPTIMIZE TABLE car_driver_uid_seq;
OPTIMIZE TABLE car_selection;
OPTIMIZE TABLE car_seq;
OPTIMIZE TABLE car_type;
OPTIMIZE TABLE car_type_dispatch;
OPTIMIZE TABLE car_uid_seq;
OPTIMIZE TABLE careem_event;
OPTIMIZE TABLE careem_module;
OPTIMIZE TABLE careem_service_area;
OPTIMIZE TABLE cash_transaction;
OPTIMIZE TABLE cash_transaction_product;
OPTIMIZE TABLE city;
OPTIMIZE TABLE city_configuration;
OPTIMIZE TABLE communication_template;
OPTIMIZE TABLE communication_template_content;
OPTIMIZE TABLE communication_template_parameter;
OPTIMIZE TABLE communication_template_subscriber;
OPTIMIZE TABLE company;
OPTIMIZE TABLE company_payment_country;
OPTIMIZE TABLE company_payment_info;
OPTIMIZE TABLE company_phone;
OPTIMIZE TABLE company_saved_location;
OPTIMIZE TABLE company_uid_seq;
OPTIMIZE TABLE corporate_user_invitation;
OPTIMIZE TABLE country;
OPTIMIZE TABLE country_sms_gateway;
OPTIMIZE TABLE country_sms_gateway_configuration;
OPTIMIZE TABLE country_uid_seq;
OPTIMIZE TABLE credit;
OPTIMIZE TABLE credit_card;
OPTIMIZE TABLE credit_card_payment_gateway;
OPTIMIZE TABLE credit_card_transaction;
OPTIMIZE TABLE credit_card_transaction_notes;
OPTIMIZE TABLE credit_card_uid_seq;
OPTIMIZE TABLE credit_product;
OPTIMIZE TABLE credit_product_service_area;
OPTIMIZE TABLE currency;
OPTIMIZE TABLE currency_exchange;
OPTIMIZE TABLE currency_user_credit_amount;
OPTIMIZE TABLE customer_car_type;
OPTIMIZE TABLE customer_car_type_availability_configuration;
OPTIMIZE TABLE customer_car_type_availability_configuration_company;
OPTIMIZE TABLE customer_car_type_availability_configuration_sla;
OPTIMIZE TABLE customer_car_type_availability_configuration_zone;
OPTIMIZE TABLE customer_car_type_availability_message;
OPTIMIZE TABLE customer_car_type_cap;
OPTIMIZE TABLE customer_car_type_category;
OPTIMIZE TABLE customer_car_type_company;
OPTIMIZE TABLE customer_car_type_description;
OPTIMIZE TABLE customer_car_type_details;
OPTIMIZE TABLE customer_car_type_location;
OPTIMIZE TABLE customer_car_type_uid_seq;
OPTIMIZE TABLE customer_car_type_verification_check;
OPTIMIZE TABLE customer_car_type_zone;
OPTIMIZE TABLE customer_custom_pricing;
OPTIMIZE TABLE customer_fixed_pricing;
OPTIMIZE TABLE customer_fixed_pricing_surcharge;
OPTIMIZE TABLE customer_pricing;
OPTIMIZE TABLE customer_pricing_surcharge;
OPTIMIZE TABLE device;
OPTIMIZE TABLE discount;
OPTIMIZE TABLE dispatch_check_relaxation;
OPTIMIZE TABLE dispatch_configuration_template;
OPTIMIZE TABLE dispatch_radius_per_spatial_zone;
OPTIMIZE TABLE dispatch_response;
OPTIMIZE TABLE driver_availability;
OPTIMIZE TABLE driver_availability_uid_seq;
OPTIMIZE TABLE driver_bank_detail;
OPTIMIZE TABLE driver_block;
OPTIMIZE TABLE driver_bonus;
OPTIMIZE TABLE driver_bonus_zone;
OPTIMIZE TABLE driver_car_type;
OPTIMIZE TABLE driver_coordinator;
OPTIMIZE TABLE driver_coordinator_token;
OPTIMIZE TABLE driver_credit;
OPTIMIZE TABLE driver_credit_seq;
OPTIMIZE TABLE driver_credit_uid_seq;
OPTIMIZE TABLE driver_custom_pricing;
OPTIMIZE TABLE driver_customer_trip_rating;
OPTIMIZE TABLE driver_data_usage;
OPTIMIZE TABLE driver_detail;
OPTIMIZE TABLE driver_document;
OPTIMIZE TABLE driver_fixed_pricing;
OPTIMIZE TABLE driver_fixed_pricing_surcharge;
OPTIMIZE TABLE driver_fraud;
OPTIMIZE TABLE driver_invitation;
OPTIMIZE TABLE driver_last_seen;
OPTIMIZE TABLE driver_manual_trip_pricing;
OPTIMIZE TABLE driver_manual_trip_request;
OPTIMIZE TABLE driver_passport;
OPTIMIZE TABLE driver_payment;
OPTIMIZE TABLE driver_payment_adjustment;
OPTIMIZE TABLE driver_payment_adjustment_category;
OPTIMIZE TABLE driver_payment_adjustment_uid_seq;
OPTIMIZE TABLE driver_payment_uid_seq;
OPTIMIZE TABLE driver_phone;
OPTIMIZE TABLE driver_point_log;
OPTIMIZE TABLE driver_point_log_uid_seq;
OPTIMIZE TABLE driver_pricing;
OPTIMIZE TABLE driver_pricing_surcharge;
OPTIMIZE TABLE driver_referred_driver;
OPTIMIZE TABLE driver_referred_user;
OPTIMIZE TABLE driver_referred_user_seq;
OPTIMIZE TABLE driver_referred_user_uid_seq;
OPTIMIZE TABLE driver_release_configuration;
OPTIMIZE TABLE driver_service_area;
OPTIMIZE TABLE driver_spoken_language;
OPTIMIZE TABLE driver_status;
OPTIMIZE TABLE driver_status_uid_seq;
OPTIMIZE TABLE driver_to_customer_call;
OPTIMIZE TABLE driver_token;
OPTIMIZE TABLE driver_token_temp;
OPTIMIZE TABLE driver_user_invitation;
OPTIMIZE TABLE driver_user_invitation_seq;
OPTIMIZE TABLE driver_user_invitation_uid_seq;
OPTIMIZE TABLE drivers;
OPTIMIZE TABLE drivers_knows_city;
OPTIMIZE TABLE earning;
OPTIMIZE TABLE earning_configuration;
OPTIMIZE TABLE entity_dump;
OPTIMIZE TABLE eta_adjustment;
OPTIMIZE TABLE event_point;
OPTIMIZE TABLE external_booking;
OPTIMIZE TABLE extra;
OPTIMIZE TABLE extra_category;
OPTIMIZE TABLE extra_company_customer_car_type;
OPTIMIZE TABLE faraway_bonus;
OPTIMIZE TABLE fixed_package;
OPTIMIZE TABLE fixed_package_configurations;
OPTIMIZE TABLE fixed_package_customer_car_type;
OPTIMIZE TABLE fixed_package_service_area;
OPTIMIZE TABLE geofenced_locations;
OPTIMIZE TABLE global_blocked_location;
OPTIMIZE TABLE global_blocked_location_enriched;
OPTIMIZE TABLE global_blocked_location_enriched_old;
OPTIMIZE TABLE global_saved_location;
OPTIMIZE TABLE guest;
OPTIMIZE TABLE id_card;
OPTIMIZE TABLE incoming_adma_booking_message;
OPTIMIZE TABLE incoming_adma_message_log;
OPTIMIZE TABLE incoming_call_rating;
OPTIMIZE TABLE incoming_phone_call;
OPTIMIZE TABLE incoming_sms_driver;
OPTIMIZE TABLE incoming_sms_user;
OPTIMIZE TABLE insurance;
OPTIMIZE TABLE invoice;
OPTIMIZE TABLE invoice_payment;
OPTIMIZE TABLE invoice_payment_component;
OPTIMIZE TABLE invoice_payment_component_uid_seq;
OPTIMIZE TABLE invoice_payment_uid_seq;
OPTIMIZE TABLE kpi;
OPTIMIZE TABLE language;
OPTIMIZE TABLE limo_company;
OPTIMIZE TABLE limo_company_adjustment;
OPTIMIZE TABLE limo_company_adjustment_uid_seq;
OPTIMIZE TABLE limo_company_detail;
OPTIMIZE TABLE limo_company_host;
OPTIMIZE TABLE limo_company_payment_information;
OPTIMIZE TABLE limo_company_works_with_service_provider;
OPTIMIZE TABLE location;
OPTIMIZE TABLE location_coordinate;
OPTIMIZE TABLE location_coordinate_uid_seq;
OPTIMIZE TABLE location_detail;
OPTIMIZE TABLE location_detail_uid_seq;
OPTIMIZE TABLE location_uid_seq;
OPTIMIZE TABLE login_log;
OPTIMIZE TABLE loyalty_program;
OPTIMIZE TABLE loyalty_program_bonus;
OPTIMIZE TABLE loyalty_setting;
OPTIMIZE TABLE loyalty_setting_key;
OPTIMIZE TABLE make;
OPTIMIZE TABLE manual_credit_card_transaction;
OPTIMIZE TABLE manual_trip;
OPTIMIZE TABLE marketing_email;
OPTIMIZE TABLE merchant;
OPTIMIZE TABLE model;
OPTIMIZE TABLE multi_dispatch_configuration;
OPTIMIZE TABLE on_demand_dispatch_parameter;
OPTIMIZE TABLE outgoing_sms_driver;
OPTIMIZE TABLE outgoing_sms_user;
OPTIMIZE TABLE outgoing_user_sms_template;
OPTIMIZE TABLE passenger;
OPTIMIZE TABLE passenger_seq;
OPTIMIZE TABLE passenger_uid_seq;
OPTIMIZE TABLE payment_gateway;
OPTIMIZE TABLE payment_gateway_configuration;
OPTIMIZE TABLE payment_gateway_currency;
OPTIMIZE TABLE payment_information;
OPTIMIZE TABLE payment_information_block;
OPTIMIZE TABLE payment_information_blocked_charge_code;
OPTIMIZE TABLE payment_information_seq;
OPTIMIZE TABLE payment_information_uid_seq;
OPTIMIZE TABLE payment_processing_schedulers;
OPTIMIZE TABLE point_of_interest;
OPTIMIZE TABLE point_of_interest_uid_seq;
OPTIMIZE TABLE pricing_component;
OPTIMIZE TABLE pricing_report;
OPTIMIZE TABLE priority_dispatch_parameter;
OPTIMIZE TABLE priority_dispatch_zone;
OPTIMIZE TABLE product;
OPTIMIZE TABLE promotion_attributes_uid_seq;
OPTIMIZE TABLE promotion_block_uid_seq;
OPTIMIZE TABLE promotion_booking_card_series_uid_seq;
OPTIMIZE TABLE promotion_booking_customer_car_type_uid_seq;
OPTIMIZE TABLE promotion_booking_phone_number_series_uid_seq;
OPTIMIZE TABLE promotion_booking_uid_seq;
OPTIMIZE TABLE promotion_booking_zone_uid_seq;
OPTIMIZE TABLE promotion_campaign_uid_seq;
OPTIMIZE TABLE promotion_credit_card_series_uid_seq;
OPTIMIZE TABLE promotion_credit_card_uid_seq;
OPTIMIZE TABLE promotion_credit_uid_seq;
OPTIMIZE TABLE promotion_location;
OPTIMIZE TABLE promotion_uid_seq;
OPTIMIZE TABLE promotion_usage_uid_seq;
OPTIMIZE TABLE publicapi_customer;
OPTIMIZE TABLE pushy_receipt;
OPTIMIZE TABLE refund;
OPTIMIZE TABLE registration;
OPTIMIZE TABLE report;
OPTIMIZE TABLE reporting_zone;
OPTIMIZE TABLE request_token;
OPTIMIZE TABLE retry_bonus;
OPTIMIZE TABLE role_modules;
OPTIMIZE TABLE role_users;
OPTIMIZE TABLE roles;
OPTIMIZE TABLE route;
OPTIMIZE TABLE route_pricing_component;
OPTIMIZE TABLE route_pricing_component_uid_seq;
OPTIMIZE TABLE route_uid_seq;
OPTIMIZE TABLE route_verification_error;
OPTIMIZE TABLE schedule;
OPTIMIZE TABLE service_area;
OPTIMIZE TABLE service_area_city;
OPTIMIZE TABLE service_area_company;
OPTIMIZE TABLE service_area_company_car_type;
OPTIMIZE TABLE service_area_configuration;
OPTIMIZE TABLE service_area_connection;
OPTIMIZE TABLE service_area_extra;
OPTIMIZE TABLE service_area_language;
OPTIMIZE TABLE service_area_merchant_configuration;
OPTIMIZE TABLE service_area_payment_information;
OPTIMIZE TABLE service_area_uid_seq;
OPTIMIZE TABLE service_area_verification_check;
OPTIMIZE TABLE service_area_verification_configuration;
OPTIMIZE TABLE service_area_zone;
OPTIMIZE TABLE service_provider;
OPTIMIZE TABLE service_provider_car_type_configuration;
OPTIMIZE TABLE service_provider_configuration;
OPTIMIZE TABLE service_provider_country;
OPTIMIZE TABLE service_provider_country_configuration;
OPTIMIZE TABLE service_provider_currency_referral;
OPTIMIZE TABLE service_provider_payment_information;
OPTIMIZE TABLE service_provider_preference;
OPTIMIZE TABLE shadow_user_credit;
OPTIMIZE TABLE sim;
OPTIMIZE TABLE sms_blast;
OPTIMIZE TABLE sms_blast_configuration;
OPTIMIZE TABLE sms_blast_driver_category;
OPTIMIZE TABLE sms_blast_driver_status;
OPTIMIZE TABLE sp_search_upcoming_bookings;
OPTIMIZE TABLE sp_search_upcoming_bookings_cloud;
OPTIMIZE TABLE spatial_zone;
OPTIMIZE TABLE spatial_zone_dispatch;
OPTIMIZE TABLE spatial_zone_intensity;
OPTIMIZE TABLE spatial_zone_queue;
OPTIMIZE TABLE surcharge;
OPTIMIZE TABLE surge;
OPTIMIZE TABLE system_action;
OPTIMIZE TABLE system_action_possible_action;
OPTIMIZE TABLE system_dispatch;
OPTIMIZE TABLE system_log;
OPTIMIZE TABLE tax;
OPTIMIZE TABLE tax_component;
OPTIMIZE TABLE tax_distribution_component;
OPTIMIZE TABLE tax_unprocessed_trip;
OPTIMIZE TABLE taximo_bucket;
OPTIMIZE TABLE taximo_pricing;
OPTIMIZE TABLE taximo_surcharge;
OPTIMIZE TABLE telecom_gateway_configuration;
OPTIMIZE TABLE telecom_point;
OPTIMIZE TABLE telecom_transaction;
OPTIMIZE TABLE telr_mpi_transaction;
OPTIMIZE TABLE ticket_owner;
OPTIMIZE TABLE tollgate;
OPTIMIZE TABLE tollgate_coordinate;
OPTIMIZE TABLE tollgate_discount;
OPTIMIZE TABLE tollgate_trip;
OPTIMIZE TABLE translation;
OPTIMIZE TABLE trip;
OPTIMIZE TABLE trip_booking_detail;
OPTIMIZE TABLE trip_calculation_booking;
OPTIMIZE TABLE trip_driver_pricing_component;
OPTIMIZE TABLE trip_financial;
OPTIMIZE TABLE trip_follow_up;
OPTIMIZE TABLE trip_follow_up_category;
OPTIMIZE TABLE trip_follow_up_type;
OPTIMIZE TABLE trip_location;
OPTIMIZE TABLE trip_location_uid_seq;
OPTIMIZE TABLE trip_modification;
OPTIMIZE TABLE trip_offline_pricing_component;
OPTIMIZE TABLE trip_price_type_pricing_component;
OPTIMIZE TABLE trip_pricing_attributes;
OPTIMIZE TABLE trip_pricing_component;
OPTIMIZE TABLE trip_rating;
OPTIMIZE TABLE trip_rating_category;
OPTIMIZE TABLE trip_rating_uid_seq;
OPTIMIZE TABLE trip_report;
OPTIMIZE TABLE trip_route;
OPTIMIZE TABLE trip_route_uid_seq;
OPTIMIZE TABLE trip_transaction;
OPTIMIZE TABLE trip_uid_seq;
OPTIMIZE TABLE trip_verification_error;
OPTIMIZE TABLE trouble_zone;
OPTIMIZE TABLE underpaid_trip;
OPTIMIZE TABLE upcoming_bookings_view;
OPTIMIZE TABLE user;
OPTIMIZE TABLE user_activity_log;
OPTIMIZE TABLE user_agent;
OPTIMIZE TABLE user_approval_request;
OPTIMIZE TABLE user_availed_promotion_seq;
OPTIMIZE TABLE user_availed_promotion_uid_seq;
OPTIMIZE TABLE user_blocked_car_type;
OPTIMIZE TABLE user_blocked_car_type_uid_seq;
OPTIMIZE TABLE user_company_access;
OPTIMIZE TABLE user_country_access;
OPTIMIZE TABLE user_credit;
OPTIMIZE TABLE user_credit_category;
OPTIMIZE TABLE user_credit_description;
OPTIMIZE TABLE user_credit_description_amount;
OPTIMIZE TABLE user_credit_seq;
OPTIMIZE TABLE user_credit_uid_seq;
OPTIMIZE TABLE user_detail;
OPTIMIZE TABLE user_detail_seq;
OPTIMIZE TABLE user_detail_uid_seq;
OPTIMIZE TABLE user_driver_block;
OPTIMIZE TABLE user_driver_block_uid_seq;
OPTIMIZE TABLE user_fixed_package;
OPTIMIZE TABLE user_fixed_package_uid_seq;
OPTIMIZE TABLE user_helps_company;
OPTIMIZE TABLE user_helps_user;
OPTIMIZE TABLE user_helps_user_seq;
OPTIMIZE TABLE user_helps_user_uid_seq;
OPTIMIZE TABLE user_identity;
OPTIMIZE TABLE user_invitation;
OPTIMIZE TABLE user_invitation_uid_seq;
OPTIMIZE TABLE user_location_uid_seq;
OPTIMIZE TABLE user_loyalty;
OPTIMIZE TABLE user_loyalty_program;
OPTIMIZE TABLE user_loyalty_program_uid_seq;
OPTIMIZE TABLE user_module_access;
OPTIMIZE TABLE user_module_access_uid_seq;
OPTIMIZE TABLE user_monthly_loyalty_program_payout;
OPTIMIZE TABLE user_monthly_loyalty_program_payout_uid_seq;
OPTIMIZE TABLE user_monthly_spending;
OPTIMIZE TABLE user_monthly_spending_uid_seq;
OPTIMIZE TABLE user_payment_information;
OPTIMIZE TABLE user_payment_information_uid_seq;
OPTIMIZE TABLE user_phone;
OPTIMIZE TABLE user_phone_seq;
OPTIMIZE TABLE user_phone_uid_seq;
OPTIMIZE TABLE user_prefered_language;
OPTIMIZE TABLE user_preference;
OPTIMIZE TABLE user_saved_location;
OPTIMIZE TABLE user_saved_location_uid_seq;
OPTIMIZE TABLE user_seq;
OPTIMIZE TABLE user_signup_detail;
OPTIMIZE TABLE user_signup_detail_seq;
OPTIMIZE TABLE user_signup_detail_uid_seq;
OPTIMIZE TABLE user_token;
OPTIMIZE TABLE user_uid_seq;
OPTIMIZE TABLE vat_applicable_country;
OPTIMIZE TABLE walk_in_customer_pricing;
OPTIMIZE TABLE zone;
OPTIMIZE TABLE zone_coordinate;
OPTIMIZE TABLE zone_pointer;
OPTIMIZE TABLE zone_surge;