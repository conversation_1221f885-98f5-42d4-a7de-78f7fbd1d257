import yaml
import os

class Configs:

    def get_account_ids(self):
        accounts_file_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'resources/accounts.yaml')
        with open(accounts_file_path) as file:
            all_accounts = yaml.load(file, Loader=yaml.FullLoader)
            all_account_ids = list(all_accounts.values())
        return all_account_ids


    def get_account_by_profile(self, profile):
        accounts_file_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'resources/accounts.yaml')
        with open(accounts_file_path) as file:
            all_accounts = yaml.load(file, Loader=yaml.FullLoader)
            return all_accounts[profile]
