import functools
from typing import Dict

import boto3

from ..dto import Record
from ..lib import DatabaseTask, DatabaseConnector


class ListModifiedRDSParameters(DatabaseTask):
    """
    Task to list all modified parameters for each RDS instance.
    """

    sheet_name = "Modified Parameter Groups"

    PARAMETER_NOTES = {
        # Common parameters
        #...

        # MySQL-specific parameters
        "log_bin_trust_function_creators": "Allows non-superusers to create functions; should be restricted.",
        "default_password_lifetime": "DANGEROUS ⚠️: Must set 0 for no expiry.",

        # PostgreSQL-specific parameters
        #...
    }

    def __init__(self, profile: str, boto3_session: boto3.Session, db_connector: DatabaseConnector, configs: Dict):
        super().__init__(profile, boto3_session, db_connector, configs)
        self.rds = self.session.client("rds")

    @classmethod
    def columns(cls):
        return ["ParameterName", "ParameterValue", "Description", "Notes"]

    def fetch_data(self):
        # Fetch instance details to get Parameter Group Name
        instance_details = self.rds.describe_db_instances(DBInstanceIdentifier=self.db_info["instance_identifier"])
        db_instance = instance_details["DBInstances"][0]
        parameter_groups = db_instance.get("DBParameterGroups", [])

        if not parameter_groups:
            return []

        db_parameter_group = parameter_groups[0]["DBParameterGroupName"]

        # Fetch modified parameters
        modified_parameters = self.get_modified_parameters(db_parameter_group)

        return modified_parameters

    @functools.cache
    def get_modified_parameters(self, db_parameter_group: str):
        """
        Fetch modified parameters for a given DB parameter group and cache the results.
        """
        rds_client = self.session.client("rds")
        response = rds_client.describe_db_parameters(
            DBParameterGroupName=db_parameter_group,
            Source="user"
        )

        parameters = response.get("Parameters", [])

        return [
            {
                "ParameterName": param["ParameterName"],
                "ParameterValue": param.get("ParameterValue", ""),
                "Description": param.get("Description", ""),
                "Notes": self.PARAMETER_NOTES.get(param["ParameterName"], "")
            }
            for param in parameters if param.get("ApplyType") and param.get("ParameterValue")
        ]

    def records(self, raw_data):
        """
        Converts modified parameters into record format.
        """
        return [
            Record(
                ParameterName=param["ParameterName"],
                ParameterValue=param["ParameterValue"],
                Description=param["Description"],
                Notes=param["Notes"]
            )
            for param in raw_data
        ]
