"""
Custom logger adapter for database tasks.
"""

import logging
from threading import current_thread


class Task<PERSON>oggerAdapter(logging.LoggerAdapter):
    """
    A logger adapter that adds thread name, instance ID, and task name to every log message.
    This ensures consistent logging format across all task implementations.
    
    Usage:
        logger = TaskLoggerAdapter(base_logger, {
            'instance_id': instance_id,
            'task_name': task_name
        })
        logger.info("This is a log message")
        # Output: [ThreadName] [instance_id] [task_name] This is a log message
    """
    def process(self, msg, kwargs):
        thread_name = current_thread().name
        instance_id = self.extra.get('instance_id', 'unknown')
        task_name = self.extra.get('task_name', 'unknown')
        return f"[{thread_name}] [{instance_id}] [{task_name}] {msg}", kwargs
