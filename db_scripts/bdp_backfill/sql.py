

class SQL:

    def __init__():
        pass

    @staticmethod
    def get_updated_records(table_name, start_date_time, end_date_time):
        query = f"""
                SELECT id, last_updated FROM {table_name} WHERE last_updated BETWEEN '{start_date_time}' AND '{end_date_time}';
                """
        return query
    
    @staticmethod
    def update_last_updated_column(table_name, id, last_updated):
        query = f"""
                UPDATE {table_name} SET last_updated = '{last_updated}' WHERE id = {id};
                """
        return query