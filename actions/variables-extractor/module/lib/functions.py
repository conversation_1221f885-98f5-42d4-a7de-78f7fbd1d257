import re

from parse import parse


def extract_variables_from_sql(file_path):
    variables = {}
    sql_content = []
    with open(file_path, 'r') as file:
        for line in file:
            match = re.match(r'(--|#)\s*(\w+)\s*=\s*(.+)', line)
            if match:
                comment, key, value = match.groups()
                variables[key.lower()] = value.strip()
            else:
                sql_content.append(line)
    return variables, ''.join(sql_content)


def escape_multiline_content(content):
    return content.replace('\n', '\\n').replace('\r', '\\r')


def extract_variables_from_path(path, pattern):
    result = parse(pattern, path)
    if result:
        return result.named
    else:
        raise ValueError("Pattern does not match the path")
