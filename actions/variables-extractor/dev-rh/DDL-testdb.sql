#endpoint = test-db.cgevunlz3bfz.eu-west-1.rds.amazonaws.com
#schema = payout-info
#downtime_tolerance = 10

ALTER TABLE bank
    ADD COLUMN swift_code VARCHAR(11) NULL;

CREATE TABLE transaction_log (
                                 id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
                                 transaction_id VARCHAR(255) NOT NULL,
                                 status VARCHAR(64) NOT NULL,
                                 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                 updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

ALTER TABLE payment_method
    ADD CONSTRAINT fk_payout_option
        FOREIGN KEY (payout_option_id) REFERENCES payout_option(id);

CREATE INDEX idx_audit_log_created_at
    ON audit_log (created_at);

ALTER TABLE test
    MODIFY COLUMN price FLOAT NOT NULL;

UPDATE payment_method
SET status = 1
WHERE id = 130;

DELETE FROM blocking_event
WHERE payout_client = 'KUBERA';

INSERT INTO bank (name, display_name, code, currency, country, status, data_json, translations, updated_at, created_at,
                  iban_bank_identifier)
VALUES ('Bank Name', 'Bank Display Name', 'BANKCODE', 'USD', 'US', 1, '{}', '{}', NOW(), NOW(), 'IBAN123');

UPDATE payout_option
SET currency = 'EUR', country = 'DE'
WHERE id = 67890;

DELETE FROM translations
WHERE id = 54321;

SELECT b.id, b.name, b.display_name, po.name AS payout_option_name, po.currency
FROM bank b
         JOIN payment_method pm ON b.id = pm.entity_id
         JOIN payout_option po ON pm.payout_option_id = po.id;

SELECT al.*
FROM audit_log al
WHERE al.created_at = (
    SELECT MAX(created_at)
    FROM audit_log
    WHERE table_name = al.table_name
);

SELECT country, COUNT(*) AS request_count
FROM payout_request
GROUP BY country;

SELECT id, name, JSON_EXTRACT(data_json, '$.field_name') AS field_value
FROM bank;

SELECT *
FROM card_cash_in_transaction_log
WHERE created_at >= NOW() - INTERVAL 30 DAY;