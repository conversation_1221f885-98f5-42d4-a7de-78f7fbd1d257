import json


def postgres_convert_schemas_to_dict_string(schemas):
    # Convert postgres schemas to a dictionary
    schemas_dict = {}
    for item in schemas:
        db, schema = item.split(':')
        db = db.strip()
        schema = schema.strip()
        if db not in schemas_dict:
            schemas_dict[db] = []
        schemas_dict[db].append(schema)
    return json.dumps(schemas_dict)


def mysql_convert_schemas_to_list_string(schemas):
    return json.dumps(schemas)
