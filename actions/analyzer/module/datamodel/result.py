import logging
from dataclasses import dataclass, field
from functools import cached_property
from typing import Optional

import humanize

from ..lib import jinja, QueryPlanAnalyzer
from ..lib import sql
from ..models import Query
from ..models.database import Database
from ..shared.dto.aws import Env


@dataclass
class Result:
    env: Optional[Env]
    database: Database
    queries: list[Query] = field(default_factory=list)

    def _database_analysis(self):
        analysis = self.database.analysis
        analysis['db_size_text'] = humanize.naturalsize(analysis['db_size_bytes'])
        return {
            'rds_instance_name': self.database.db_info.instance,
            'name': self.database.db_info.database,
            'content': analysis
        }

    def _queries_analysis(self):
        results = []
        engine = self.database.db_info.engine
        for query in self.queries:
            result = {
                'query': sql.sql_format(query.query, engine),
                'analysis': {}
            }

            plan = query.analysis.get('query_plan_json') or query.analysis.get('query_plan_text')

            if plan:
                plan_results = QueryPlanAnalyzer(engine, plan).parse()
                result['analysis']['plan_text'] = query.analysis.get('query_plan_text')
                data = {
                    'estimated_scanned_rows': plan_results['estimated_scanned_rows'],
                    'estimated_created_rows': plan_results['estimated_created_rows'],
                    'estimated_updated_rows': plan_results['estimated_updated_rows'],
                    'estimated_deleted_rows': plan_results['estimated_deleted_rows'],
                    'planning_time': plan_results['planning_time'],
                }

                result['analysis']['plan_results'] = data

            results.append(result)
        return results

    def _tables_analysis(self):
        unique_tables = {}
        for query in self.queries:
            for query_table in query.tables:
                if query_table.table.name in unique_tables:
                    continue

                analysis = query_table.table.analysis
                stats = analysis['stats']
                if stats['table_size_bytes']:
                    analysis['stats']['table_size_text'] = humanize.naturalsize(analysis['stats']['table_size_bytes'])

                unique_tables[query_table.table.name] = analysis

        return [{'table': name, 'analysis': analysis} for name, analysis in unique_tables.items()]

    @property
    def analysis(self):
        return {
            'database_analysis': self._database_analysis(),
            'queries_analysis': self._queries_analysis(),
            'tables_analysis': self._tables_analysis()
        }

    @cached_property
    def _jinja(self):
        return jinja.env('query_templates/results')

    def save_to_file(self, output_file='analysis_output.md'):
        template = self._jinja.get_template('analysis_template.md.jinja2')
        rendered_markdown = template.render(env=self.env, analysis=self.analysis, db_info=self.database.db_info)

        # Write the rendered Markdown to a file
        with open(output_file, 'w') as file:
            file.write(rendered_markdown)
        logging.info(f"Analysis written to {output_file}")
