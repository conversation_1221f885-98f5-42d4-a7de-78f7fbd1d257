import logging

from ..configs import DecisionThresholds
from ..shared.dto.analysis import Analysis
from ..shared.dto.aws.env import Env
from ..shared.dto.decision import ExecutionPlan, ExecutionType, DecisionStatus
from ..shared.services import ExecutionPlanService


class ExecutionPlanner:
    def __init__(self, analysis: Analysis,
                 decision_thresholds: DecisionThresholds,
                 service: ExecutionPlanService,
                 env: Env):
        self.analysis = analysis
        self.table_size_threshold = decision_thresholds.table_size_threshold
        self.rows_count_threshold = decision_thresholds.rows_count_threshold
        self.decision_thresholds = decision_thresholds
        self.service: ExecutionPlanService = service
        self.plan: ExecutionPlan = service.plan
        self.env: Env = env

    def make_decision(self):
        if not self.env.is_prod():  # Changed from is_staging() to not is_prod() to include all non-prod environments
            self.plan.execution_type = ExecutionType.DIRECT
            self.plan.create_replica = False
            self.plan.replica_status = DecisionStatus.NOT_NEEDED
            self.plan.bg_status = DecisionStatus.NOT_NEEDED
            self.plan.status = DecisionStatus.PROCESSING
            logging.info(f"Status set to: {self.plan.status}")
            return self.plan

        any_table_exceeds_threshold = any(
            table.analysis.stats.table_size_bytes > self.table_size_threshold for table in self.analysis.tables_analysis
        )

        any_query_exceeds_threshold = any(
            query.analysis and query.analysis.plan_results and query.analysis.plan_results.estimated_scanned_rows is not None and query.analysis.plan_results.estimated_scanned_rows >= self.rows_count_threshold
            for query in self.analysis.queries_analysis
        )

        self.plan.status = DecisionStatus.PENDING
        logging.info(f"Status set to: {self.plan.status}")

        if any_table_exceeds_threshold:
            if any_query_exceeds_threshold:
                self.plan.create_replica = False
                self.plan.execution_type = ExecutionType.BG_DEPLOYMENT
                self.plan.replica_status = DecisionStatus.NOT_NEEDED
                self.plan.bg_status = DecisionStatus.PENDING
                self.plan.status = DecisionStatus.PROCESSING
                logging.info(f"Status set to: {self.plan.status}")
            else:
                self.plan.create_replica = True
                self.plan.replica_status = DecisionStatus.PENDING
                self.plan.status = DecisionStatus.PENDING
                logging.info(f"Status set to: {self.plan.status}")
        else:
            if not any_query_exceeds_threshold:
                self.plan.execution_type = ExecutionType.DIRECT
                self.plan.create_replica = False
                self.plan.replica_status = DecisionStatus.NOT_NEEDED
                self.plan.bg_status = DecisionStatus.NOT_NEEDED
                self.plan.status = DecisionStatus.PROCESSING
                logging.info(f"Status set to: {self.plan.status}")
            else:
                self.plan.create_replica = True
                self.plan.replica_status = DecisionStatus.PENDING
                self.plan.status = DecisionStatus.PENDING
                logging.info(f"Status set to: {self.plan.status}")

        return self.plan

    def make_decision_after_examination(self):
        # For non-production environments, always use direct execution regardless of examination results
        if not self.env.is_prod():
            self.plan.execution_type = ExecutionType.DIRECT
            self.plan.create_replica = False
            self.plan.replica_status = DecisionStatus.EXECUTED  # Mark as executed to prevent further replica creation
            self.plan.bg_status = DecisionStatus.NOT_NEEDED
            self.plan.status = DecisionStatus.PROCESSING
            logging.info(f"Non-prod environment: Status set to {self.plan.status}, using direct execution")
            return self.plan

        any_query_error = any(query.analysis.error is not None for query in self.analysis.queries_analysis)

        if any_query_error:
            logging.info("Error occurred in query execution")
            self.plan.status = DecisionStatus.ERRORED
            logging.info(f"Status set to: {self.plan.status}")
            self.plan.execution_type = ExecutionType.NOT_DECIDED
            self.plan.create_replica = True
            self.plan.replica_status = DecisionStatus.EXECUTED_WITH_ERRORS
            self.plan.bg_status = DecisionStatus.NA
            return self.plan

        any_query_execution_time_exceeded_tolerance = any(
            query.analysis.execution_time is not None and
            query.analysis.execution_time > self.decision_thresholds.downtime_tolerance
            for query in self.analysis.queries_analysis
        )

        all_query_execution_time_is_none = all(
            query.analysis.execution_time is None for query in self.analysis.queries_analysis
        )

        if all_query_execution_time_is_none:
            raise ValueError("All queries execution time is None")

        if any_query_execution_time_exceeded_tolerance:
            self.plan.execution_type = ExecutionType.BG_DEPLOYMENT
            self.plan.create_replica = True
            self.plan.replica_status = DecisionStatus.EXECUTED
            self.plan.bg_status = DecisionStatus.PENDING
            self.plan.status = DecisionStatus.PROCESSING
            logging.info(f"Status set to: {self.plan.status}")
        else:
            self.plan.execution_type = ExecutionType.DIRECT
            self.plan.create_replica = True
            self.plan.replica_status = DecisionStatus.EXECUTED
            self.plan.bg_status = DecisionStatus.NOT_NEEDED
            self.plan.status = DecisionStatus.PROCESSING
            logging.info(f"Status set to: {self.plan.status}")

        return self.plan
