import logging
from services.alarms import Alarms
from data.redis import Redis

class CloudWatchAlarmManager:
    def __init__(self, ec2_client, cloudwatch_client, account_id, elasticache_client=None):
        self.ec2_client = ec2_client
        self.elasticache_client = elasticache_client
        self.account_id = account_id
        self.cloudwatch_client = cloudwatch_client
        self.logger = logging.getLogger(self.__class__.__name__)

    def add_alarms_for_redis_cluster(self, node: Redis):
        self.logger.info("Adding alarms for Redis cluster: %s", node.cluster_id)
        alarms = Alarms(self.cloudwatch_client)
        alarms.add_all_alarms_to_redis_cluster(node, self.account_id)