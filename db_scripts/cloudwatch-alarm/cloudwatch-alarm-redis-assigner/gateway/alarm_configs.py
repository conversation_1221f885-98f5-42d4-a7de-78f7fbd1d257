import logging

import yaml

from data.redis import Redis
from environment_variables import *
from services.threshold_calculator import ThresholdCalculator


class AlarmConfigs:
    def __init__(self, cloudwatch_client):
        self.cloudwatch_client = cloudwatch_client

    def get_object_alarm_configuration(self, instance_info, alarm_name, alarm, alarm_action):

        calc = ThresholdCalculator(self.cloudwatch_client)

        if alarm['threshold'] == 'default':
            alarm['threshold'] = calc.calculate_threshold(alarm['metricname'], instance_info)

        return {
            'cluster_id': instance_info.cluster_id,
            'node_id': instance_info.node_id,
            'alarm_name': alarm_name,
            'alarm_action': alarm_action,
            'metric_name': alarm['metricname'],
            'metric_operator': alarm['operator'],
            'period': alarm['period'],
            'threshold': alarm['threshold'],
            'statistic': alarm['statistic'],
            'description': alarm['description'],
            'datapoint': alarm['datapoint']
            # 'mem_size': instance_info['mem_size'],
            # 'storage_size': instance_info.get('db_storage_size', 0),
            # 'db_engine': instance_info.get('db_engine', ''),
        }

    def get_alarm_configurations(self, alarm, node: Redis, account_id):
        lambda_arn_slack_action = f"arn:aws:sns:eu-west-1:{account_id}:storage-rds-cloudwatch-alarm-slack"
        conf_alarm_action = alarm['alarm_action']
        alarm_name = ALARM_PREFIX + node.cluster_id + '_METRIC_' + alarm['metricname']
        alarm_action = lambda_arn_slack_action if conf_alarm_action == 'default' else conf_alarm_action
        return self.get_object_alarm_configuration(node, alarm_name, alarm, alarm_action)

    def get_file_configurations(self):
        with open(FILE_PATH, 'r') as file:
            conf = yaml.load(file, Loader=yaml.FullLoader)
        logging.info("Successfully fetched configuration file")
        return conf

    def get_node_alarm_configuration_from_file(self, instance_id):
        conf = self.get_file_configurations()
        if instance_id in conf:
            new_defaults = dict(conf['default'])
            for alarm in conf[instance_id]['alarms']:
                old_alarm = [x for x in new_defaults['alarms'] if x['metricname'] == alarm['metricname']][0]
                new_defaults['alarms'].remove(old_alarm)
                new_defaults['alarms'].append(alarm)
                logging.info("DEFAULT " + alarm['metricname'])
            return new_defaults
        else:
            return conf['default']

    # def get_all_alarms(self, cloudwatch_client):
    #     response = cloudwatch_client.describe_alarms(
    #         AlarmNamePrefix=ALARM_PREFIX,
    #         AlarmTypes=['MetricAlarm']
    #     )
    #     rds_alarms = []
    #     for alarm in response['MetricAlarms']:
    #         rds_alarms.append({
    #             'alarm_name': alarm["AlarmName"],
    #             'threshold': alarm["Threshold"],
    #             'metric': alarm["MetricName"],
    #             'period': alarm["Period"],
    #             'operator': alarm["ComparisonOperator"],
    #             'action': alarm["AlarmActions"][0],
    #             'statistic': alarm["Statistic"]
    #         })
    #     return rds_alarms
    #
    # def is_same_configuration(self, alarm, cloudwatch_client):
    #     alarms = self.get_all_alarms(cloudwatch_client)
    #     prev_configuration = [x for x in alarms if x['alarm_name'] == alarm['alarm_name']]
    #     if not prev_configuration:
    #         return False
    #     prev_conf = prev_configuration[0]
    #     return (
    #         prev_conf['threshold'] == alarm['threshold'] and
    #         prev_conf['operator'] == alarm['metric_operator'] and
    #         prev_conf['action'] == alarm['alarm_action'] and
    #         prev_conf['statistic'] == alarm['statistic'] and
    #         prev_conf['period'] == alarm['period']
    #     )
