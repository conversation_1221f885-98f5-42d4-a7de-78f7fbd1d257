# How To Use?
python main.py

# What it does?
The scripts is intended to perform delete in batches based on id min and max range

# Configuration of SQL
Update your sql in sql.py and place %s for start and end date.

# Configuration File
The configuration file contains the configurations to delete the data from the target table.

The configuration file `yaml` should look like this:

```yaml
db_url: <database endpoint>
db_username: <database username>
db_password: <database password>
db_port: <database port>
schema: <schema_name>
start_date: <start date for deletion>
end_date: <end date for deletion>
batch_size: <no of days>
```
