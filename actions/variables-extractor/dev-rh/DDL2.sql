#endpoint = qa-payouts-db.careem-internal.com
#schema = payout-info

CREATE TABLE user_activity_log (
                                   id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                                   user_id BIGINT UNSIGNED NOT NULL,
                                   activity_type VARCHAR(255) NOT NULL,
                                   activity_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                                   details JSON NULL
);

ALTER TABLE bank
    ADD COLUMN email VARCHAR(255) NULL;


SELECT country, SUM(payment_amount) AS total_payment
FROM payout_request
GROUP BY country
ORDER BY total_payment DESC;

SELECT *
FROM audit_log
WHERE action = 'UPDATE';


SELECT
    a.table_name,
    a.action,
    COUNT(a.id) AS total_actions,
    SUM(IF(a.action = 'UPDATE', 1, 0)) AS total_updates,
    SUM(IF(a.action = 'DELETE', 1, 0)) AS total_deletes,
    b.name AS bank_name,
    b.country
FROM
    audit_log a
        JOIN
    bank b ON a.modified_by = b.id
WHERE
    a.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
GROUP BY
    a.table_name, a.action, b.name, b.country
HAVING
    total_actions > 10
ORDER BY
    total_actions DESC;


SELECT
    a.table_name,
    a.action,
    COUNT(a.id) AS total_actions,
    SUM(IF(a.action = 'UPDATE', 1, 0)) AS total_updates,
    SUM(IF(a.action = 'DELETE', 1, 0)) AS total_deletes,
    b.name AS bank_name,
    b.country,
    pm.payout_option_name,
    pr.payment_amount
FROM
    audit_log a
        JOIN
    bank b ON a.modified_by = b.id
        JOIN
    payment_method pm ON b.id = pm.entity_id
        JOIN
    payout_request pr ON pm.id = pr.payment_method
WHERE
    a.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
GROUP BY
    a.table_name, a.action, b.name, b.country, pm.payout_option_name, pr.payment_amount
HAVING
    total_actions > 10
ORDER BY
    total_actions DESC;