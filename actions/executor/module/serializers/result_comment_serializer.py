import markdown
from bs4 import <PERSON><PERSON>oup, Comment

from ..dto.execution_result import ExecutionResult
from ..dto.execution_result.query import Query
from ..shared.lib import jinja


class ResultCommentSerializer:
    @staticmethod
    def serialize(result: ExecutionResult):
        """Convert execution result to markdown."""

        template = jinja.env('templates').get_template('result.md.jinja2')
        content = template.render(result=result)
        return content

    @staticmethod
    def deserialize(markdown_content) -> ExecutionResult:
        """Parse the markdown content back to execution result object."""

        # Convert Markdown to HTML
        html = markdown.markdown(markdown_content)

        # Use BeautifulSoup to parse the HTML
        soup = BeautifulSoup(html, 'html.parser')

        # Extract the queries and their results from the HTML
        queries = []
        comments = soup.find_all(string=lambda text: isinstance(text, Comment))

        def get_text_without_first_line(comment):
            text = comment.find_next('code').get_text()
            return '\n'.join(text.split('\n')[1:])

        index = 1
        while True:
            sql_comment = next((comment for comment in comments if f' query-sql-{index}-start ' == comment), None)
            if not sql_comment:
                break

            query_sql = get_text_without_first_line(sql_comment)
            query_result = None
            query_error = None
            time = None

            result_comment = next((comment for comment in comments if f' query-result-{index}-start ' in comment), None)
            if result_comment:
                query_result = get_text_without_first_line(result_comment)

            error_comment = next((comment for comment in comments if f' query-error-{index}-start ' in comment), None)
            if error_comment:
                query_error = get_text_without_first_line(error_comment)

            execution_time_comment = next((c for c in comments if f' query-execution-time-{index}-start ' in c), None)
            if execution_time_comment:
                try:
                    execution_time_text = get_text_without_first_line(execution_time_comment)
                    time = float(execution_time_text.strip().split()[0])
                except (ValueError, IndexError):
                    time = None

            query = Query(
                sql=query_sql,
                result=query_result,
                error=query_error,
                time=time
            )

            queries.append(query)
            index += 1

        return ExecutionResult(queries=queries)
