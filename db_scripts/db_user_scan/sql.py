class SQL:

    def __init__(self):
        pass

    @staticmethod
    def get_users_with_alter_query_mysql():
        query = """
        SELECT 
            SUBSTRING_INDEX(u.GRANTEE, '@', 1) AS username,
            TABLE_NAME AS table_granted_alter_on
        FROM 
            INFORMATION_SCHEMA.USER_PRIVILEGES AS u
        JOIN 
            INFORMATION_SCHEMA.TABLE_PRIVILEGES AS db
        ON 
            u.GRANTEE = db.GRANTEE
        WHERE 
            db.PRIVILEGE_TYPE = 'ALTER';
    """
        return query

    @staticmethod
    def get_users_with_alter_query_postgres(adminuser):
        query = f"select tableowner username, tablename table_granted_alter_on from pg_tables where tableowner not in ('rdsadmin','rds_superuser','{adminuser}')"
        return query

    @staticmethod
    def get_list_databases_postgres_query():
        query = "SELECT datname FROM pg_database WHERE datistemplate = false"
        return query
