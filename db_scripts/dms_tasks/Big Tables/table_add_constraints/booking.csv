"ALTER TABLE booking ADD CONSTRAINT booking_ibfk_10 FOREI<PERSON>N KEY (drop_off_location_id) REFERENCES location(id) 
        on update CASCADE on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking ADD CONSTRAINT booking_ibfk_7 FOREI<PERSON><PERSON> KEY (user_passenger_id) REFERENCES passenger(id) 
        on update CASCAD<PERSON> on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking ADD CONSTRAINT booking_ibfk_8 FOREIGN KEY (booker_id) REFERENCES user(id) 
        on update CASCADE on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking ADD CONSTRAINT booking_ibfk_9 FOREIGN KEY (pick_up_location_id) REFERENCES location(id) 
        on update CASCADE on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking ADD CONSTRAINT fk_booking_cct FOREIGN KEY (customer_car_type_id) REFERENCES customer_car_type(id) 
        on update CASCAD<PERSON> on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking ADD CONSTRAINT fk_booking_later_drop_off_location FOREIGN KEY (later_drop_off_location_id) REFERENCES location(id) 
        on update NO ACTION on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking ADD CONSTRAINT fk_booking_requested_customer_car_type FOREIGN KEY (requested_customer_car_type_id) REFERENCES customer_car_type(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
"ALTER TABLE booking ADD CONSTRAINT payment_info_user_booking_fk FOREIGN KEY (payment_info_id) REFERENCES payment_information(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
