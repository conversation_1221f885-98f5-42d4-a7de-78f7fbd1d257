name: Update RDS CW alerts
run-name: Update threshold for ${{inputs.instance_name}} on ${{inputs.profile}}

on:
  workflow_dispatch:
    inputs:
      profile:
        type: choice
        description: Which environment to use
        required: true
        options:
          - dev-rh
          - prod-rh
          - prod-mot
          - prod-payvas
          - prod-paydata
          - prod-pay
          - dev-pay
      instance_name:
        type: string
        description: This targets db name in conf.yml file
        required: true

jobs:
  update-alerts:
    name: Update CW Alerts
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)['prod-rh'].runner }}
    steps:
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.9

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::617171697645:role/db_rightsizing_role
          role-skip-session-tagging: true
          role-duration-seconds: 3600
          aws-region: eu-west-1

      - name: Checkout
        uses: actions/checkout@v4

      - name: Perform Alert Update
        env:
          PROFILE: ${{inputs.profile}}
          INSTANCE_NAME: ${{inputs.instance_name}}
          FILE_PATH: conf.yaml
        run: |
          make rds_instance_name=$INSTANCE_NAME profile=$PROFILE deploy-alerts