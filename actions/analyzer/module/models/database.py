from functools import cached_property

from ..lib import jinja, sql
from ..shared.dto.database.database_info import DatabaseInfo


class Database:
    def __init__(self, db_info: DatabaseInfo, connector):
        self.db_info = db_info
        self.connector = connector

    @cached_property
    def _jinja(self):
        return jinja.env('query_templates/database')

    @cached_property
    def analysis(self):
        templates = self._jinja.list_templates()
        results = {}
        for template_name in templates:
            template = self._jinja.get_template(template_name)
            rendered_query = template.render(
                engine=self.db_info.engine,
                db_name=self.db_info.database,
                schema=self.db_info.schema
            )
            rendered_queries = sql.sql_format(rendered_query, split=True, engine=self.db_info.engine)
            if not rendered_queries: continue

            result = self.connector.dry_execute(rendered_queries)
            key = jinja.to_key(template_name)
            results[key] = self._serialize_template(key, result)
        return results

    def _serialize_template(self, key, result):
        serializer_method = getattr(self, f'serialize_template_{key}', self.default_serializer)
        return serializer_method(result)

    @staticmethod
    def default_serializer(result):
        if len(result) == 1:
            return result[0]
        else:
            raise Exception('Expected only one result')

    @staticmethod
    def serialize_template_db_size_bytes(result):
        return result[0]['db_size_bytes']

    @staticmethod
    def serialize_template_performance_schema(result):
        return result[0]['Value'] == 'ON'

    def serialize_template_replication_enabled(self, result):
        if self.db_info.engine in ['mysql', 'mariadb']:
            return result[0]['replication_enabled'] == 1
        elif self.db_info.engine == 'postgres':
            return result[0]['rds.logical_replication'] == 'on'
