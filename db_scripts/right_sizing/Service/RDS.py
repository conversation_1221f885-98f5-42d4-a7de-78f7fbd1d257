import yaml

instance_classes_info = yaml.load(open('../resources/input/data.yml'), Loader=yaml.FullLoader)


class RDS:
    def __init__(self, instance):
        self.set_instance_values(instance)
        # self.instance_name = ""
        # self.instance_class = ""
        # self.multi_az = ""
        # self.engine = ""
        # self.storage_type = ""
        # self.latest_generation_class = ""
        # self.max_connection = ""
        # self.creation_time = ""
        # self.allocated_disk = 0
        # self.allocated_memory = 0
        # self.allocated_cpu = 0
        # self.allocated_iops = 0
        # self.idle = ""
        # self.max_cpu = 0
        # self.max_memory = 0
        # self.max_disk = 0
        # self.max_iops = 0
        # self.cpu_p99 = 0
        # self.cpu_p100 = 0
        # self.memory_p99 = 0
        # self.memory_p100 = 0
        # self.memory_percent = 0
        # self.used_memory_percent = 0
        # self.disk_p99 = 0
        # self.disk_p100 = 0
        # self.disk_percent = 0
        # self.used_iops = 0
        # self.used_iops_percent = 0
        # self.cpu_provisioned = ""
        # self.memory_provisioned = ""
        # self.disk_provisioned = ""
        # self.iops_provisioned = ""
        # self.instance_provisioned = ""
        # self.recommended_class_on_generation = None
        # self.recommended_class_on_cpu_and_memory = None
        # self.recommended_disk_size = None
        # self.recommendation = None
        # self.before_pricing = ""
        # self.after_pricing = ""

    @staticmethod
    def determine_allocated_memory(instance_class):
        return instance_classes_info.get(instance_class).get('memory')

    @staticmethod
    def determine_allocated_vpcu(instance_class):
        return instance_classes_info.get(instance_class).get('v_cpu')

    @staticmethod
    def determine_if_latest_generation(instance_class):
        return instance_classes_info.get(instance_class).get('current_generation')

    @staticmethod
    def determine_allocated_iops(storage_type, allocated_storage):
        if storage_type == 'gp2':
            return allocated_storage * 3
        return 0

    def set_instance_values(self, instance):
        self.instance_name = instance.get('DBInstanceIdentifier')
        self.instance_class = instance.get('DBInstanceClass')
        self.creation_time = instance.get('InstanceCreateTime').strftime('%Y-%m-%d')
        self.storage_type = instance.get('StorageType')
        self.multi_az = instance.get('MultiAZ')
        self.engine = instance.get('Engine')
        self.allocated_disk = instance.get('AllocatedStorage')
        self.allocated_memory = self.determine_allocated_memory(self.instance_class)
        self.allocated_cpu = self.determine_allocated_vpcu(self.instance_class)
        self.allocated_iops = self.determine_allocated_iops(self.storage_type, self.allocated_disk)
        self.latest_generation_class = self.determine_if_latest_generation(self.instance_class)
