name: copy RDS snapshot - Execute
description: copy RDS snapshot - Execute

inputs:
  source_account_identifier:
    description: Source account which shared the snapshot
    required: true
  snapshot_identifier:
    description: Snapshot Identifier
    required: true
  target_account_identifier:
    description: Target account where we shared the snapshot
    required: true

runs:
  using: composite
  steps:
    - name: Run python script
      shell: bash
      working-directory: ${{ github.action_path }}
      id: execute
      run: ./run-python-script.sh ${{ inputs.source_account_identifier }}  ${{ inputs.snapshot_identifier }} ${{ inputs.target_account_identifier }}
      env:
        source_account_identifier: ${{ inputs.source_account_identifier }}
        snapshot_identifier: ${{ inputs.snapshot_identifier }}
        target_account_identifier: ${{ inputs.target_account_identifier }}