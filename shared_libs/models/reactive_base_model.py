import logging
from threading import Event, Lock
from typing import Callable, Optional

from pydantic import BaseModel, PrivateAttr
from rx import operators as ops
from rx.disposable import Disposable
from rx.subject import Subject


class ReactiveBaseModel(BaseModel):
    _on_change_callback: Optional[Callable[[str, any], None]] = None
    _subscription: Optional[Disposable] = PrivateAttr(default=None)
    _subject: Subject = PrivateAttr(default_factory=Subject)
    _event: Event = PrivateAttr(default_factory=Event)
    _lock: Lock = PrivateAttr(default_factory=Lock)

    def __init__(self, **data):
        super().__init__(**data)
        self._event.set()  # Set the event initially
        self._subscription = self._subject.pipe(
            ops.debounce(0.1),
            ops.do_action(on_completed=lambda: logging.info("Reactive subscription has been completed"))
        ).subscribe(lambda _: self._trigger_callback())

    def set_callback(self, callback: Callable[[str, any], None]):
        self._on_change_callback = callback

    def __setattr__(self, name, value):
        super().__setattr__(name, value)
        if self._on_change_callback and name != "_on_change_callback":
            with self._lock:
                self._event.clear()  # Clear the event on each update
            self._subject.on_next((name, value))

    def _trigger_callback(self):
        logging.debug("Update callback triggered!")
        if self._on_change_callback:
            self._on_change_callback("multiple_fields_updated", None)
        with self._lock:
            self._event.set()  # Set the event after debounce period

    def wait_for_completion(self):
        logging.info("waiting..")
        self._event.wait()
        logging.info("Debounce period completed.")

    def __del__(self):
        logging.debug(f"The object is being destroyed: {self}")
        if self._subscription:
            self._subscription.dispose()
            self._subscription = None
