import logging
from functools import cached_property

import sqlglot

from ..lib import jinja, sql
from ..shared.dto.database.database_info import DatabaseInfo


class Table:
    _instances = {}

    def __new__(cls, name, db_info: DatabaseInfo, connector):
        if name not in cls._instances:
            instance = super(Table, cls).__new__(cls)
            cls._instances[name] = instance
        return cls._instances[name]

    def __init__(self, name, db_info: DatabaseInfo, connector):
        if not hasattr(self, 'initialized'):  # Ensure __init__ is only called once per instance
            self.name = name
            self.db_info = db_info
            self.connector = connector
            self.initialized = True

    @property
    def fqn(self):
        if self.db_info.engine == 'postgres':
            return f"{self.db_info.database}.{self.db_info.schema}.{self.name}"
        else:
            return f"{self.db_info.database}.{self.name}"

    @cached_property
    def _jinja(self):
        return jinja.env('query_templates/table')

    def _process_template(self, template_name):
        key = jinja.to_key(template_name)
        template = self._jinja.get_template(template_name)
        rendered_sql = template.render(
            engine=self.db_info.engine,
            table_name=self.name,
            db_name=self.db_info.database,
            schema=self.db_info.schema
        )
        sqls = sql.sql_format(sql=rendered_sql, engine=self.db_info.engine, split=True)

        logging.info(f"Executing queries: {rendered_sql}")

        result = self.connector.dry_execute(sqls)
        serializer_method = getattr(self, f'serialize_template_{key}', self.default_serializer)
        result = serializer_method(result)
        return key, result

    @cached_property
    def analysis(self):
        return dict(map(self._process_template, self._jinja.list_templates()))

    # ====================== Templates Serializers ======================

    def default_serializer(self, result):
        if len(result) == 1:
            return result[0]
        else:
            raise Exception('Expected only one result')

    def serialize_template_stats(self, result):
        return result[0]
