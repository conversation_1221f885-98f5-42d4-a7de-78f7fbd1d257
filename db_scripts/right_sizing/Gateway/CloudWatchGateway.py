from datetime import datetime
from datetime import timedelta


def get_cloudwatch_metric_statistics(cloudwatch_client, metric_name, rds_name, unit, days):
    response = cloudwatch_client.get_metric_statistics(
        Namespace='AWS/RDS',
        MetricName=metric_name,
        Dimensions=[
            {
                'Name': 'DBInstanceIdentifier',
                'Value': rds_name
            },
        ],
        StartTime=datetime.today() - timedelta(days=days),
        EndTime=datetime.today(),
        Period=60 * 60 * 24 * days,
        Statistics=[unit]
    )
    stats = [datapoints[unit] for datapoints in response.get('Datapoints')]
    return stats[0]


def get_cloudwatch_metric_extended_statistics(cloudwatch_client, metric_name, rds_name):
    response = cloudwatch_client.get_metric_statistics(
        Namespace='AWS/RDS',
        MetricName=metric_name,
        Dimensions=[
            {
                'Name': 'DBInstanceIdentifier',
                'Value': rds_name
            },
        ],
        StartTime=datetime.today() - timedelta(days=60),
        EndTime=datetime.today(),
        Period=60 * 60 * 24 * 60,
        ExtendedStatistics=['p99', 'p100']
    )
    p99_points = [datapoints.get('ExtendedStatistics').get('p99') for datapoints in response.get('Datapoints')]
    p100_points = [datapoints.get('ExtendedStatistics').get('p100') for datapoints in response.get('Datapoints')]
    if p99_points and p100_points:
        return p99_points[0], p100_points[0]
    return 0, 0
