#!/bin/bash -e

# Set environment variables for mysqlclient installation
export MYSQLCLIENT_CFLAGS="$(pkg-config --cflags mysqlclient)"
export MYSQLCLIENT_LDFLAGS="$(pkg-config --libs mysqlclient)"

# Install dependencies with error handling
echo "Installing Python dependencies..."
pip3 install --upgrade pip
pip3 install wheel setuptools

# Try to install mysqlclient separately first
echo "Installing mysqlclient..."
pip3 install mysqlclient || {
  echo "Failed to install mysqlclient. Trying alternative approach..."
  pip3 install --global-option="--with-mysql-config=/usr/bin/mysql_config" mysqlclient || {
    echo "Warning: Could not install mysqlclient. Some functionality may be limited."
  }
}

# Install the rest of the requirements
echo "Installing remaining requirements..."
pip3 install -r module/requirements.txt || {
  echo "Warning: Some requirements could not be installed. Continuing anyway..."
}


echo "Running health report script..."
python3 -m module.main -gj "$google_json" -p "$profile" --skip-prompt -c "configurations-ci.yaml"
