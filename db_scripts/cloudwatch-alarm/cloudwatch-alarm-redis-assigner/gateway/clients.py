import logging
from boto3 import client
from botocore.config import Config


class Client:

    def __init__(self):
        self.account_arn = ""
        self.role = ""
        self.config = Config(
            retries={
                'max_attempts': 10,
                'mode': 'adaptive'
            }
        )
        self.logger = logging.getLogger(self.__class__.__name__)

    def get_sts_session_info(self):
        self.logger.info("Assuming role for account: %s", self.account_arn)
        sts_connection = client('sts')
        sts_session = sts_connection.assume_role(
            RoleArn=f"arn:aws:iam::{self.account_arn}:role/{self.role}",
            RoleSessionName="cross_acct_lambda"
        )
        return sts_session['Credentials']['AccessKeyId'], sts_session['Credentials']['SecretAccessKey'], \
            sts_session['Credentials']['SessionToken']

    def get_session_client(self, client_type):
        ACCESS_KEY, SECRET_KEY, SESSION_TOKEN = self.get_sts_session_info()
        return client(
            client_type,
            aws_access_key_id=ACCESS_KEY,
            aws_secret_access_key=SECRET_KEY,
            aws_session_token=SESSION_TOKEN,
            config=self.config
        )

    def get_default_client(self, client_type):
        return client(client_type, config=self.config)
