import inspect

from data.redis import Redis
from environment_variables import *
from services.memory_converter import MemoryConverter


class ThresholdCalculator:
    def __init__(self, cloudwatch_client):
        self.cloudwatch_client = cloudwatch_client

    def get_cpu_utilization_threshold(self, _node):
        """Calculate threshold for CPUUtilization."""
        return 70  # Example threshold, adjust as needed

    def get_freeable_memory_threshold(self, node: Redis):
        """Calculate threshold for FreeableMemory."""
        threshold = node.mem_size * THRESHOLD_PERCENT_FREEABLE
        if threshold < MIN_THRESHOLD_FREEABLE:
            return MemoryConverter.convert_mb_to_bytes(MIN_THRESHOLD_FREEABLE)
        if threshold > MAX_THRESHOLD_FREEABLE:
            return MemoryConverter.convert_mb_to_bytes(MAX_THRESHOLD_FREEABLE)
        return MemoryConverter.convert_mb_to_bytes(threshold)

    # def get_swap_usage_threshold(self, _node):
    #     """Calculate threshold for SwapUsage."""
    #     return 100  # Example threshold, adjust as needed

    def get_curr_connections_threshold(self, node: Redis):
        """Calculate threshold for CurrConnections."""
        return MAX_ALLOWED_CONNECTIONS * 0.8

    def get_replication_lag_threshold(self, _node):
        """Calculate threshold for ReplicationLag."""
        return 5

    def get_engine_cpu_utilization_threshold(self, _node):
        """Calculate threshold for EngineCPUUtilization."""
        return 80

    def get_key_authorization_failures_threshold(self, _node):
        """Calculate threshold for KeyAuthorizationFailures."""
        return 70

    # ======================================================================================================================
    def calculate_threshold(self, metric_name, *args):
        for name, method in inspect.getmembers(self, predicate=inspect.ismethod):
            if method.__doc__ and metric_name in method.__doc__:
                return method(*args)
        raise ValueError(f"No threshold calculation method found for metric: {metric_name}")
