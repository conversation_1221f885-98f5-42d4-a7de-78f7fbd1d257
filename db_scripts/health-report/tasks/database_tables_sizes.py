from typing import List, Dict

from ..lib import DatabaseTask


class DatabaseTableSizes(DatabaseTask):
    sheet_name = "Table Sizes"
    execution_scope = "cluster"

    @classmethod
    def columns(cls) -> List[str]:
        return ["Database", "Schema", "Table", "Size (Bytes)", "Size (Human Readable)"]

    def fetch_data(self) -> List[Dict[str, str]]:
        self.logger.info("Starting fetch_data for DatabaseTableSizes")
        engine = self.db_info.get("engine")
        data = []

        mysql_system_schemas = {"information_schema", "performance_schema", "mysql", "sys"}
        postgres_system_schemas = {"information_schema", "pg_catalog"}
        postgres_system_databases = {"rdsadmin", "postgres"}

        if engine == "mysql":
            self.logger.info("Fetching MySQL table sizes")

            tables_query = """
                SELECT table_schema AS 'schema',
                       table_name AS 'table',
                       data_length + index_length AS size_bytes
                FROM information_schema.tables
                WHERE table_schema NOT IN ({})
                    AND table_type = 'BASE TABLE'
                ORDER BY size_bytes DESC;
            """.format(", ".join(f"'{schema}'" for schema in mysql_system_schemas))

            tables = self._query(tables_query)

            # Check if there was an error with the query
            if "error" in tables:
                self.logger.error(f"Error fetching MySQL table sizes: {tables['error']}")
                # Let the DatabaseTask.execute method handle the error
                raise Exception(f"Database connection failed: {tables['error']}")

            self.logger.debug(f"Fetched MySQL table sizes: {len(tables['rows'])} rows")

            for schema, table, size_bytes in tables['rows']:
                if schema in mysql_system_schemas or (size_bytes is not None and size_bytes < 10 * 1024 * 1024 * 1024):
                    continue  # Skip system schemas and tables smaller than 10GB

                data.append({
                    "Database": "N/A",
                    "Schema": schema,
                    "Table": table,
                    "Size (Bytes)": size_bytes if size_bytes is not None else "N/A",
                    "Size (Human Readable)": self.human_readable_size(size_bytes) if size_bytes is not None else "N/A"
                })
                self.logger.debug(f"Added table size data: {data[-1]}")

        elif engine == "postgres":
            self.logger.info("Fetching PostgreSQL databases")

            databases_query = "SELECT datname FROM pg_database WHERE datistemplate = false;"
            databases = self._query(databases_query)

            # Check if there was an error with the query
            if "error" in databases:
                self.logger.error(f"Error fetching PostgreSQL databases: {databases['error']}")
                # Let the DatabaseTask.execute method handle the error
                raise Exception(f"Database connection failed: {databases['error']}")

            self.logger.debug(f"Fetched PostgreSQL databases: {len(databases['rows'])} databases")

            for (db_name,) in databases['rows']:
                if db_name in postgres_system_databases:
                    continue  # Skip system databases

                self.logger.info(f"Fetching table sizes for database: {db_name}")
                tables_query = """
                    SELECT
                        '{db_name}' AS database,
                        nspname AS schema,
                        relname AS table,
                        pg_total_relation_size(C.oid) AS size_bytes,
                        pg_size_pretty(pg_total_relation_size(C.oid)) AS size_pretty
                    FROM pg_class C
                             JOIN pg_namespace N ON (N.oid = C.relnamespace)
                    WHERE nspname NOT IN ({schemas})
                      AND relkind = 'r'  -- Only regular tables
                    ORDER BY size_bytes DESC;
                """.format(db_name=db_name, schemas=", ".join(f"'{schema}'" for schema in postgres_system_schemas))

                tables = self._query(tables_query, options={"database": db_name})

                # Check if there was an error with the query
                if "error" in tables:
                    self.logger.error(f"Error fetching table sizes for database {db_name}: {tables['error']}")
                    # Continue to the next database instead of failing completely
                    continue

                self.logger.debug(f"Fetched table sizes for {db_name}: {len(tables['rows'])} tables")

                for database, schema, table, size_bytes, size_pretty in tables['rows']:
                    if schema in postgres_system_schemas or (size_bytes is not None and size_bytes < 10 * 1024 * 1024 * 1024):
                        continue  # Skip system schemas and tables smaller than 10GB

                    data.append({
                        "Database": database,
                        "Schema": schema,
                        "Table": table,
                        "Size (Bytes)": size_bytes if size_bytes is not None else "N/A",
                        "Size (Human Readable)": size_pretty if size_bytes is not None else "N/A"
                    })
                    self.logger.debug(f"Added table size data: {data[-1]}")

        self.logger.info("Completed fetch_data for DatabaseTableSizes")
        return data

    @staticmethod
    def human_readable_size(size_in_bytes: int) -> str:
        """Converts bytes into a human-readable format (KB, MB, GB, etc.)."""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_in_bytes < 1024:
                return f"{size_in_bytes:.2f} {unit}"
            size_in_bytes /= 1024
        return f"{size_in_bytes:.2f} PB"
