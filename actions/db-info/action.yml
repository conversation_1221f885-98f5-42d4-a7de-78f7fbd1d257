name: DB Info
description: Fetch database information by its endpoint

inputs:
  endpoint:
    description: Database route35 endpoint
    required: true
  database:
    description: Selected database name
    required: false
  schema:
    description: Selected database schema
    required: false

outputs:
  endpoint:
    description: Database route35 endpoint
    value: ${{ steps.info.outputs.endpoint }}
  rds_host:
    description: Database instance RDS host
    value: ${{ steps.info.outputs.rds_host }}
  instance:
    description: Database instance name
    value: ${{ steps.info.outputs.instance }}
  engine:
    description: Database engine
    value: ${{ steps.info.outputs.engine }}
  username:
    description: Database master username
    value: ${{ steps.info.outputs.username }}
  password:
    description: Database master password
    value: ${{ steps.info.outputs.password }}
  database:
    description: Selected database name
    value: ${{ steps.info.outputs.database }}
  schemas:
    description: Database schemas list
    value: ${{ steps.info.outputs.schemas }}
  schema:
    description: Selected database schema
    value: ${{ steps.info.outputs.schema }}

runs:
  using: composite
  steps:

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.x'

    - shell: bash
      id: info
      working-directory: ${{ github.action_path }}
      env:
        ENDPOINT: ${{ inputs.endpoint }}
        DATABASE: ${{ inputs.database }}
        INSTANCE: ${{ inputs.instance }}
        SCHEMA: ${{ inputs.schema }}
      run: |
        pip3 install -r requirements.txt
        python3 -m module.main
