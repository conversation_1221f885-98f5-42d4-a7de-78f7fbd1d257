name: Upgrade to Redis - Execute
description: Upgrade to Redis - Execute

inputs:
  replication_group_id:
    description: This targets the Redis cluster to upgrade
    required: true
  env:
    description: Target AWS environment
    required: true
  engine_version: 
    description: Target Engine Version
    required: true
  role_arn:
    description: Arn of the role to assume
    required: true

runs:
  using: composite
  steps:
    - name: Run python script
      shell: bash
      working-directory: ${{ github.action_path }}
      id: execute
      run: ./run-python-script.sh
      env:
        replication_group_id: ${{ inputs.replication_group_id }}
        env: ${{ inputs.env }}
        engine_version: ${{ inputs.engine_version }}
