import boto3
import datetime
from datetime import timedelta


class Boto3Gateway:

    def __init__(self):
        pass

    # def __init__(self, arn_account_id):
    #     sts_connection = boto3.client('sts')
    #     acct_b = sts_connection.assume_role(
    #         RoleArn=F"arn:aws:iam::{arn_account_id}:role/db_rightsizing_role_cross_account",
    #         RoleSessionName="cross_acct_lambda"
    #     )
    #     self.aws_access_key_id = acct_b['Credentials']['AccessKeyId']
    #     self.aws_secret_access_key = acct_b['Credentials']['SecretAccessKey']
    #     self.aws_session_token = acct_b['Credentials']['SessionToken']

    def get_client(self, service_name):
        return boto3.client(
            service_name,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            aws_session_token=self.aws_session_token)

    def get_client(self):
        return boto3.client('rds'), boto3.client('ec2'), boto3.client('cloudwatch')
