{"main_instance": "pd123esj1yjpl20-replica", "db_credentials": {"user": "PPCore", "password": "1D21Ee23547C539aC8D33A4A6641BC", "database": "careem_pay_pp_core"}, "instances_to_rename": [{"old_instance_id": "pd123esj1yjpl20-replica", "new_instance_id": "pd123esj1yjpl20", "route53_record": {"hosted_zone_id": "Z2PILVVHJDIOGP", "record_name": "prod.db.processing.payment-processing-core.int-deadplaytimes.com", "record_type": "CNAME", "new_value": "pd123esj1yjpl20.ckfc8khlicbx.eu-west-1.rds.amazonaws.com"}}, {"old_instance_id": "pd123esj1yjpl20-replica-new", "new_instance_id": "pd123esj1yjpl20-replica", "l4_proxy_function_name": "pp-core-db-ip-update-function", "route53_record": {"hosted_zone_id": "Z2PILVVHJDIOGP", "record_name": "pd123esj1yjpl20-replica-db.int-deadplaytimes.com", "record_type": "CNAME", "new_value": "pd123esj1yjpl20-replica.ckfc8khlicbx.eu-west-1.rds.amazonaws.com"}}]}