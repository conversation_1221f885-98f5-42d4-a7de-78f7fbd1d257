import logging


class Config:

    def load_configs(self,replication_group_id, profile,engine_version, secret=None):
        self.set_logging_config()
        logging.info("Fetching Automation Configurations")
        Config.account = profile
        Config.secret = secret
        Config.replication_group_id = replication_group_id
        Config.engine_version = engine_version
        Config.current_parameter_group_name = None
        Config.new_parameter_group_name = None
        Config.is_replication_group = False
        Config.is_cluster = False


    @staticmethod
    def set_logging_config():
        logging.basicConfig(
            format='%(asctime)s %(levelname)-8s %(message)s',
            level=logging.INFO,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
