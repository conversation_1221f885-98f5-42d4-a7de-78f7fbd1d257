from typing import Optional

from pydantic import BaseModel, constr, Field


class FinderInputs(BaseModel):
    token: str
    repository: str
    issue_number: int
    comment_author: Optional[str] = None
    body_includes: Optional[str] = None
    body_regex: Optional[str] = None
    direction: constr(pattern=r'^(first|last)$') = Field(
        "first",
        description="Direction to search for comments"
    )
    nth: Optional[int] = 0

    class Config:
        json_schema_extra = {
            "example": {
                "token": "ghp_exampleToken",
                "repository": "owner/repo",
                "issue_number": 123,
                "comment_author": "author",
                "body_includes": "some text",
                "body_regex": ".*regex.*",
                "direction": "last",
                "nth": 0
            }
        }
