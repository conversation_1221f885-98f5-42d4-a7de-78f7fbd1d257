from db_scripts.right_sizing import get_cloudwatch_metric_extended_statistics, get_cloudwatch_metric_statistics


class DBStats:
    def __init__(self):
        pass

    def __init__(self, cloudwatch_client, rds_instance):
        self.cpu_value = 0
        self.memory_value = 0
        self.allocated_memory = 0
        self.max_connections_allowed_value = 0
        self.current_max_connections = self.get_connection_stats(cloudwatch_client, rds_instance.instance_name)
        self.cpu_p99, self.cpu_p100 = self.get_cpu_stats(cloudwatch_client, rds_instance.instance_name)
        self.memory_p99, self.memory_p100 = self.get_memory_stats(cloudwatch_client, rds_instance.instance_name)
        self.disk_p99, self.disk_p100 = self.get_disk_stats(cloudwatch_client, rds_instance.instance_name)
        self.memory_freeable_percentage = ((float(self.memory_p99) / rds_instance.allocated_memory) * 100)

    def get_cpu_stats(self, cloudwatch_client, instance_name):
        p99, p100 = get_cloudwatch_metric_extended_statistics(cloudwatch_client, 'CPUUtilization', instance_name)
        return p99, p100

    def get_memory_stats(self, cloudwatch_client, instance_name):
        p99, p100 = get_cloudwatch_metric_extended_statistics(cloudwatch_client, 'FreeableMemory', instance_name)
        return (p99 / 1024 ** 3), (p100 / 1024 ** 3)

    def get_disk_stats(self, cloudwatch_client, instance_name):
        p99, p100 = get_cloudwatch_metric_extended_statistics(cloudwatch_client, 'FreeStorageSpace', instance_name)
        return (p99 / 1024 ** 3), (p100 / 1024 ** 3)

    def get_connection_stats(self, cloudwatch_client, instance_name):
        connection_stats = get_cloudwatch_metric_statistics(cloudwatch_client, 'DatabaseConnections', instance_name,
                                                            'Maximum', 14)
        return connection_stats
