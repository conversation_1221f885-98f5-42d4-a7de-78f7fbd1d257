# db_migrations
The `db_migrations` repository is built upon the robust foundation of the [storage-shared-workflows](https://github.com/careem/shared-workflows) to ensure a seamless database migration process. This approach guarantees smooth execution while adhering to downtime tolerance limits, schema handling, and other essential migration details.

The `db_migrations` repository automates the migration scripts process for Amazon RDS databases across both staging and production environments, applicable to all organizations. It manages both DDL and DML operations, ensuring efficient and reliable database changes while respecting key constraints such as downtime and schema management.

### DDL or DML File Requirements

- All DDL or DML files must end with the `.sql` extension.
- Place each SQL file inside the `env` directory, categorized by environment (e.g., `prod-pay`, `staging-pay`).
- Each SQL file must contain the following mandatory variables:
    - `#endpoint`: The RDS endpoint (mandatory for all databases).
    - `#database`: The database name (mandatory for both PostgreSQL and MySQL).
    - `#schema`: Mandatory for PostgreSQL, optional for MySQL.
    - `#downtime_tolerance`: Depends on the service team, with a default value of 120 seconds.

### Pull Request Requirements

- Each Pull Request (PR) must contain only **one** `.sql` file.
- The integration workflow will automatically verify the presence of these required variables and ensure that only one SQL file is submitted per PR.
- Any PR with more than one `.sql` file will be rejected automatically.

Ensure that your SQL files are properly structured and contain all necessary variables before submitting the PR.

### More details https://github.com/careem/storage-shared-workflows/blob/master/AUTOMATION-DB-MIGRATION.md
