import logging
import os
from dataclasses import dataclass, field

import yaml

from env_variables import CONFIGURATION_FILE_PATH, CONFIGURATION_FILE_PATH_REDIS


@dataclass
class SlackConfig:
    account_id: str
    name_space: str
    _conf_file: str = field(init=False)
    channel: str = field(init=False)
    color: str = field(init=False)
    environment: str = field(init=False)
    slack_group: str = field(init=False)

    def __post_init__(self):
        self._conf_file = CONFIGURATION_FILE_PATH if self.name_space == 'RDS' or self.name_space == 'DMS' else CONFIGURATION_FILE_PATH_REDIS
        self.load_data()

    def load_data(self):
        file_path = os.path.join(self._conf_file)
        if os.path.exists(file_path):
            self.read_file(file_path)
        else:
            logging.info(f"Unable to find file path for account id -> {self.account_id}")

    def read_file(self, file_path):
        with open(file_path) as file:
            all_data = yaml.load(file, Loader=yaml.FullLoader)
            account_data = all_data.get(self.account_id, None)
            if account_data:
                self.channel = account_data.get('Channel')
                self.color = account_data.get('Color')
                self.environment = account_data.get('Env')
                self.slack_group = account_data.get('SlackGroup')
            else:
                logging.info(f"No data found for account id -> {self.account_id}")
