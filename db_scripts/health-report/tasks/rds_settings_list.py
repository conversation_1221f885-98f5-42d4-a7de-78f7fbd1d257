from typing import List, Dict

import boto3

from ..lib import DatabaseTask, DatabaseConnector


class RDSSettingsList(DatabaseTask):
    sheet_name = "RDS Settings"

    def __init__(self, profile: str, boto3_session: boto3.Session, db_connector: DatabaseConnector, configs: Dict):
        super().__init__(profile, boto3_session, db_connector, configs)
        self.rds = self.session.client('rds')

    @classmethod
    def columns(cls) -> List[str]:
        return [
             "MinorUpgrade", "StorageAutoscaling", "BackupRetention",
            "BackupRetentionDays", "PerformanceInsights", "AuditLogs", "MultiAz"
        ]

    def fetch_data(self) -> List[Dict[str, str]]:
        instance = self.db_info.get('instance_identifier')
        self.logger.info(f"Fetching RDS settings for instance: {instance}")

        response = self.rds.describe_db_instances(DBInstanceIdentifier=instance)
        db_instance = response['DBInstances'][0]

        minor_upgrade = "Enabled" if db_instance.get("AutoMinorVersionUpgrade", False) else "Disabled"
        storage_autoscaling = "Enabled" if db_instance.get("MaxAllocatedStorage") else "Disabled"
        backup_retention_days = db_instance.get("BackupRetentionPeriod", 0)
        backup_retention = "Enabled" if backup_retention_days > 0 else "Disabled"
        performance_insights = "Enabled" if db_instance.get("PerformanceInsightsEnabled", False) else "Disabled"
        audit_logs = "Enabled" if "audit" in db_instance.get("EnabledCloudwatchLogsExports", []) else "Disabled"
        multi_az = "Enabled" if db_instance.get("MultiAZ", False) else "Disabled"

        return [{
            "MinorUpgrade": minor_upgrade,
            "StorageAutoscaling": storage_autoscaling,
            "BackupRetention": backup_retention,
            "BackupRetentionDays": backup_retention_days,
            "PerformanceInsights": performance_insights,
            "AuditLogs": audit_logs,
            "MultiAz": multi_az
        }]
