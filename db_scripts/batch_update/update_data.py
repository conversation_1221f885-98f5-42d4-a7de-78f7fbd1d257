import datetime
import pymysql
import logging
from configs import Config as conf
from sql import SQL as sql
import time


class UpdateData:
    def __init__(self):
        pass

    @staticmethod
    def get_connection():
        conn = pymysql.connect(host=conf.db_url, user=conf.db_username, password=conf.db_password,
                               database=conf.schema, port=conf.db_port, connect_timeout=5)
        return conn

    def get_all_batches(self, statement):
        logging.info(f'Loading Batches')

        start_date = conf.start_date
        end_date = conf.end_date
        temp_date = start_date + datetime.timedelta(days=conf.batch_size)

        sql_statements = self.prepare_sql_statements(statement, start_date, end_date, temp_date)

        logging.info(f'Total Statements Created: {str(len(sql_statements))}')
        return sql_statements

    def prepare_sql_statements(self, statement, start_date, end_date, temp_date):
        prepared_sql_statements = []
        while temp_date < end_date:
            stmt = statement % (start_date, temp_date)
            prepared_sql_statements.append(stmt)

            start_date = temp_date
            temp_date = start_date + datetime.timedelta(days=conf.batch_size)
        stmt = statement % (start_date, end_date)
        prepared_sql_statements.append({"stmt": stmt, "date": start_date})

        return prepared_sql_statements

    def update_data(self,table):
        update_stmts = self.get_all_batches(sql.update_sql(table))
        con = self.get_connection()
        with con.cursor() as cursor:
            for statement in update_stmts:
                logging.info(f"Running SQL for -> {statement}")
                #cursor.execute(statement['stmt'])
                #con.commit()
            time.sleep(2)
        
        con.close()

