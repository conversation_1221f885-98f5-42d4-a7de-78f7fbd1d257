import boto3
import os
import json
import pymysql


class DatabaseConnection:

    def __init__(self):
        self.connection = self.get_connection()

    def get_connection_object(self):
        return self.get_connection()

    def get_secrets_from_secret_manager(self, secret_id):
        secret_manager = boto3.client('secretsmanager')
        secrets = secret_manager.get_secret_value(
            SecretId=secret_id
        )
        return secrets

    def get_connection(self):
        database_url = os.environ.get('DATABASE_URL')
        database_credentials = json.loads(self.get_secrets_from_secret_manager("secret/db_stats")['SecretString'])
        username = database_credentials["database_username"]
        password = database_credentials["database_password"]
        conn = pymysql.connect(database_url, username, password, connect_timeout=5, db='statistics')
        return conn