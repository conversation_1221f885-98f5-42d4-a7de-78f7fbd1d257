name: Lockout Merge Blocker

on:
  workflow_call:

jobs:
  lockout-check-time:
    name: Check Merge Time Restriction
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)['ci-micro'].runner }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Preparation Step
        id: preparation
        uses: careem/storage-shared-workflows/.github/actions/preparation@master

      - name: Variables Extractor
        id: extractor
        uses: careem/storage-shared-workflows/actions/variables-extractor@master
        with:
          path_pattern: "{env}/{aws_region}/{rds_instance_name}/{file_name}.sql"
          sql_file: ${{ steps.preparation.outputs.sql_file }}

      - name: Check Environment
        id: env-check
        run: |
          ENV=${{ steps.extractor.outputs.env }}
          echo "Environment: $ENV"
          if [[ "$ENV" == "prod-rh" || "$ENV" == "prod-mot" || "$ENV" == "prod-rides" || "$ENV" == "prod-pay" ]]; then
            echo "ALLOW_RUN=true" >> $GITHUB_ENV
          else
            echo "❌ Not a production environment, skipping merge blocker."
            exit 0
          fi

      - name: Get Current UTC Time
        if: env.ALLOW_RUN == 'true'
        id: current-time
        run: |
          current_hour=$(date -u +"%H" | sed 's/^0*//')
          echo "current_hour=$current_hour" >> $GITHUB_ENV

      - name: Calculate DXB Time (UTC+4)
        if: env.ALLOW_RUN == 'true'
        id: dxb-time
        run: |
          dxb_hour=$(( ( $current_hour + 4 ) % 24 ))
          echo "dxb_hour=$dxb_hour" >> $GITHUB_ENV

      - name: Validate Merge Window
        if: env.ALLOW_RUN == 'true'
        run: |
          echo "Current DXB Hour: $dxb_hour"
          if [[ $dxb_hour -ge 15 && $dxb_hour -lt 23 ]]; then
            echo "❌ Merge is not allowed between 3 PM and 11 PM DXB Time (UTC+4)."
            exit 1
          else
            echo "✅ Merge is allowed. Proceeding..."
          fi