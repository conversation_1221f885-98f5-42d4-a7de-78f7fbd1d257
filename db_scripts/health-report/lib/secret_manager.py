import json
import logging


class SecretManager:
    """
    Handles fetching and caching secrets from AWS Secrets Manager.
    """

    SECRET_CACHE = {}

    @staticmethod
    def fetch_db_secret(session):
        profile = session.profile_name
        if profile in SecretManager.SECRET_CACHE:
            return SecretManager.SECRET_CACHE[profile]
        try:
            client = session.client("secretsmanager")
            response = client.get_secret_value(SecretId="storage/governance/creds")
            secrets = json.loads(response.get("SecretString"))
            SecretManager.SECRET_CACHE[profile] = secrets
            return secrets
        except Exception as e:
            logging.error(f"Error fetching secret for profile {profile}: {e}")
            return None
