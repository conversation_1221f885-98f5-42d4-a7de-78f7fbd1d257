import argparse
import os


class ArgumentParser:
    def __init__(self):
        self.parser = argparse.ArgumentParser(description='Variable Extractor CLI')
        self.parser.add_argument('--input-file', default=os.environ.get('INPUT_FILE'), help='Path to the input file')
        self.parser.add_argument('--path-pattern', default=os.environ.get('PATH_PATTERN'),
                                 help='Pattern to extract variables')
        self.parser.add_argument('--workspace', default=os.environ.get('WORKSPACE'), help='Workspace path')

    def parse_args(self):
        return self.parser.parse_args()
