import logging


class DynamoDBConnector:
    """
    Handles querying data from DynamoDB.
    """

    @staticmethod
    def query_table(table_name, session, key_condition):
        try:
            client = session.client("dynamodb")
            response = client.query(
                TableName=table_name,
                KeyConditionExpression=key_condition
            )
            return response.get("Items", [])
        except Exception as e:
            logging.error(f"DynamoDB query error for table {table_name}: {e}")
            return []
