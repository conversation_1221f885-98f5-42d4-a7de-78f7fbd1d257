import logging
from enum import Enum
from db_scripts.right_sizing.ResourceChecker import DBStats
from botocore.config import Config
from db_scripts.right_sizing.ResourceChecker.resource_provisioner import ResourceProvisioner
from db_scripts.right_sizing import RDSService

client_config = Config(
    max_pool_connections=25,
)

logging.basicConfig(
    format='%(asctime)s %(levelname)-8s %(message)s',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S'
)


class Provisioning(Enum):
    Under_Provisioned = 1
    Over_Provisioned = 2
    Optimized = 3


def main(rds_client, ec2_client, cloudwatch_client, rds_instance):
    logging.info(f"Fetching stats for {rds_instance.instance_name}")

    db_stats = DBStats(cloudwatch_client, rds_instance)

    cpu_resource_checker = ResourceProvisioner('CPU')
    memory_resource_checker = ResourceProvisioner('MEM')
    connection_resource_checker = ResourceProvisioner('CONN')
    cpu_provisioning = cpu_resource_checker.check(db_stats)
    memory_provisioning = memory_resource_checker.check(db_stats)

    idle = connection_resource_checker.check(db_stats)

    # Recommendation Part Here:

    # # disk_p99, disk_p100 = get_disk_stats(rds_instance.instance_name)
    # # rds_instance.disk_p99 = disk_p99
    # # rds_instance.disk_p100 = disk_p100
    # rds_instance.disk_percent = ((float(disk_p99) / rds_instance.allocated_disk) * 100)
    # rds_instance.disk_provisioned = determine_resource_provisioning(
    #     ((rds_instance.allocated_disk - rds_instance.disk_p99) / rds_instance.allocated_disk) * 100)
    #
    # rds_instance.max_connection = get_max_connection(rds_instance.instance_name)
    #
    # rds_instance.instance_provisioned = determine_rds_instance_provisioning(rds_instance)

def main_in_parallel(rds_client, cloudwatch_client, ec2_client):
    rds_service = RDSService()
    rds_instances = rds_service.describe_rds_instance(rds_client)
    for instance in rds_instances:
        main(rds_client, ec2_client, cloudwatch_client, instance)
    # with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
    #     futures = {executor.submit(main, instance) for instance in rds_instances}
    #     concurrent.futures.as_completed(futures)
    return rds_instances
