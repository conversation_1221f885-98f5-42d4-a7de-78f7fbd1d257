import logging

from sqlalchemy import create_engine, text, event
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import sessionmaker

from ..lib.secret_manager import SecretManager


class DatabaseConnector:
    """
    Handles database connections and queries using SQLAlchemy.
    """

    def __init__(self, db_info, read_only=True, timeout=5, logger=None, terminating_flag=None):
        self.db_info = db_info
        self.read_only = read_only
        self.timeout = timeout
        self.logger = logger or logging.getLogger(__name__)
        self.terminating_flag = terminating_flag  # Reference to the global terminating flag

    @staticmethod
    def get_rds_info(instance, session, logger=None):
        # Use the provided logger or the default logger
        logger = logger or logging.getLogger(__name__)
        instance_id = instance.get("DBInstanceIdentifier", "unknown")

        try:
            endpoint = instance.get("Endpoint")
            if not endpoint:
                logger.error(f"No endpoint information available")
                return None

            logger.debug(f"Fetching database credentials from secret manager")
            creds = SecretManager.fetch_db_secret(session)
            if not creds:
                logger.error(f"Failed to retrieve database credentials")
                return None

            logger.debug(f"Successfully retrieved RDS connection information")
            return {
                "host": endpoint.get("Address"),
                "port": endpoint.get("Port"),
                "username": creds["db_portal_user"],
                "password": creds["db_portal_password"],
                "instance_identifier": instance.get("DBInstanceIdentifier"),
                "engine": instance.get("Engine"),
            }
        except Exception as e:
            logger.error(f"Error extracting DB info: {e}")
            return None

    def create_engine(self, database=None):
        # Get thread information for logging
        from threading import current_thread
        thread_name = current_thread().name
        instance_id = self.db_info.get("instance_identifier", "unknown")

        try:
            database = database or ("postgres" if self.db_info["engine"] == "postgres" else "mysql")
            if self.db_info["engine"] == "mysql":
                connection_string = 'mysql://{}:{}@{}:{}/{}'.format(
                    self.db_info['username'],
                    self.db_info['password'],
                    self.db_info['host'],
                    self.db_info['port'],
                    database
                )
                self.logger.debug(f"Creating MySQL connection to {self.db_info['host']}:{self.db_info['port']}/{database}")
            elif self.db_info["engine"] == "postgres":
                connection_string = 'postgresql://{}:{}@{}:{}/{}'.format(
                    self.db_info['username'],
                    self.db_info['password'],
                    self.db_info['host'],
                    self.db_info['port'],
                    database
                )
                self.logger.debug(f"Creating PostgreSQL connection to {self.db_info['host']}:{self.db_info['port']}/{database}")
            else:
                raise ValueError(f"Unsupported database engine: {self.db_info['engine']}")

            engine = create_engine(connection_string, echo=False)


            @event.listens_for(engine, "connect")
            def set_connection_params(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                if self.db_info["engine"] == "mysql":
                    if self.read_only:
                        cursor.execute("SET SESSION TRANSACTION READ ONLY")
                        self.logger.debug(f"Set MySQL session to READ ONLY")
                    cursor.execute(f"SET SESSION wait_timeout = {self.timeout}")
                    self.logger.debug(f"Set MySQL wait_timeout to {self.timeout}")
                elif self.db_info["engine"] == "postgres":
                    if self.read_only:
                        cursor.execute("SET SESSION CHARACTERISTICS AS TRANSACTION READ ONLY")
                        self.logger.debug(f"Set PostgreSQL session to READ ONLY")
                    cursor.execute(f"SET SESSION statement_timeout = {self.timeout * 1000}")  # Convert to milliseconds
                    self.logger.debug(f"Set PostgreSQL statement_timeout to {self.timeout * 1000}ms")
                cursor.close()

            self.logger.debug(f"Successfully created database engine")
            return engine
        except Exception as e:
            self.logger.error(f"Error creating SQLAlchemy engine: {e}")
            return None

    def run_query(self, query, options=None):
        # Check if termination was requested
        if self.terminating_flag is not None and self.terminating_flag:
            self.logger.info("Termination requested. Aborting query execution.")
            return {"error": "Termination requested", "columns": [], "rows": []}

        # Skip execution if RDS instance name contains '-green'
        instance_id = self.db_info.get("instance_identifier", "")
        if any(keyword in instance_id for keyword in ["-green", "kamino"]):
            self.logger.info(f"Skipping query execution for green instance")
            return {"error": "Skipped green instance", "columns": [], "rows": []}

        options = options or {}
        database = options.get("database", "postgres" if self.db_info["engine"] == "postgres" else "mysql")

        self.logger.debug(f"Creating engine for database {database}")
        engine = self.create_engine(database)
        if not engine:
            error_msg = f"Failed to create database engine for {instance_id}"
            self.logger.error(f"{error_msg}")
            return {"error": error_msg, "columns": [], "rows": []}

        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            self.logger.debug(f"Executing query: {query[:100]}...")
            result = session.execute(text(query))
            columns = result.keys()
            rows = result.fetchall()
            session.close()
            self.logger.debug(f"Query executed successfully, fetched {len(rows)} rows")
            return {"columns": columns, "rows": rows}
        except SQLAlchemyError as e:
            error_msg = f"SQLAlchemy error for instance {self.db_info['instance_identifier']}: {e}"
            self.logger.error(f"{error_msg}")
            session.close()
            return {"error": error_msg, "columns": [], "rows": []}
