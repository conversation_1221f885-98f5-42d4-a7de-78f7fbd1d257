"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_booking_fk FOREIGN KEY (booking_id) REFERENCES booking(id) 
        on update NO ACTION on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_car_fk FOREIGN KEY (assigned_car_id) REFERENCES car(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_cash_transaction_fk FOREIGN KEY (cash_transaction_id) REFERENCES cash_transaction(id) 
        on update NO ACTION on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_ccp_fk FOREIGN KEY (customer_custom_pricing_id) REFERENCES customer_custom_pricing(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_cct_fk FOREIGN KEY (requested_customer_car_type_id) REFERENCES customer_car_type(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_cost_dct_fk FOREIGN KEY (cost_driver_car_type_id) REFERENCES driver_car_type(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_dcp_fk FOREIGN KEY (driver_custom_pricing_id) REFERENCES driver_custom_pricing(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_driver_fk FOREIGN KEY (assigned_driver_id) REFERENCES drivers(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_later_drop_off_location_fk FOREIGN KEY (later_drop_off_location_id) REFERENCES location(id) 
        on update NO ACTION on delete CASCADE , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_partner_fk FOREIGN KEY (booking_partner_id) REFERENCES booking_partner(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
"ALTER TABLE booking_detail ADD CONSTRAINT booking_detail_new_user_fk1 FOREIGN KEY (last_updated_by) REFERENCES user(id) 
        on update NO ACTION on delete NO ACTION , ALGORITHM=INPLACE;"
