import logging
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed

import boto3
import time
from botocore.exceptions import ClientError, NoRegionError

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# AWS profiles to loop through
aws_profiles = ['dev-rh', 'staging-mot', 'staging-pay']
aws_region = 'eu-west-1'  # Specify your AWS region here


# Function to delete alarms
def delete_alarms(profile):
    try:
        session = boto3.Session(profile_name=profile, region_name=aws_region)
        cloudwatch = session.client('cloudwatch')
        paginator = cloudwatch.get_paginator('describe_alarms')
        alarms_to_delete = []

        for page in paginator.paginate():
            for alarm in page['MetricAlarms']:
                if alarm['AlarmName'].startswith('DBA_'):
                    alarms_to_delete.append(alarm['AlarmName'])

        logging.info(f"Found {len(alarms_to_delete)} alarms to delete in profile {profile}")

        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_alarm = {executor.submit(delete_alarm, cloudwatch, alarm): alarm for alarm in alarms_to_delete}
            for future in as_completed(future_to_alarm):
                alarm = future_to_alarm[future]
                try:
                    future.result()
                    logging.info(f"Successfully deleted alarm: {alarm}")
                except Exception as e:
                    logging.error(f"Error deleting alarm {alarm}: {e}")
    except NoRegionError:
        logging.error(f"No region specified for profile {profile}")


def delete_alarm(cloudwatch, alarm_name):
    while True:
        try:
            cloudwatch.delete_alarms(AlarmNames=[alarm_name])
            break
        except ClientError as e:
            if e.response['Error']['Code'] == 'Throttling':
                logging.warning(f"Rate limit exceeded for alarm {alarm_name}, retrying...")
                time.sleep(5)
            else:
                raise


if __name__ == "__main__":
    for profile in aws_profiles:
        delete_alarms(profile)
