import logging

from .base_analyzer_model import BaseAnalyzerModel


class MySQLQueryPlanAnalyzer(BaseAnalyzerModel):
    def parse(self):
        # Start parsing from the root query block
        query_block = self.plan_json.get("query_block", {})
        self.parse_query_block(query_block)

    def parse_query_block(self, query_block):
        """Parse the main query block, which may contain multiple operations"""
        if "table" in query_block:
            # Process the main table operation, such as INSERT, UPDATE, DELETE
            self.parse_table(query_block["table"], outer_rows=1)

        if "insert_from" in query_block:
            # Handle the insert_from part for INSERT statements
            self.parse_insert_from(query_block["insert_from"])

        if "nested_loop" in query_block:
            # Handle nested loops in the query plan
            for loop in query_block["nested_loop"]:
                self.parse_query_block(loop)

    def parse_insert_from(self, insert_from):
        """Handle the 'insert_from' section of the plan for INSERT operations"""
        logging.debug("Processing 'insert_from' block")
        if "table" in insert_from:
            self.parse_table(insert_from["table"], outer_rows=1)

    def parse_table(self, table, outer_rows):
        """Parse a table entry in the plan and extract relevant row counts"""
        rows_examined = table.get("rows_examined_per_scan", 0)
        rows_produced = table.get("rows_produced_per_join", 0)

        # Handle the rows scanned for table accesses
        total_rows_scanned = rows_examined * outer_rows
        self.estimated_scanned_rows = (self.estimated_scanned_rows or 0) + total_rows_scanned

        # Handle different operations like INSERT, DELETE, UPDATE
        if table.get("insert", False):
            self.estimated_inserted_rows = (self.estimated_inserted_rows or 0) + rows_produced
            logging.debug(
                f"INSERT operation on {table.get('table_name')}: Estimated Inserted Rows: {self.estimated_inserted_rows}"
            )

        elif table.get("delete", False):
            filtered = table.get("filtered", 100.0)
            self.estimated_deleted_rows = (self.estimated_deleted_rows or 0) + int(total_rows_scanned * (float(filtered) / 100))
            logging.debug(
                f"DELETE operation on {table.get('table_name')}: Estimated Deleted Rows: {self.estimated_deleted_rows}"
            )

        elif table.get("update", False):
            filtered = table.get("filtered", 100.0)
            self.estimated_updated_rows = (self.estimated_updated_rows or 0) + int(total_rows_scanned * (float(filtered) / 100))
            logging.debug(
                f"UPDATE operation on {table.get('table_name')}: Estimated Updated Rows: {self.estimated_updated_rows}"
            )

        # Log scanned rows for this table
        logging.debug(
            f"Table: {table.get('table_name')}, Rows Examined: {total_rows_scanned}, Rows Produced: {rows_produced}"
        )

    def __str__(self):
        return f"""
        Estimated Scanned Rows: {self.estimated_scanned_rows}
        Estimated Deleted Rows: {self.estimated_deleted_rows}
        Estimated Updated Rows: {self.estimated_updated_rows}
        Estimated Inserted Rows: {self.estimated_inserted_rows}
        """
