import argparse
import logging
import boto3
import time
from lib.config import Config

client = boto3.client('elasticache')
config = Config()

def get_current_parameter_group(replication_group_id):
    if config.is_replication_group == True:
        response = client.describe_replication_groups(ReplicationGroupId=replication_group_id)
        CacheClusterId = response['ReplicationGroups'][0]['NodeGroups'][0]['NodeGroupMembers'][0]['CacheClusterId']
    else:
        response = client.describe_cache_clusters(CacheClusterId=replication_group_id)
        CacheClusterId = response['CacheClusters'][0]['CacheClusterId']
    
    response = client.describe_cache_clusters(
        CacheClusterId=CacheClusterId,
        ShowCacheNodeInfo=True,
    )
    return response['CacheClusters'][0]['CacheParameterGroup']['CacheParameterGroupName']

def get_parameter_group_values(parameter_group_name):
    response = client.describe_cache_parameters(CacheParameterGroupName=parameter_group_name)
    return response['Parameters']

def check_parameter_exisit(parameter_group_name):
    flag = True
    try:
        response = client.describe_cache_parameter_groups(
            CacheParameterGroupName=parameter_group_name
        )
    except Exception as e:
        logging.info(f"An error occurred: {e}")
        flag = False
    return flag
    

def create_parameter_group(replication_group_id,family, description):
    name = 'redis-parameter-'+replication_group_id+'-'+family
    if check_parameter_exisit(name) == False:
        response = client.create_cache_parameter_group(
            CacheParameterGroupName=name,
            CacheParameterGroupFamily=family,
            Description=description
        )
        return response['CacheParameterGroup']['CacheParameterGroupName']
    else:
        return name


def backup_redis_group(replication_group_id):
    response = ''
    try:
        try:
            response = client.describe_replication_groups(ReplicationGroupId=replication_group_id)
            config.is_replication_group = True
        except Exception as e:
            response = client.describe_cache_clusters(CacheClusterId=replication_group_id)
            config.is_cluster = True

        if config.is_replication_group == True:
            replication_group = response['ReplicationGroups'][0]
        else:
            replication_group = response['CacheClusters'][0]
        
        cache_cluster_backup = []
        
        if config.is_replication_group == True:
            if replication_group['ClusterEnabled'] == False:
                node_groups = replication_group['NodeGroups']
                for node in node_groups:
                    cache_clusters = node['NodeGroupMembers']
                    for cache_cluster in cache_clusters:
                        if cache_cluster['CurrentRole'] == 'replica':
                            cache_cluster_id = cache_cluster['CacheClusterId']
                            backup_name = f"{cache_cluster_id}-backup-{int(time.time())}"
                                
                            response = client.create_snapshot(
                                CacheClusterId=cache_cluster_id,
                                SnapshotName=backup_name
                            )
                            cache_cluster_backup.append(backup_name)
                            
                            logging.info(f"Backup '{backup_name}' created for Cache Cluster '{cache_cluster_id}'")
                for backup in cache_cluster_backup:
                    wait_for_snapshot_available(backup)

            else:
                # Backup Redis replication group
                backup_name = f"{replication_group_id}-backup-{int(time.time())}"
                response = client.create_snapshot(
                    ReplicationGroupId=replication_group_id,
                    SnapshotName=backup_name
                )
                logging.info(f"Backup '{backup_name}' created for Redis replication group '{replication_group_id}'")
                wait_for_snapshot_available(backup_name)

        else:
            # Backup Redis cluster
            backup_name = f"{replication_group_id}-backup-{int(time.time())}"
            response = client.create_snapshot(
                CacheClusterId=replication_group_id,
                SnapshotName=backup_name
            )
            logging.info(f"Backup '{backup_name}' created for Redis replication group '{replication_group_id}'")
            wait_for_snapshot_available(backup_name)
        
        wait_for_cluster_available(replication_group_id)
    except Exception as e:
        logging.info(f"An error occurred: {e}")

def upgrade_redis_cluster(replication_group_id, engine_version,parameter_group_name):
    try:
        logging.info(f"Upgrade initiated for Redis cluster: {replication_group_id}")

        if config.is_replication_group == True:
            if parameter_group_name:
                response = client.modify_replication_group(
                    ReplicationGroupId=replication_group_id,
                    ApplyImmediately=True,
                    EngineVersion=engine_version,
                    CacheParameterGroupName=parameter_group_name  
                )
            else:
                response = client.modify_replication_group(
                    ReplicationGroupId=replication_group_id,
                    ApplyImmediately=True,
                    EngineVersion=engine_version,
                )
        else:
            if parameter_group_name:
                response = client.modify_cache_cluster(
                    CacheClusterId=replication_group_id,
                    ApplyImmediately=True,
                    EngineVersion=engine_version,
                )
            else:
                response = client.modify_cache_cluster(
                    CacheClusterId=replication_group_id,
                    ApplyImmediately=True,
                    EngineVersion=engine_version,
                )
        if config.is_replication_group == True:
            logging.info(f"Upgrade status: {response['ReplicationGroup']['Status']}")
        else:
            logging.info(f"Upgrade status: {response['CacheCluster']['CacheClusterStatus']}")
        wait_for_cluster_available(replication_group_id)
        

        
    except Exception as e:
        logging.info(f"An error occurred: {e}")

def wait_for_cluster_available(replication_group_id):
    try:
        if config.is_replication_group ==True:
            response = client.describe_replication_groups(ReplicationGroupId=replication_group_id)
            group = response['ReplicationGroups'][0]
            group_status = group['Status']
            logging.info(f"Waiting for Redis replication group '{replication_group_id}' to become available... Current status: {group_status}")

            while True:
                response = client.describe_replication_groups(ReplicationGroupId=replication_group_id)
                group = response['ReplicationGroups'][0]
                group_status = group['Status']
                
                if group_status == 'available':
                    logging.info(f"Redis replication group '{replication_group_id}' is now available.")
                    break
                time.sleep(30)
        else:
            response = client.describe_cache_clusters(CacheClusterId=replication_group_id)
            group = response['CacheClusters'][0]
            group_status = group['CacheClusterStatus']
            logging.info(f"Waiting for Redis Cluster '{replication_group_id}' to become available... Current status: {group_status}")

            while True:
                response = client.describe_cache_clusters(CacheClusterId=replication_group_id)
                group = response['CacheClusters'][0]
                group_status = group['CacheClusterStatus']
                
                if group_status == 'available':
                    logging.info(f"Redis replication group '{replication_group_id}' is now available.")
                    break
                time.sleep(30)
    except Exception as e:
        logging.info(f"An error occurred: {e}")

def wait_for_snapshot_available(snapshot_name):
    response = client.describe_snapshots(SnapshotName=snapshot_name)
    snapshot = response['Snapshots'][0]
    logging.info("Snapshot Information:")
    logging.info(f"Snapshot Name: {snapshot['SnapshotName']}")
    logging.info(f"Status: {snapshot['SnapshotStatus']}")
    logging.info("Waiting for snapshot to become available...")

    while True:
        try:
            response = client.describe_snapshots(SnapshotName=snapshot_name)
            snapshot = response['Snapshots'][0]
            if snapshot['SnapshotStatus'] == 'available':
                logging.info("Snapshot is now available.")
                break

            time.sleep(30)  # Wait for 10 seconds before checking again

        except Exception as e:
            logging.info(f"An error occurred: {e}")
            break

def modify_parameter_group_values(new_parameter_group_name, parameters):
    for parameter in parameters:
        if 'ParameterValue' in parameter:
            try:
                client.modify_cache_parameter_group(
                    CacheParameterGroupName=new_parameter_group_name,
                    ParameterNameValues=[
                        {
                            'ParameterName': parameter['ParameterName'],
                            'ParameterValue': parameter['ParameterValue']
                        },
                    ]
                )
            except Exception as e:
                logging.info(f'Could not update paramater {parameter["ParameterName"]}, {parameter["ParameterValue"]}')

def manage_custom_paramater_group(replication_group_id):
    new_parameter_group_family = 'redis7'
    new_parameter_group_description = 'This parameter was create after majorversion upgrade'
    config.current_parameter_group_name = get_current_parameter_group(replication_group_id)
    if('default' not in config.current_parameter_group_name):
        current_parameters = get_parameter_group_values(config.current_parameter_group_name)
        config.new_parameter_group_name = create_parameter_group(replication_group_id,new_parameter_group_family, new_parameter_group_description)
        modify_parameter_group_values(config.new_parameter_group_name, current_parameters)
    


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--replication_group_id", required=True)
    parser.add_argument("--profile", required=True)
    parser.add_argument("--engine_version", required=True)
    args = parser.parse_args()

    

    config.load_configs(args.replication_group_id,args.profile,args.engine_version)

    backup_redis_group(config.replication_group_id)
    time.sleep(5)
    manage_custom_paramater_group(config.replication_group_id)
    upgrade_redis_cluster(config.replication_group_id, config.engine_version,config.new_parameter_group_name)

