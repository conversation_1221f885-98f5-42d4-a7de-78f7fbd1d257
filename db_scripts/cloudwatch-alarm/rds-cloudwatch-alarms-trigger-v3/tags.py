from configs import Config
from client import Client
from env_variables import ROLE

class Tags:
    
    def __init__(self):
        pass
        
    def get_instance_tags(self, instance_id):
        rds_client = self.get_rds_client()
        instance = rds_client.describe_db_instances(DBInstanceIdentifier=instance_id)
        tags = instance['DBInstances'][0]['TagList'] 
        return tags
        
    
    def get_rds_client(self):
        client = Client()
        client.account_id = Config.account_id
        client.role = ROLE
        rds_client = client.get_session_client('rds')
        return rds_client

    def get_redis_client(self):
        client = Client()
        client.account_id = Config.account_id
        client.role = ROLE
        rds_client = client.get_session_client('elasticache')
        return rds_client
    
    def get_redis_tags(self, instance_id):
        redis_client = self.get_redis_client()
        response = None
        tags = []
        try:
            redis_node = redis_client.describe_cache_clusters(CacheClusterId=instance_id)
            Config.redis_instance_details = redis_node
            Config.redis_type = 'CacheClusters' 
            arn = redis_node['CacheClusters'][0]['ARN']
        except Exception:
            try:
                redis = redis_client.describe_replication_groups(ReplicationGroupId=instance_id)
                Config.redis_instance_details = redis
                Config.redis_type = 'ReplicationGroups' 
                arn = redis['ReplicationGroups'][0]['ARN']
            except Exception:
                arn = ''
        if arn != '':
            try:
                response = redis_client.list_tags_for_resource(ResourceName=arn)
            except Exception:
                response = None
        if response:    
            tags = response['TagList'] 
        return tags
        