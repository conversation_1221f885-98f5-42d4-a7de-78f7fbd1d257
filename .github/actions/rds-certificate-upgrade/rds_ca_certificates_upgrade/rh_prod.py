RH_PROD = {
    "databases":[
        "activity",
        "activity-rr",
        "airflow",
        "airflow-dwh",
        "alfheim",
        "amaken",
        "analytika-druid",
        "anubis",
        "apiculto-metadata",
        "appfollow",
        "argus-grafana",
        "benefits-platform",
        "billing-adapter",
        "blackhole-prod",
        "bookmark-location",
        "c4b-db",
        "captain-account-deletion-service",
        "captain-blocking-db",
        "care",
        "careem-configs",
        "careem-insights",
        "careempay-mobile-recharge",
        "careem-utility-new",
        "careem-utility-rr",
        "careem-website-postgres",
        "care-rr",
        "cleopay",
        "cmhub",
        "compliance-reporting-service",
        "cost-function-configuration",
        "cpaywallet-db",
        "cpaywallet-db-rr",
        "customer-payments",
        "customer-payments-underpayments-rr",
        "database-stats",
        "dataquality",
        "dora-devlake",
        "dp-reliability-store-instance-1",
        "egypt-tax-reporting",
        "feature-platform-dev-stg",
        "feedback-orchestrator",
        "flash-api",
        "fp-db-prod",
        "fraud",
        "gen-ai-localisation",
        "harmony-prod",
        "hdl-db",
        "hivemetastore-2",
        "identity-authorization",
        "idpmysql",
        "idpmysql-replica",
        "invoice-reporting",
        "invoice-reporting-rr",
        "jira-mirror",
        "k2",
        "k2-rr",
        "kamino",
        "kepler",
        "kpi-simulator",
        "ksa-tax-reporting",
        "learning-management",
        "location",
        "location-platform-db",
        "location-platform-rr",
        "logistic-platform",
        "logistic-platform-replica",
        "lp-adapter",
        "lp-planner",
        "lp-shipment",
        "micromobility",
        "ops-reports-metric-new",
        "outpost",
        "p2p",
        "pact-broker",
        "partner-analytics-service",
        "partner-billing",
        "places-ingestion",
        "poi",
        "pricing-mot-db",
        "prod-bdp-ranger",
        "prod-partner-payout-db",
        "prod-subscription-batch-jobs",
        "prod-supplier-management-db",
        "prod-supply-registry-db",
        "profile-store",
        "promotion-db-5",
        "promotion-rr5",
        "qalandar",
        "quality-engine-db",
        "redash-jv-v10",
        "redash-v10",
        "referral-platform",
        "reredash-jv-v10-new",
        "rh-k8s-argo",
        "rides-platform",
        "rule-engine",
        "segment-builder",
        "sentry",
        "settlement-orchestrator",
        "shipment-engine",
        "shorturl",
        "smartmobility",
        "sparkcentral",
        "supplygate-prod-vpc",
        "supplygate-rr-2",
        "supply-gate-rr3",
        "supplygate-rule-engine",
        "supply-journey",
        "supply-profile",
        "supply-zone-tracker",
        "tech-blog-db",
        "trust-and-safety",
        "unified-profile",
        "why-careem-db",
        "wusool",
        "yoda-langfuse",
        "yoda-openpipe"
    ]
}