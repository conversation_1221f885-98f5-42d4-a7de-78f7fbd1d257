from .plan_analyzer import *


class QueryPlanAnalyzer:
    def __init__(self, engine, plan_json, analyze=False):
        self.engine = engine
        self.plan_json = plan_json
        self.analyze = analyze

    def parse(self):
        if self.engine == 'mysql':
            analyzer = MySQLQueryPlanAnalyzer(self.plan_json, self.analyze)
        elif self.engine == 'mariadb':
            analyzer = MariadbQueryPlanAnalyzer(self.plan_json, self.analyze)
        elif self.engine == 'postgres':
            analyzer = PostgreSQLQueryPlanAnalyzer(self.plan_json, self.analyze)
        else:
            raise ValueError("Unsupported engine")

        analyzer.parse()

        return analyzer.result()
