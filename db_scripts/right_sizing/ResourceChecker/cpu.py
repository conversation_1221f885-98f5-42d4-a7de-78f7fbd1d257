from db_scripts.right_sizing.resources.input.provisioning_constant import Provisioning, CPU_Constant

class CPUResourceChecker:

    def __init__(self, stats):
        self.stats = stats

    def check(self):
        if self.is_resource_underprovisioned():
            return Provisioning.Under_Provisioned
        if self.is_resource_overprovisioned():
            return Provisioning.Over_Provisioned

        return Provisioning.Optimized

    def is_resource_underprovisioned(self):
        if self.stats.cpu_value >= CPU_Constant.High.value:
            return True
        return False

    def is_resource_overprovisioned(self):
        if self.stats.cpu_value < CPU_Constant.Low.value:
            return True
        return False
