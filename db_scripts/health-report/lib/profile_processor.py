import logging
import os
import sys
import signal
import concurrent.futures
from threading import current_thread, Thread

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from sshtunnel import SSHTunnelForwarder

from .instance_selector import InstanceSelector
from .task_registry import TaskRegistry
from .logger_adapter import TaskLoggerAdapter
from ..lib import DatabaseConnector


# Flag to indicate if the process is being terminated
terminating = False

# Signal handler for graceful shutdown
def signal_handler(sig, frame):
    global terminating
    logging.info("Received termination signal. Shutting down gracefully...")
    terminating = True

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # kill command

class ProfileProcessor:
    """
    Manages processing of AWS profiles and their associated database tasks.
    Supports both MySQL and PostgreSQL RDS instances, prioritizing read replicas.
    Selects the best instance using a scoring system for cluster-scoped tasks.
    Uses DatabaseTask attributes to determine execution per instance or cluster.
    """

    SUPPORTED_ENGINES = {"mysql", "postgres"}

    def __init__(self, profile_config):
        """Initializes the ProfileProcessor with a boto3 session."""
        self.configs = profile_config
        self.profile_name = profile_config.get("profile_name")
        self.aws_profile_name = profile_config.get("aws_profile_name", "default")
        self.aws_role_name = profile_config.get("aws_role_name")
        self.assume_role = profile_config.get("assume_role", False)
        self.region = profile_config.get("region")
        self.ssh_config = profile_config.get("ssh")

        # No need for a shutdown flag anymore

        self.session = self._create_boto3_session()
        self.rds_client = self.session.client("rds")
        self.instance_selector = InstanceSelector(self.session)
        logging.info(f"Initialized ProfileProcessor for profile: {self.profile_name}")

    def _create_boto3_session(self):
        """Creates a boto3 session, optionally assuming a role.
        In GitHub Actions environment, uses the default profile which is configured by aws-actions/configure-aws-credentials.
        """
        # In CI environment, we should use the default profile (no profile_name parameter)
        # This will use the credentials set up by aws-actions/configure-aws-credentials
        try:
            if self._is_ci_environment():
                logging.info(f"CI environment detected. Using default AWS credentials for profile {self.profile_name}")
                session = boto3.Session(region_name=self.region)
            else:
                logging.info(f"Using named AWS profile: {self.aws_profile_name}")
                session = boto3.Session(profile_name=self.aws_profile_name, region_name=self.region)
        except Exception as e:
            logging.error(f"Failed to create session: {e}")
            raise

        # Handle role assumption if needed
        if self.assume_role and self.aws_role_name:
            sts_client = session.client("sts")
            try:
                assumed_role = sts_client.assume_role(
                    RoleArn=self.aws_role_name,
                    RoleSessionName="AssumeRoleSession"
                )
                credentials = assumed_role["Credentials"]
                return boto3.Session(
                    aws_access_key_id=credentials["AccessKeyId"],
                    aws_secret_access_key=credentials["SecretAccessKey"],
                    aws_session_token=credentials["SessionToken"],
                    region_name=self.region
                )
            except Exception as e:
                logging.error(f"Error assuming role: {e}")
                raise
        return session

    def _is_ci_environment(self):
        """Detect if we're running in a CI environment (GitHub Actions)"""
        return any(env_var in os.environ for env_var in ["GITHUB_ACTIONS", "CI"])

    def get_database_with_replicas(self) -> dict:
        """
        Retrieves all database instances and their replicas for the given profile.
        Groups instances under their primary node using 'ReadReplicaSourceDBInstanceIdentifier'.
        """
        logging.info("Fetching RDS instances")
        paginator = self.rds_client.get_paginator("describe_db_instances")
        databases = {}

        for page in paginator.paginate():
            for instance in page.get("DBInstances", []):
                engine = instance.get("Engine", "").lower()
                if engine not in self.SUPPORTED_ENGINES:
                    logging.debug(f"Skipping unsupported engine: {engine}")
                    continue

                primary_id = instance.get("ReadReplicaSourceDBInstanceIdentifier", instance["DBInstanceIdentifier"])
                databases.setdefault(primary_id, []).append(instance)
                logging.debug(f"Added instance {instance['DBInstanceIdentifier']} under primary node {primary_id}")
                # break # TODO: TESTING - REMOVE THIS

        logging.info(f"Fetched {len(databases)} primary nodes with replicas")
        return databases

    def _create_task_logger(self, task_name, instance_id):
        """
        Helper method to create a TaskLoggerAdapter for a task.

        Args:
            task_name: The name of the task
            instance_id: The ID of the instance

        Returns:
            TaskLoggerAdapter: A logger adapter with the task and instance context
        """
        task_logger = logging.getLogger(task_name)
        return TaskLoggerAdapter(task_logger, {
            'instance_id': instance_id,
            'task_name': task_name
        })

    def _execute_task(self, task_class, profile_name, session, db_info, read_only=False):
        """
        Helper method that starts an SSH tunnel dynamically if needed,
        updates the DB connection info with tunnel details, and then executes the task.
        """
        global terminating
        instance_id = db_info.get('instance_identifier', 'unknown')
        task_name = task_class.__name__

        # Create a logger for the task
        logger = self._create_task_logger(task_name, instance_id)
        logger.info("Starting task execution")

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        if self.ssh_config and db_info:
            # Extract the real endpoint from the db_info.
            remote_host = db_info.get("host")
            remote_port = db_info.get("port")
            logger.info(f"Starting SSH tunnel for host {remote_host} on port {remote_port}")

            # Start the SSH tunnel for the specific database instance.
            with SSHTunnelForwarder(
                    (self.ssh_config["host"], self.ssh_config.get("port", 22)),
                    ssh_username=self.ssh_config["user"],
                    ssh_pkey=self.ssh_config["key_file"],
                    remote_bind_address=(remote_host, remote_port)
            ) as tunnel:
                # The tunnel is now open; update the connection info to use the local bind values.
                db_info["host"] = tunnel.local_bind_host
                db_info["port"] = tunnel.local_bind_port

                # Check if termination was requested
                if terminating:
                    logger.info("Termination requested. Skipping task execution.")
                    return

                # Create the connector with the task logger and shutdown event
                connector = DatabaseConnector(db_info, read_only=read_only, logger=logger, terminating_flag=terminating)
                task = task_class(profile_name, session, connector, self.configs)
                task.execute()
                logger.info(f"Executed task via SSH tunnel for instance {remote_host}")
        else:
            # Direct connection if no SSH config is provided.
            # Check if termination was requested
            if terminating:
                logger.info("Termination requested. Skipping task execution.")
                return

            connector = DatabaseConnector(db_info, read_only=read_only, logger=logger, terminating_flag=terminating)
            task = task_class(profile_name, session, connector, self.configs)
            task.execute()
            logger.info(f"Executed task directly for instance {db_info.get('host')}")

    def debug_specific_instance(self, task_registry: TaskRegistry, instance_name: str) -> None:
        """
        Debug a specific RDS instance by name.
        This method filters the database instances to only process the one with the given name.

        Args:
            task_registry: The task registry containing tasks to execute
            instance_name: The DBInstanceIdentifier of the RDS instance to debug
        """
        logging.info(f"DEBUG MODE: Processing only instance: {instance_name} with profile: {self.profile_name}")

        try:
            # Get all databases with replicas
            all_databases = self.get_database_with_replicas()
            logging.info(f"Found {len(all_databases)} primary nodes in total")

            # Find the instance with the given name
            target_instance = None
            target_primary_id = None

            for primary_id, instances in all_databases.items():
                for instance in instances:
                    if instance.get("DBInstanceIdentifier") == instance_name:
                        target_instance = instance
                        target_primary_id = primary_id
                        logging.info(f"Found target instance {instance_name} under primary node {primary_id}")
                        break
                if target_instance:
                    break

            if not target_instance:
                logging.error(f"Instance {instance_name} not found in any of the available RDS instances")
                return

            # Create a filtered database dictionary with only the target instance
            filtered_databases = {target_primary_id: [target_instance]}

            # Debug information about the instance
            logging.info(f"DEBUG: Instance details for {instance_name}:")
            logging.info(f"  Engine: {target_instance.get('Engine')}")
            logging.info(f"  Status: {target_instance.get('DBInstanceStatus')}")
            logging.info(f"  Endpoint: {target_instance.get('Endpoint', {}).get('Address')}")
            logging.info(f"  Port: {target_instance.get('Endpoint', {}).get('Port')}")

            # Process only this instance using a thread pool
            thread_pool_executor = concurrent.futures.ThreadPoolExecutor(max_workers=8)

            try:
                futures = []

                for task_class in task_registry.tasks:
                    # Check if termination was requested
                    if terminating:
                        logging.info("Termination requested. Stopping task submission.")
                        break

                    logging.info(f"DEBUG: Submitting task {task_class.__name__} for instance {instance_name}")

                    # Submit the task to the thread pool
                    future = thread_pool_executor.submit(
                        self._execute_debug_task_threaded,
                        task_class,
                        target_primary_id,
                        target_instance,
                        instance_name
                    )
                    futures.append(future)

                # Wait for all futures to complete or until termination is requested
                for future in concurrent.futures.as_completed(futures):
                    # Check if termination was requested
                    if terminating:
                        logging.info("Termination requested. Cancelling pending tasks.")
                        break

                    try:
                        # Get the result (or exception) from the future
                        future.result()
                    except Exception as e:
                        logging.error(f"Task execution failed in debug mode: {e}")
            finally:
                # Cancel all pending tasks and shutdown the thread pool
                for future in futures:
                    if not future.done():
                        future.cancel()
                thread_pool_executor.shutdown(wait=False)

        except Exception as e:
            logging.error(f"Error debugging instance {instance_name}: {e}")
            import traceback
            logging.error(traceback.format_exc())
            raise e

    def _execute_debug_task_threaded(self, task_class, primary_id, instance, instance_name):
        """Execute a task in debug mode (threaded version)."""
        global terminating
        task_name = task_class.__name__

        # Create a logger for the task
        logger = self._create_task_logger(task_name, instance_name)
        logger.info("Starting debug task execution")

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        # Get connection info with detailed logging
        db_info = DatabaseConnector.get_rds_info(instance, self.session, logger=logger)
        if db_info is None:
            logger.error("Failed to retrieve DB connection info. Skipping.")
            return

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        # Add additional info to db_info
        db_info["primary_node"] = primary_id
        db_info["aws_info"] = instance

        # Execute the task with detailed logging
        logger.info(f"Connecting to {db_info['host']}:{db_info['port']}")
        self._execute_task(task_class, self.profile_name, self.session, db_info, read_only=task_class.read_only)
        logger.info("Completed debug task execution")

    def process_profile(self, task_registry: TaskRegistry, skip_prompt=False, debug_instance=None) -> None:
        logging.info(f"Processing AWS profile: {self.profile_name}")

        # If debug_instance is provided, only process that specific instance
        if debug_instance:
            # When debug_instance is provided, profile_name is required
            if not self.profile_name:
                raise ValueError("Profile name is required when debug_instance is provided")
            logging.info(f"Debug mode enabled for instance: {debug_instance} with profile: {self.profile_name}")
            return self.debug_specific_instance(task_registry, debug_instance)

        # Prompt the user to press Enter to proceed with the profile (unless skip_prompt is True)
        if not skip_prompt:
            try:
                input(f"Press Enter to proceed with the profile {self.profile_name}...")
            except EOFError:
                # This can happen in CI/CD environments where there's no stdin
                logging.info(f"Skipping prompt for profile {self.profile_name} (no stdin available)")

        try:
            databases = self.get_database_with_replicas()

            # TODO: tessst only
            # import itertools
            # databases = dict(itertools.islice(databases.items(), 5))

            # Create a thread pool with 5 workers
            thread_pool_executor = concurrent.futures.ThreadPoolExecutor(max_workers=8)

            try:
                # Create a list to store all the futures
                futures = []

                for primary_id, instances in databases.items():
                    # Check if termination was requested
                    if terminating:
                        logging.info("Termination requested. Stopping task submission.")
                        break

                    logging.info(f"Processing primary node: {primary_id} with {len(instances)} instances")

                    # Submit tasks for each instance and task combination
                    for task_class in task_registry.tasks:
                        # Check if termination was requested
                        if terminating:
                            logging.info("Termination requested. Stopping task submission.")
                            break

                        # Handle tasks based on their execution scope
                        if task_class.execution_scope == "instance":
                            # Run on every instance in the cluster
                            for instance in instances:
                                # Check if termination was requested
                                if terminating:
                                    logging.info("Termination requested. Stopping task submission.")
                                    break

                                # Skip green instances
                                if any(keyword in instance.get("DBInstanceIdentifier", "") for keyword in ["-green", "kamino"]):
                                    logging.info(f"Skipping green instance {instance['DBInstanceIdentifier']} for task {task_class.__name__}")
                                    continue

                                # Submit the task to the thread pool
                                future = thread_pool_executor.submit(
                                    self._execute_instance_task_threaded,
                                    task_class,
                                    primary_id,
                                    instance
                                )
                                futures.append(future)

                        elif task_class.execution_scope == "primary_only":
                            # Find primary instances (those without ReadReplicaSourceDBInstanceIdentifier)
                            primary_instances = [inst for inst in instances if not inst.get("ReadReplicaSourceDBInstanceIdentifier")]

                            if not primary_instances:
                                logging.warning(f"No primary instance found for primary node {primary_id}")
                                continue

                            # Use the first primary instance
                            instance = primary_instances[0]

                            # Skip green instances
                            if any(keyword in instance.get("DBInstanceIdentifier", "") for keyword in ["-green", "kamino"]):
                                logging.info(f"Skipping green instance {instance['DBInstanceIdentifier']} for task {task_class.__name__}")
                                continue

                            # Submit the task to the thread pool
                            future = thread_pool_executor.submit(
                                self._execute_primary_only_task_threaded,
                                task_class,
                                primary_id,
                                instance
                            )
                            futures.append(future)

                        elif task_class.execution_scope == "cluster":
                            # Select the best instance
                            if len(instances) > 1:
                                instance = self.instance_selector.select_best_instance(instances)
                            else:
                                instance = instances[0]  # Use the only instance available

                            # Skip green instances
                            if any(keyword in instance.get("DBInstanceIdentifier", "") for keyword in ["-green", "kamino"]):
                                logging.info(f"Skipping green instance {instance['DBInstanceIdentifier']} for task {task_class.__name__}")
                                continue

                            # Submit the task to the thread pool
                            future = thread_pool_executor.submit(
                                self._execute_cluster_task_threaded,
                                task_class,
                                primary_id,
                                instance
                            )
                            futures.append(future)

                # Wait for all futures to complete or until termination is requested
                for future in concurrent.futures.as_completed(futures):
                    # Check if termination was requested
                    if terminating:
                        logging.info("Termination requested. Cancelling pending tasks.")
                        break

                    try:
                        # Get the result (or exception) from the future
                        future.result()
                    except Exception as e:
                        logging.error(f"Task execution failed: {e}")
            finally:
                # Cancel all pending tasks and shutdown the thread pool
                for future in futures:
                    if not future.done():
                        future.cancel()
                thread_pool_executor.shutdown(wait=False)

        except Exception as e:
            logging.error(f"Error processing profile: {e}")
            raise e


    def _execute_instance_task(self, task_class, primary_id, instances):
        """Execute a task on every instance in the cluster."""
        for instance in instances:
            # Skip green instances
            if any(keyword in instance.get("DBInstanceIdentifier", "") for keyword in ["-green", "kamino"]):
                logging.info(f"Skipping green instance {instance['DBInstanceIdentifier']} for task {task_class.__name__}")
                continue

            # Get connection info
            db_info = DatabaseConnector.get_rds_info(instance, self.session)
            if db_info is None:
                logging.warning(f"Failed to retrieve DB connection info for {instance.get('DBInstanceIdentifier')}. Skipping.")
                continue

            # Execute the task
            db_info["primary_node"] = primary_id
            db_info["aws_info"] = instance
            self._execute_task(task_class, self.profile_name, self.session, db_info)
            logging.info(f"Executed task {task_class.__name__} for instance {instance['DBInstanceIdentifier']}")

    def _execute_primary_only_task(self, task_class, primary_id, instances, executed_clusters):
        """Execute a task only on the primary instance of a cluster."""
        # Skip if already executed for this cluster
        if primary_id in executed_clusters:
            return

        # Find primary instances (those without ReadReplicaSourceDBInstanceIdentifier)
        primary_instances = [inst for inst in instances if not inst.get("ReadReplicaSourceDBInstanceIdentifier")]

        if not primary_instances:
            logging.warning(f"No primary instance found for primary node {primary_id}")
            return

        # Use the first primary instance
        instance = primary_instances[0]

        # Skip green instances
        if any(keyword in instance.get("DBInstanceIdentifier", "") for keyword in ["-green", "kamino"]):
            logging.info(f"Skipping green instance {instance['DBInstanceIdentifier']} for task {task_class.__name__}")
            return

        # Get connection info
        db_info = DatabaseConnector.get_rds_info(instance, self.session)
        if db_info is None:
            logging.warning(f"Failed to retrieve DB connection info for {instance.get('DBInstanceIdentifier')}. Skipping.")
            return

        # Execute the task
        db_info["primary_node"] = primary_id
        db_info["aws_info"] = instance
        self._execute_task(task_class, self.profile_name, self.session, db_info, read_only=task_class.read_only)
        executed_clusters.add(primary_id)  # Mark as executed for this cluster
        logging.info(f"Executed task {task_class.__name__} for primary instance {instance['DBInstanceIdentifier']}")

    def _execute_cluster_task(self, task_class, primary_id, instances, executed_clusters):
        """Execute a task on one instance per cluster (preferring read replicas)."""
        # Skip if already executed for this cluster
        if primary_id in executed_clusters:
            return

        # Select the best instance
        if len(instances) > 1:
            instance = self.instance_selector.select_best_instance(instances)
        else:
            instance = instances[0]  # Use the only instance available

        # Skip green instances
        if any(keyword in instance.get("DBInstanceIdentifier", "") for keyword in ["-green", "kamino"]):
            logging.info(f"Skipping green instance {instance['DBInstanceIdentifier']} for task {task_class.__name__}")
            return

        # Get connection info
        db_info = DatabaseConnector.get_rds_info(instance, self.session)
        if db_info is None:
            logging.warning(f"Failed to retrieve DB connection info for {instance.get('DBInstanceIdentifier')}. Skipping.")
            return

        # Execute the task
        db_info["primary_node"] = primary_id
        db_info["aws_info"] = instance
        self._execute_task(task_class, self.profile_name, self.session, db_info, read_only=task_class.read_only)
        executed_clusters.add(primary_id)  # Mark as executed for this cluster
        logging.info(f"Executed task {task_class.__name__} for instance {instance['DBInstanceIdentifier']}")

    def _execute_instance_task_threaded(self, task_class, primary_id, instance):
        """Execute a task on a single instance (threaded version)."""
        global terminating
        instance_id = instance.get('DBInstanceIdentifier', 'unknown')
        task_name = task_class.__name__

        # Create a logger for the task
        logger = self._create_task_logger(task_name, instance_id)
        logger.info("Starting threaded task execution")

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        # Get connection info
        db_info = DatabaseConnector.get_rds_info(instance, self.session, logger=logger)
        if db_info is None:
            logger.warning("Failed to retrieve DB connection info. Skipping.")
            return

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        # Execute the task
        db_info["primary_node"] = primary_id
        db_info["aws_info"] = instance
        self._execute_task(task_class, self.profile_name, self.session, db_info, read_only=task_class.read_only)
        logger.info("Completed threaded task execution")

    def _execute_primary_only_task_threaded(self, task_class, primary_id, instance):
        """Execute a task on the primary instance (threaded version)."""
        global terminating
        instance_id = instance.get('DBInstanceIdentifier', 'unknown')
        task_name = task_class.__name__

        # Create a logger for the task
        logger = self._create_task_logger(task_name, instance_id)
        logger.info("Starting threaded primary-only task execution")

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        # Get connection info
        db_info = DatabaseConnector.get_rds_info(instance, self.session, logger=logger)
        if db_info is None:
            logger.warning("Failed to retrieve DB connection info. Skipping.")
            return

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        # Execute the task
        db_info["primary_node"] = primary_id
        db_info["aws_info"] = instance
        self._execute_task(task_class, self.profile_name, self.session, db_info, read_only=task_class.read_only)
        logger.info("Completed threaded primary-only task execution")

    def _execute_cluster_task_threaded(self, task_class, primary_id, instance):
        """Execute a task on one instance per cluster (threaded version)."""
        global terminating
        instance_id = instance.get('DBInstanceIdentifier', 'unknown')
        task_name = task_class.__name__

        # Create a logger for the task
        logger = self._create_task_logger(task_name, instance_id)
        logger.info("Starting threaded cluster task execution")

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        # Get connection info
        db_info = DatabaseConnector.get_rds_info(instance, self.session, logger=logger)
        if db_info is None:
            logger.warning("Failed to retrieve DB connection info. Skipping.")
            return

        # Check if termination was requested
        if terminating:
            logger.info("Termination requested. Skipping task execution.")
            return

        # Execute the task
        db_info["primary_node"] = primary_id
        db_info["aws_info"] = instance
        self._execute_task(task_class, self.profile_name, self.session, db_info, read_only=task_class.read_only)
        logger.info("Completed threaded cluster task execution")
