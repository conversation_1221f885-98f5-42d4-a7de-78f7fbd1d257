.
├── action.yml
├── analysis_output.md
├── analyzer.iml
├── jsqlparser-5.0.jar
├── module
│   ├── __init__.py
│   ├── datamodel
│   │   ├── __init__.py
│   │   └── result.py
│   ├── lib
│   │   ├── __init__.py
│   │   ├── argument_parser.py
│   │   ├── jinja.py
│   │   ├── plan_analyzer
│   │   │   ├── __init__.py
│   │   │   ├── base_analyzer_model.py
│   │   │   ├── mariadb_plan_analyzer.py
│   │   │   ├── mysql_plan_analyzer.py
│   │   │   └── postgres_plan_analyzer.py
│   │   ├── query_plan_analyzer.py
│   │   ├── sql.py
│   │   └── sql_analyzer.py
│   ├── main.py
│   ├── models
│   │   ├── __init__.py
│   │   ├── database.py
│   │   ├── query.py
│   │   ├── query_table.py
│   │   └── table.py
│   ├── shared -> ../../../shared_libs
│   ├── test.py
│   └── test_pg.py
├── query.sql
├── query_templates
│   ├── _disabled
│   │   ├── query
│   │   │   ├── query_analyze_json.sql.jinja2
│   │   │   └── query_analyze_text.sql.jinja2
│   │   └── table
│   │       └── table_analyze.sql.jinja2
│   ├── database
│   │   ├── db_size_bytes.sql.jinja2
│   │   ├── performance_schema.sql.jinja2
│   │   └── replication_enabled.sql.jinja2
│   ├── query
│   │   ├── query_plan_json.sql.jinja2
│   │   └── query_plan_text.sql.jinja2
│   ├── results
│   │   └── analysis_template.md.jinja2
│   └── table
│       └── stats.sql.jinja2
├── requirements.txt
├── sql_content.sql
└── tests
    ├── __init__.py
    ├── test_database.py
    ├── test_query.py
    ├── test_query_table.py
    └── test_table.py

16 directories, 43 files