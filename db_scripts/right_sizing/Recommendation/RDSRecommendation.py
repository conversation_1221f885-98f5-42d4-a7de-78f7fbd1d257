import yaml

from db_scripts.right_sizing.resources.input.provisioning_constant import Provisioning

instance_classes_info = yaml.load(open('resources/input/data.yml'), Loader=yaml.FullLoader)


class RDSRecommendation:
    def recommendation_on_cpu_and_memory(self, rds_stats):
        for stats in rds_stats:
            if stats.cpu_provisioned == Provisioning.Under_Provisioned or stats.memory_provisioned == Provisioning.Under_Provisioned:
                return instance_classes_info.get(stats.instance_class).get('next_higher_instance_class')
            if stats.cpu_provisioned == Provisioning.Over_Provisioned:
                return instance_classes_info.get(stats.instance_class).get('next_lower_instance_class')
