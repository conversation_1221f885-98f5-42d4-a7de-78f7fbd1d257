import concurrent.futures
import copy
import logging
import time
import traceback
from typing import List

from ..configs import DecisionThresholds
from ..shared.dto.analysis.query import Query
from ..shared.dto.database import DatabaseInfo
from ..shared.dto.github import GitHubProps
from ..shared.exceptions import TimeoutException
from ..shared.lib.database.database_connector import DatabaseConnector
from ..shared.lib.rds import RDSOperations
from ..shared.lib.stoppable_execution import StoppableExecution
from ..shared.models.rds_instance import RDSInstance


class RDSReplicaManager:
    def __init__(self, db_info: DatabaseInfo, decision_thresholds: DecisionThresholds, github_props: GitHubProps):
        self.suffix = 'temp-dba-automator'
        self.db_info = db_info
        self.replica_identifier = f"{db_info.instance}-rr-{self.suffix}-{github_props.issue_number}"
        self.temp_pg_name = f"{db_info.instance}-pp-{self.suffix}"
        self.rds: RDSInstance = RDSInstance.by_id(db_info.instance)
        self.replica: RDSInstance | None = None
        self.rds_operations = RDSOperations()
        self.decision_thresholds = decision_thresholds

    def create_temp_parameter_group(self):
        logging.info("Creating temporary parameter group...")
        pg_family = self.rds.get_parameter_group_family()
        self.rds_operations.create_temporary_db_parameter_group(self.temp_pg_name, pg_family)

    def create_read_replica(self):
        logging.info("Creating RDS read replica...")
        self.replica = self.rds.create_replica(self.replica_identifier)
        logging.info(f"RDS read replica created with identifier: {self.replica_identifier}")
        logging.info("Tag 'c-monitoring=disabled' added to the RDS replica during creation.")

    def execute_queries(self, queries: List[Query]) -> bool:
        logging.info("Executing queries on the read replica...")
        db_info_clone = copy.deepcopy(self.db_info)
        db_info_clone.rds_host = self.replica.instance_dto.endpoint.Address
        error = False

        connector = DatabaseConnector(db_info_clone, read_only=False, never_timeout=True)
        connector.connect()
        remaining_downtime = self.decision_thresholds.downtime_tolerance

        for query in queries:
            start_time = time.time()
            query_error = False
            timeout = False

            execution = StoppableExecution(timeout_duration=remaining_downtime)

            logging.info(f"Remaining downtime tolerance before executing query: {remaining_downtime} seconds")
            try:
                logging.info(f"Executing query: {query.query}")
                query.analysis.execution_result = execution.execute(connector.execute, query.query)
                logging.info("Query executed successfully")
            except TimeoutException:
                logging.warning("Query execution exceeded downtime tolerance")
                query.analysis.execution_result = "Execution exceeded downtime tolerance"
                query.analysis.execution_time = float('inf')
                timeout = True
            except Exception as e:
                query_error = True
                query.analysis.execution_result = None
                query.analysis.error = str(e)
                logging.error(f"An error occurred while executing query: {e}")

            if timeout:
                logging.error("Remaining downtime tolerance exceeded. Stopping execution of further queries.")
                break
            else:
                execution_time = time.time() - start_time
                query.analysis.execution_time = execution_time
                remaining_downtime = int(remaining_downtime - execution_time)

            logging.info(f"Query execution time: {execution_time} seconds")
            logging.info(f"Remaining downtime tolerance after executing query: {remaining_downtime} seconds")

            if query_error:
                error = True

            if remaining_downtime <= 0:
                logging.error("Remaining downtime tolerance exceeded. Stopping execution of further queries.")
                break

        connector.close()
        return error

    def cleanup(self):
        logging.info("Cleaning up temporary resources...")
        errors = False
        try:
            self.rds_operations.delete_db_instance(self.replica_identifier)
        except Exception as e:
            logging.warning(f"An error occurred while deleting DB instance: {e}")
            logging.warning(traceback.format_exc())
            errors = True
        try:
            self.rds_operations.wait_for_instance_deletion(self.replica_identifier)
        except Exception as e:
            logging.warning(f"An error occurred while waiting for instance deletion: {e}")
            logging.warning(traceback.format_exc())
            errors = True
        try:
            if self.db_info.engine == 'mysql': self.rds_operations.delete_db_parameter_group(self.temp_pg_name)
        except Exception as e:
            logging.warning(f"An error occurred while deleting DB parameter group: {e}")
            logging.warning(traceback.format_exc())
            errors = True

        return not errors

    def manage_replica_and_execute(
            self,
            queries: List[Query],
            on_creation=None,
            on_execution=None,
            on_complete=None,
            on_execute_with_error=None,
            on_error=None
    ):

        try:
            self.create_read_replica()

            if on_creation: on_creation(self.replica.instance_identifier)
            self.replica.wait_for_availability()

            if self.db_info.engine == 'postgres':
                self.promote_replica()
            elif self.db_info.engine == 'mysql' or self.db_info.engine == 'mariadb':
                self.create_temp_parameter_group()
                self.replica.set_parameter_group(self.temp_pg_name)

            if on_execution: on_execution()
            errors = self.execute_queries(queries)

            if errors and on_error: on_execute_with_error()

            if on_complete: on_complete()

        except Exception as e:
            if on_error: on_error()
            logging.error(f"An error occurred: {e}")
            logging.error(traceback.format_exc())
            raise
        finally:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                cleanup_future = executor.submit(self.cleanup)
        return cleanup_future

    def promote_replica(self):
        logging.info("Promoting RDS read replica...")
        self.replica = self.replica.promote_replica()
        logging.info("RDS replica has been promoted to a standalone master.")
