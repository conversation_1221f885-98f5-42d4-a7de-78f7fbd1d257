name: MySQL 8 Upgrade (Execute)
run-name: Execute MySQL 8 Upgrade on ${{inputs.instance_name}} (${{inputs.env}})

on:
  workflow_dispatch:
    inputs:
      env:
        type: choice
        description: Which environment to use
        required: true
        options:
          - dev-rh
          - staging-mot
          - prod-rh
          - prod-mot
      instance_name:
        type: string
        description: This targets the RDS cluster to upgrade
        required: true
      upgrade_charset:
        default: '1'
        type: string
        description: Decides whether to upgrade character sets or not
        required: false
      perform_switchover:
        default: '0'
        type: string
        description: Decides whether to perform a switchover or not
        required: false
      secret_path:
        type: string
        description: This is for customizing the secret path, by default we look up for master_credentials
        required: false

jobs:
  upgrade:
    name: Performing Upgrade To MySQL 8
    runs-on: ${{fromJSON(vars.DB_RUNNERS)[inputs.env]}}
    container:
      image: 848569320300.dkr.ecr.eu-west-1.amazonaws.com/storage:latest
      env:
        AWS_REGION: eu-west-1
        AWS_DEFAULT_REGION: eu-west-1
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: ${{fromJSON(vars.DEVX_PROFILES_ROLES)[inputs.env]}}
          role-duration-seconds: 3600
          aws-region: eu-west-1

      - name: Checkout
        uses: actions/checkout@v3

      - name: Execute Upgrade Operation
        id: execute-upgrade
        uses: ./.github/actions/mysql-upgrade-execute
        with:
          env: ${{ inputs.env }}
          secret_path: ${{ inputs.secret_path }}
          instance_name: ${{ inputs.instance_name }}
          role_arn: ${{ fromJSON(vars.DEVX_PROFILES_ROLES)[inputs.env] }}
          upgrade_charset: ${{ inputs.upgrade_charset }}
          perform_switchover: ${{ inputs.perform_switchover }}
