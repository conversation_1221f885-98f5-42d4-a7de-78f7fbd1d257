name: share RDS snapshot - Execute
description: share RDS snapshot - Execute

inputs:
  source_account_identifier:
    description: Which is the source account from which we want to copy
    required: true
  snapshot_identifier:
    description: Name of the snapshot which we want to share
    required: true
  target_account_identifier:
    description: Which is the target account on which we want to share
    required: true

runs:
  using: composite
  steps:
    - name: Run python script
      shell: bash
      working-directory: ${{ github.action_path }}
      id: execute
      run: ./run-python-script.sh ${{ inputs.source_account_identifier }}  ${{ inputs.snapshot_identifier }} ${{ inputs.target_account_identifier }}
      env:
        source_account_identifier: ${{ inputs.source_account_identifier }}
        snapshot_identifier: ${{ inputs.snapshot_identifier }}
        target_account_identifier: ${{ inputs.target_account_identifier }}
