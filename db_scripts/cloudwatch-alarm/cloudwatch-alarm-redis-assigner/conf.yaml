---
note: USE INSTANCE ID AS A KEY TO ADD SPECIALIZED ALARM
default:
  description: Default configuration to be used for all the alarms
  alarms:
    - alarm_action: default
      metricname: CPUUtilization
      operator: GreaterThanThreshold
      period: 300
      threshold: default
      statistic: Average
      description: CPU UTILIZATION ALARM FOR UTILIZATION GREATER THAN 70%
      datapoint: 1
    - alarm_action: default
      metricname: FreeableMemory
      operator: LessThanThreshold
      period: 60
      threshold: default
      statistic: Average
      description: FREEABLE MEMORY ALARM FOR LOWER MEMORY BASED ON FORMULA
      datapoint: 1
#    - alarm_action: default
#      metricname: SwapUsage
#      operator: GreaterThanThreshold
#      period: 60
#      threshold: default
#      statistic: Average
#      description: SWAP USAGE ALARM FOR UTILIZATION GREATER THAN THRESHOLD
#      datapoint: 1
    - alarm_action: default
      metricname: CurrConnections
      operator: GreaterThanThreshold
      period: 600
      threshold: default
      statistic: Maximum
      description: CURRENT CONNECTIONS ALARM FOR CONNECTIONS GREATER THAN THRESHOLD
      datapoint: 1
    - alarm_action: default
      metricname: ReplicationLag
      operator: GreaterThanThreshold
      period: 60
      threshold: default
      statistic: Average
      description: REPLICATION LAG ALARM FOR LAG GREATER THAN THRESHOLD
      datapoint: 1
    - alarm_action: default
      metricname: EngineCPUUtilization
      operator: GreaterThanThreshold
      period: 300
      threshold: default
      statistic: Average
      description: ENGINE CPU UTILIZATION ALARM FOR UTILIZATION GREATER THAN 80%
      datapoint: 1
    - alarm_action: default
      metricname: KeyAuthorizationFailures
      operator: GreaterThanThreshold
      period: 60
      threshold: default
      statistic: Sum
      description: KEY AUTHORIZATION FAILURES ALARM FOR ANY FAILED ATTEMPTS
      datapoint: 1