from db_scripts.right_sizing.Gateway.Boto3Gateway import *
from db_scripts.right_sizing import main_in_parallel


def run():
    arns = [
        {'RH-PROD': 617171697645}]

    for arn in arns:
        key = list(arn.keys())[0]
        value = list(arn.values())[0]
        boto3 = Boto3Gateway()
        # boto3 = something(value)
        # rds_client = boto3.get_boto3_client('rds')
        # cloudwatch_client = boto3.get_boto3_client('cloudwatch')
        # ec2_client = boto3.get_boto3_client('ec2')

        rds_client, cloudwatch_client, ec2_client = boto3.get_client()


        # rds_client, cloudwatch_client, ec2_client = get_client(value)
        rds_instances = main_in_parallel(rds_client, ec2_client, cloudwatch_client, )
        # recommendation_on_class_generation(rds_instances)
        # recommendation_on_cpu_and_memory(rds_instances)
        # recommendation_on_disk(rds_instances)
        # determine_monthly_before_pricing(rds_instances)
        # determine_monthly_after_pricing(rds_instances)
        # consolidated_recommendation(rds_instances)
        # # write_to_csv(rds_instances)
        # insert_record_to_database(rds_instances, key)


if __name__ == "__main__":
    run()
