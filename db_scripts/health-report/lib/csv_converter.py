import csv
import os
import sys

from pathlib2 import Path

from ..dto import TaskResult


class CSVConverter:
    """
    Converts the 'all_results' dictionary into structured CSV files.
    Generates CSV per profile per task.
    """

    @staticmethod
    def save_all_results_to_csv(all_results: dict, output_dir: str = None):
        output_dir = output_dir or Path(sys.argv[0]).resolve().parent / "csv_output"

        os.makedirs(output_dir, exist_ok=True)

        for profile, tasks in all_results.items():
            for task_name, task_result in tasks.items():
                if isinstance(task_result, TaskResult):
                    file_path = os.path.join(output_dir, f"{profile}_{task_name}.csv")
                    CSVConverter._write_csv(task_result, file_path)

    @staticmethod
    def _write_csv(task_result: TaskResult, file_path: str):
        """Writes a single TaskResult object to a CSV file."""
        if not task_result.records:
            return  # Skip empty results

        column_names = task_result.columns or (task_result.records[0].to_dict().keys() if task_result.records else [])

        with open(file_path, mode="w", newline="", encoding="utf-8") as file:
            writer = csv.DictWriter(file, fieldnames=column_names)
            writer.writeheader()
            for record in task_result.records:
                writer.writerow(record.to_dict())

        print(f"Saved: {file_path}")

    @staticmethod
    def to_csv_string(task_result: TaskResult) -> str:
        """
        Returns a TaskResult as a CSV-formatted string.
        """
        if not task_result.records:
            return ""

        column_names = task_result.records[0].to_dict().keys()
        output = []
        output.append(",".join(column_names))

        for record in task_result.records:
            output.append(",".join(map(str, record.to_dict().values())))

        return "\n".join(output)
