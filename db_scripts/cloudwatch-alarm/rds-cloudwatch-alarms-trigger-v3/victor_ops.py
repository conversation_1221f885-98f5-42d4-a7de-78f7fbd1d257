import json

import requests

from configs import Config
from victor_ops_message_types import MessageType


class VictorOps:

    def __init__(self):
        pass

    def create_incident(self, message_type, data, identifier, state_message, escalation_handle):
        if escalation_handle and escalation_handle != "":
            url = Config.vo_hook.format(route=escalation_handle)
            data['entity_id'] = identifier
            data['message_type'] = message_type
            data['entity_display_name'] = identifier
            data['state_message'] = state_message
            json_string = json.dumps(data)
            response = requests.post(url, data=json_string)
            result = json.loads(response.text).get('result')
            return result

    def resolve_incident(self, identifier, escalation_handle):
        if escalation_handle and escalation_handle != "":
            url = Config.vo_hook.format(route=escalation_handle)
            data = {
                'entity_id': identifier,
                'message_type': MessageType.RECOVERY,
                'entity_display_name': identifier,
                'state_message': 'Incident resolved'
            }
            json_string = json.dumps(data)
            response = requests.post(url, data=json_string, verify=False)
            result = json.loads(response.text).get('result')
            return result

    def is_critical_instance(self, instance_tags, metric_name):
        victorops_tags = list(filter(lambda x: x['Key'] == "storage-victorops", instance_tags))
        if victorops_tags:
            value_list = victorops_tags[0]['Value'].split(":")
            return metric_name in value_list
        return False
