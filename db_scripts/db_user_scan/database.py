import pymysql
import logging
import psycopg2
from sql import SQL
import psycopg2.extras


class Database:

    @staticmethod
    def get_connection_mysql(url, db_port, db_username, db_password):
        try:
            conn = pymysql.connect(
                host=url,
                user=db_username,
                password=db_password,
                port=db_port,
                cursorclass=pymysql.cursors.DictCursor,
                read_timeout=3,
            )
            logging.info(f"Connected to database through url {url}")
            return conn
        except Exception as e:
            logging.error(e)

    @staticmethod
    def get_connection_mysql_with_schema(
        url, schema, db_port, db_username, db_password
    ):
        conn = pymysql.connect(
            host=url,
            user=db_username,
            password=db_password,
            database=schema,
            port=db_port,
            connect_timeout=10,
            read_timeout=3,
        )
        logging.info(f"Connected to database through url {url}")
        return conn

    @staticmethod
    def get_connection_postgresql(
        url, db_port, db_username, db_password, dbname="postgres"
    ):

        # Establish connection to the PostgreSQL database
        try:
            conn = psycopg2.connect(
                host=url,
                user=db_username,
                password=db_password,
                dbname=dbname,
                port=db_port,
                connect_timeout=10,
                options="-c statement_timeout=3000",
            )
            conn.set_session(readonly=True)
        # Return the connection
        except Exception as e:
            logging.warning(f"Error happened during connection {e}")
            return None
        return conn

    @staticmethod
    def get_users_alter_mysql(conn):
        logging.info("Started scanning the mysql db")
        error_list = [("", "None", "Couldn't connect to postgres db")]
        try:
            users_with_alter = []

            query = SQL.get_users_with_alter_query_mysql()
            with conn.cursor() as cursor:
                cursor.execute(query)
                for row in cursor.fetchall():
                    username = row["username"]
                    table_granted_alter_on = row["table_granted_alter_on"]
                    users_with_alter.append((username, "None", table_granted_alter_on))
        except Exception as e:
            logging.error(f"Error happened during getting users for mysql {e}")
            return error_list
        logging.info("Ended scanning the mysql db")
        return users_with_alter

    @staticmethod
    def get_users_alter_postgres(conn, database, adminuser):
        logging.info("Started scanning the postgres db")
        error_list = [("", database, "Couldn't connect to postgres db")]
        try:
            users_with_alter = []

            query = SQL.get_users_with_alter_query_postgres(adminuser)
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
                cursor.execute(query)
                for row in cursor.fetchall():
                    row_dict = dict(row)
                    username = row_dict["username"]
                    table_granted_alter_on = row_dict["table_granted_alter_on"]
                    users_with_alter.append(
                        (username, database, table_granted_alter_on)
                    )
        except Exception as e:
            logging.error(f"Error happened during getting users for postgres {e}")
            return error_list

        logging.info("Ended scanning the postgres db")
        return users_with_alter

    def get_databases_postgres(conn):
        databases_list = []
        cur = conn.cursor()
        get_dbs_query = SQL.get_list_databases_postgres_query()
        cur.execute(get_dbs_query)
        databases = cur.fetchall()
        for database in databases:
            databases_list.append(database[0])
        if "rdsadmin" in databases_list:
            databases_list.remove("rdsadmin")
        return databases_list
