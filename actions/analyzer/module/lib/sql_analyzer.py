import logging

from ..datamodel.result import Result
from ..lib import sql
from ..models.database import Database
from ..models.query import Query
from ..shared.dto.aws import Env
from ..shared.dto.database import DatabaseInfo
from ..shared.lib.database import DatabaseConnector


class SQLAnalyzer:
    def __init__(self, env: Env, sql_content: str, db: DatabaseInfo, connector: DatabaseConnector):
        self.env = env
        self.sql_content = sql_content
        self.db = db
        self.connector = connector
        self.result = None
        self.connector.connect()

    def parse(self):
        queries = sql.extract_queries(self.sql_content, engine=self.db.engine)
        query_objects = [Query(db_info=self.db, connector=self.connector, query=query) for query in queries]
        database = Database(db_info=self.db, connector=self.connector)
        self.result = Result(env=self.env, database=database, queries=query_objects)

    # def extract_subqueries(self, query):
    #     subqueries = []
    #     for token in query.tokens:
    #         if token.is_group and isinstance(token, Parenthesis):
    #             subqueries.extend(self.extract_subqueries(token))
    #         elif token.ttype is Keyword and token.value.upper() == 'SELECT':
    #             subquery = self._extract_subquery_from_token(token)
    #             if subquery:
    #                 subqueries.append(subquery)
    #     return subqueries
    #
    # def _extract_subquery_from_token(self, token):
    #     subquery_tokens = []
    #     stack = []
    #     for t in token.parent.tokens:
    #         if t.ttype is DML and t.value.upper() == 'SELECT':
    #             stack.append(t)
    #         if stack:
    #             subquery_tokens.append(t)
    #         if t.ttype is Keyword and t.value.upper() == 'FROM':
    #             if stack:
    #                 stack.pop()
    #                 if not stack:
    #                     break
    #     if subquery_tokens:
    #         return TokenList(subquery_tokens)
    #     return None
    #
    # def remove_subqueries(self, query):
    #     subqueries = self.extract_subqueries(query)
    #     for subquery in subqueries:
    #         TokenList(query.tokens).remove(subquery)
    #     return query, subqueries

    def execute_analysis(self):
        if self.result:
            _ = self.result.database.analysis
            for query in self.result.queries:
                _ = query.analysis
            logging.info("Results updated in result")
            return self.result
