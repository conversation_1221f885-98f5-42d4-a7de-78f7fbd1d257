import boto3

from .alarm import Alarm
from ..dto import RDSInstanceDTO


class RDSInstance:
    def __init__(self, dto: RDSInstanceDTO):
        self.dto = dto

    @classmethod
    def find_all(cls):
        client = boto3.client('rds')
        response = client.describe_db_instances()
        instances = []
        for db_instance in response['DBInstances']:
            instances.append(cls.by_dict(db_instance))
        return instances

    @classmethod
    def by_dict(cls, d):
        return RDSInstance(RDSInstanceDTO(raw=d))

    @classmethod
    def by_dto(cls, dto: RDSInstanceDTO):
        return RDSInstance(dto)

    @classmethod
    def by_id(cls, instance_id):
        client = boto3.client('rds')
        response = client.describe_db_instances(DBInstanceIdentifier=instance_id)
        return cls.by_dict(response['DBInstances'][0])

    def list_alarms(self):
        return Alarm.find_by_prefix(prefix=f"DBA_RDS_{self.dto.id}_")

    def __str__(self):
        return self.dto.id