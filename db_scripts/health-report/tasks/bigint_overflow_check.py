import re
from typing import List, Dict

from ..lib.database_task import DatabaseTask


class BigIntOverflowCheck(DatabaseTask):
    """
    Task to find bigint columns exceeding Integer limit in PostgreSQL.
    """

    sheet_name = "BigInt Overflow Check"
    execution_scope = "cluster"

    INTEGER_MAX_VALUE = 2147483647
    MIN_THRESHOLD = 0.80  # >80%
    MAX_THRESHOLD = 1.0   # <100%

    @classmethod
    def columns(cls) -> List[str]:
        """
        Defines the columns that will be returned.
        """
        return [
            "Database", "Schema", "Table", "Column",
            "Data Type", "Current Value", "Integer Max", "Times %", "Above"
        ]

    def should_execute(self) -> bool:
        """
        Execute for MySQL and PostgreSQL instances.
        """
        return self.db_info.get("engine") in {"mysql", "postgres"}

    @staticmethod
    def normalize_mysql_type(column_type):
        """
        Removes display width from MySQL column type.
        """
        match = re.match(r"(\w+)(?:\(\d+\))?( unsigned)?", column_type.lower())
        if match:
            base_type = match.group(1)
            unsigned = match.group(2)
            return f"{base_type} unsigned" if unsigned else base_type
        return column_type.lower()

    def fetch_data(self) -> List[Dict[str, str]]:
        """
        Queries PostgreSQL database to retrieve bigint columns within threshold limits.
        """
        engine = self.db_info.get("engine")

        min_value = int(self.INTEGER_MAX_VALUE * self.MIN_THRESHOLD)
        max_value = int(self.INTEGER_MAX_VALUE * self.MAX_THRESHOLD)

        if engine == "mysql":
            query = f"""
                SELECT
                    c.TABLE_SCHEMA AS database_name,
                    c.TABLE_SCHEMA AS schema_name,
                    c.TABLE_NAME AS table_name,
                    c.COLUMN_NAME AS column_name,
                    c.COLUMN_TYPE AS data_type,
                    t.AUTO_INCREMENT AS current_value
                FROM information_schema.COLUMNS c
                JOIN information_schema.TABLES t
                    ON c.TABLE_SCHEMA = t.TABLE_SCHEMA
                    AND c.TABLE_NAME = t.TABLE_NAME
                WHERE c.TABLE_SCHEMA NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
                  AND c.DATA_TYPE = 'bigint'
                  AND c.COLUMN_KEY = 'PRI'
                  AND t.AUTO_INCREMENT BETWEEN {min_value} AND {max_value}
                  AND (
                      SELECT COUNT(*)
                      FROM information_schema.KEY_COLUMN_USAGE k
                      WHERE k.TABLE_SCHEMA = c.TABLE_SCHEMA
                        AND k.TABLE_NAME = c.TABLE_NAME
                        AND k.CONSTRAINT_NAME = 'PRIMARY'
                  ) = 1;
            """

        elif engine == "postgres":
            query = f"""
                SELECT
                    c.table_catalog AS database_name,
                    c.table_schema AS schema_name,
                    c.table_name AS table_name,
                    c.column_name AS column_name,
                    c.data_type AS data_type,
                    seq.last_value AS current_value
                FROM information_schema.columns c
                JOIN information_schema.key_column_usage kcu
                    ON c.table_schema = kcu.table_schema
                    AND c.table_name = kcu.table_name
                    AND c.column_name = kcu.column_name
                JOIN information_schema.table_constraints tc
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    AND tc.constraint_type = 'PRIMARY KEY'
                JOIN pg_catalog.pg_class pgc
                    ON pgc.relname = (c.table_name || '_' || c.column_name || '_seq')
                    AND pgc.relkind = 'S'
                JOIN pg_catalog.pg_namespace pgn
                    ON pgn.oid = pgc.relnamespace
                    AND pgn.nspname = c.table_schema
                JOIN pg_catalog.pg_sequences seq
                    ON seq.schemaname = pgn.nspname
                    AND seq.sequencename = pgc.relname
                WHERE c.data_type = 'bigint'
                    AND seq.last_value BETWEEN {min_value} AND {max_value}
                    AND c.table_schema NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
                    AND c.table_schema NOT LIKE 'pg_temp_%'
                    AND c.table_schema NOT LIKE 'pg_toast_temp_%';
            """

        else:
            self.logger.info(f"Unsupported database engine: {engine}")
            return []

        result = self._query(query)

        # Check if there was an error with the query
        if "error" in result:
            self.logger.error(f"Error fetching bigint overflow data: {result['error']}")
            raise Exception(f"Database query failed: {result['error']}")

        if not result or not result.get("rows"):
            return []

        columns = result.get("columns", [])
        rows = result.get("rows", [])
        processed_data = [dict(zip(columns, row)) for row in rows]

        data = []

        for row in processed_data:
            current_value = int(row.get("current_value", 0))
            usage_percent = round((current_value / self.INTEGER_MAX_VALUE) * 100, 2)
            remaining = self.INTEGER_MAX_VALUE - current_value

            data_type = row["data_type"]
            if engine == "mysql":
                data_type = self.normalize_mysql_type(data_type)

            data.append({
                "Database": row["database_name"],
                "Schema": row["schema_name"],
                "Table": row["table_name"],
                "Column": row["column_name"],
                "Data Type": data_type,
                "Current Value": current_value,
                "Integer Max": self.INTEGER_MAX_VALUE,
                "Times %": f"{usage_percent}%",
                "Above": remaining
            })


        return data
