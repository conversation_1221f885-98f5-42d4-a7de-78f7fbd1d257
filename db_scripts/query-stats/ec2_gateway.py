import itertools
import boto3
import botocore

client_config = botocore.config.Config(max_pool_connections=200)
ec2_client = boto3.client('ec2', config=client_config)


class EC2Metadata:
    def __init__(self):
        self.ip = ""
        self.launch_time = ""
        self.environment_name = ""
        self.team_name = ""

    @staticmethod
    def describe_ec2_instance_against(addresses):
        addresses = list(filter(lambda x: x != "", addresses))
        try:
            response = ec2_client.describe_instances(
                Filters=[
                    {
                        'Name': 'private-ip-address',
                        'Values': addresses
                    },
                ],
            )
        except botocore.exceptions.ClientError as e:
            response = ''   
        return response

    @staticmethod
    def divide_ip_addresses_in_chunks(ip_adresses, chunk_size):
        for value in range(0, len(ip_adresses), chunk_size):
            yield list(ip_adresses[value:value + chunk_size])

    @staticmethod
    def fetch_ec2_metadata_against(ec2_response):
        ec2_instances_metadata = []
        for reservation in ec2_response.get('Reservations'):
            for instance in reservation.get('Instances'):
                ec2_metadata = EC2Metadata()
                ec2_metadata.ip = instance.get('PrivateIpAddress')
                ec2_metadata.launch_time = instance.get('LaunchTime').strftime('%Y-%m-%d %H:%M:%S')
                for tag in instance.get('Tags'):
                    if tag.get('Key') == 'Name':
                        ec2_metadata.environment_name = tag.get('Value')
                    if tag.get('Key') == 'c-team':
                        ec2_metadata.team_name = tag.get('Value')
            ec2_instances_metadata.append(ec2_metadata)

        return ec2_instances_metadata

    def fetch_ec2_instances_against(self, private_ip_addresses):
        ec2_stats = []
        ip_addresses = list(self.divide_ip_addresses_in_chunks(private_ip_addresses, 200))
        for addresses in ip_addresses:
            ec2_response = self.describe_ec2_instance_against(addresses)
            ec2_stats.append(self.fetch_ec2_metadata_against(ec2_response))

        return list(itertools.chain(*ec2_stats))
