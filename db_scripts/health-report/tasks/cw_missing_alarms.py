from typing import List, Dict

import boto3

from ..lib import DatabaseTask, DatabaseConnector


class CWMissingAlarmsList(DatabaseTask):
    sheet_name = "Missing Alarms"

    SEVERE_ALARMS = {'FreeableMemory', 'FreeStorageSpace', 'CPUUtilization'}
    MEDIUM_ALARMS = {'DatabaseConnections', 'DiskQueueDepth', 'ReadIOPS', 'ReadLatency', 'WriteIOPS', 'WriteLatency'}
    REQUIRED_ALARMS = SEVERE_ALARMS.union(MEDIUM_ALARMS)

    ALARM_DESCRIPTIONS = {
        "FreeableMemory": "Ensures the database has enough memory to avoid performance degradation.",
        "FreeStorageSpace": "Prevents database crashes due to insufficient storage.",
        "CPUUtilization": "Monitors CPU usage to prevent overutilization and performance issues.",
        "DatabaseConnections": "Helps track the number of active connections to prevent connection exhaustion.",
        "DiskQueueDepth": "Indicates potential disk I/O bottlenecks affecting performance.",
        "ReadIOPS": "Monitors read operations per second to ensure disk performance stability.",
        "ReadLatency": "Helps detect slow read performance that may impact query response times.",
        "WriteIOPS": "Monitors write operations per second to prevent disk performance issues.",
        "WriteLatency": "Helps detect slow write operations that could impact data integrity.",
    }

    def __init__(self, profile: str, boto3_session: boto3.Session, db_connector: DatabaseConnector, configs: Dict):
        super().__init__(profile, boto3_session, db_connector, configs)
        self.cloudwatch = self.session.client('cloudwatch')

    @classmethod
    def columns(cls) -> List[str]:
        return ["MissingAlarm", "Notes"]

    def get_alarms(self, instance: str) -> List[Dict[str, str]]:
        """Fetch all CloudWatch alarms for a given instance, handling pagination."""
        self.logger.info(f"Fetching alarms for instance: {instance}")

        alarm_name_prefix = f"DBA_RDS_{instance}_METRIC_"
        all_alarms = []
        next_token = None

        while True:
            params = {
                "AlarmNamePrefix": alarm_name_prefix,
                "AlarmTypes": ["MetricAlarm"],
                "MaxRecords": 100
            }
            if next_token:
                params["NextToken"] = next_token

            response = self.cloudwatch.describe_alarms(**params)
            all_alarms.extend(response.get("MetricAlarms", []))
            next_token = response.get("NextToken")

            if not next_token:
                break  # Exit loop if there are no more pages

        return all_alarms

    def fetch_data(self) -> List[Dict[str, str]]:
        instance = self.db_info.get('instance_identifier')
        all_alarms = self.get_alarms(instance)
        existing_alarms = {alarm.get("MetricName", "") for alarm in all_alarms}
        missing_alarms = self.REQUIRED_ALARMS - existing_alarms

        missing_alarms_data = []
        for alarm in missing_alarms:
            notes = self.ALARM_DESCRIPTIONS.get(alarm, "No description available.")
            missing_alarms_data.append({
                # "InstanceIdentifier": instance,
                "MissingAlarm": alarm,
                "Notes": notes
            })
            self.logger.debug(f"Instance {instance} is missing alarm: {alarm}")

        return missing_alarms_data
