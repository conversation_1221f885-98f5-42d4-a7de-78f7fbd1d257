#!/bin/bash -e
# runs the integrity checks python script
#cd scripts

pip3 install -r requirements.txt

# Set profile based on env
#if [[ "$env" == "dev-rh" || "$env" == "staging-mot" ]]; then
 #   profile="qa"
#else
#    profile="prod"
#fi
# Check if the variable is null or empty
MY_VARIABLE=$4
if [ -z "$MY_VARIABLE" ]; then
  # Assign a random string to the variable if it's null
  MY_VARIABLE="hello"
fi



python3 script.py $1 \
        $2 \
        $3 \
        $MY_VARIABLE \
