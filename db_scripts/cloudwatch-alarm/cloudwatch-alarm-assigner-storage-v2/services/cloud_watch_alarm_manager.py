from gateway.rds_instances import RDSInstance
from services.alarms import Alarms


class CloudWatchAlarmManager:

    def __init__(self, _ec2Client, _rdsClient, _cloudwatch_client, _accountId):
        self.ec2_client = _ec2Client
        self.rds_client = _rdsClient
        self.account_id = _accountId
        self.cloudwatch_client = _cloudwatch_client

    def add_alarms_for_instance(self, instance_id):
        instance = RDSInstance().get_specific_rds_instance(instance_id, self.rds_client, self.ec2_client)
        if instance:
            Alarms().add_all_alarms_to_instance(instance, self.cloudwatch_client, self.account_id)
        else:
            print(f"No RDS instance found with the identifier: {instance_id}")
