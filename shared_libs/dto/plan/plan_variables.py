from typing import Optional

from pydantic import Field

from ..analysis import Analysis
from ..database import DatabaseInfo
from ..decision import ExecutionPlan
from ...models import ReactiveBaseModel


class PlanVariables(ReactiveBaseModel):
    plan: Optional[ExecutionPlan] = Field(default=None)
    analysis: Optional[Analysis] = Field(default=None)
    db_info: Optional[DatabaseInfo] = Field(default=None)

    class Config:
        arbitrary_types_allowed = True
