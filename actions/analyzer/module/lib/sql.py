import sqlglot
import sqlparse
from sql_metadata import QueryType


def sql_format(sql: str, engine: str, split: bool = False, **opts) -> list[str] | str:
    default_opts = {
        # 'write': engine,
        # 'read': engine,
        'pretty': True,
        'comments': False
    }
    default_opts.update(opts)

    try:
        queries = sqlglot.transpile(sql, **default_opts)
    except Exception:
        try:
            default_opts['read'] = engine
            default_opts['write'] = engine
            queries = sqlglot.transpile(sql, **default_opts)

        except Exception:
            queries = [sqlparse.format(stmt, reindent=True, keyword_case='upper', strip_comments=True) for stmt in
                       sqlparse.split(sql)]

    queries = [query for query in queries if query.strip()]
    if split: return queries

    return ";\n".join(queries)


def extract_queries(sql, engine: str):
    statements = sqlparse.split(sql)
    filtered_statements = []
    for statement in statements:
        statement = sql_format(statement, engine=engine)
        if statement:
            filtered_statements.append(statement)
    return filtered_statements


def cast_sqlglot_key_to_query_type(parsed_query) -> QueryType:
    """Convert sqlglot parsed query to QueryType"""
    if not parsed_query:
        return None

    key = parsed_query.key

    # Handle CREATE statements by checking the kind
    if key.upper() == "CREATE":
        try:
            if hasattr(parsed_query, 'kind') and parsed_query.kind:
                kind = parsed_query.kind.upper()
                if kind == "TABLE":
                    return QueryType.CREATE
                # For other CREATE types (INDEX, VIEW, etc.), return None to indicate unsupported
                return None
        except Exception:
            pass

    sqlglot_to_query_type_mapping = {
        "INSERT": QueryType.INSERT,
        "REPLACE": QueryType.REPLACE,
        "UPDATE": QueryType.UPDATE,
        "DELETE": QueryType.DELETE,
        "SELECT": QueryType.SELECT,
        "ALTER": QueryType.ALTER,
        "DROP": QueryType.DROP,
    }

    try:
        return sqlglot_to_query_type_mapping[key.upper()]
    except KeyError:
        raise ValueError(f"'{key}' is not a valid QueryType")
