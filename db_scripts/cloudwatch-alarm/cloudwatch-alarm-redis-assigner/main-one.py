import argparse
import logging

from configs import Configs
from gateway.clients import Client
from services.cloud_watch_alarm_manager import CloudWatchAlarmManager
from services.redis_fetcher import RedisFetcher

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


def main(args):
    logging.info(f"Alarms assignment to instance {args.redis_identifier} is started")
    account = Configs().get_account_by_profile(args.profile)

    client = Client()
    client.account_arn = int(account)
    client.role = "db_rightsizing_role_cross_account"
    ec2_client = client.get_session_client('ec2')
    cloudwatch_client = client.get_session_client('cloudwatch')
    elasticache_client = client.get_session_client('elasticache')

    redis_fetcher = RedisFetcher(elasticache_client, ec2_client)
    node = redis_fetcher.get_redis_node_by_name(args.redis_identifier)

    obj = CloudWatchAlarmManager(ec2_client, cloudwatch_client, client.account_arn, elasticache_client)
    obj.add_alarms_for_redis_cluster(node)

    logging.info("Alarms assignment to instances completed")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--redis-identifier", required=True)
    parser.add_argument("--profile", required=True)
    arguments = parser.parse_args()
    main(arguments)
