name: Upgrade to MySQL 8 - Execute
description: Upgrade to MySQL 8 - Execute

inputs:
  instance_name:
    description: This targets the RDS cluster to upgrade
    required: true
  env:
    description: Target AWS environment
    required: true
  role_arn:
    description: Arn of the role to assume
    required: true
  upgrade_charset:
    description: Decides whether to upgrade character sets or not
    required: true
  perform_switchover:
    description: Decides whether to perform a switchover or not
    required: true
  secret_path:
    description: This is for customizing the secret path, by default we look up for master_credentials
    required: true

runs:
  using: composite
  steps:
    - name: Run python script
      shell: bash
      working-directory: ${{ github.action_path }}
      id: execute
      run: ./run-python-script.sh
      env:
        env: ${{ inputs.env }}
        role_arn: ${{ inputs.role_arn }}
        secret_path: ${{ inputs.secret_path }}
        instance_name: ${{ inputs.instance_name }}
        upgrade_charset: ${{ inputs.upgrade_charset }}
        perform_switchover: ${{ inputs.perform_switchover }}
