import logging
import os

from .lib import *
from .shared.configs import Config


def main():
    Config.configure_logging()
    endpoint = os.environ.get('ENDPOINT')
    database = os.environ.get('DATABASE')
    schema = os.environ.get('SCHEMA')

    if not endpoint:
        raise ValueError("No DB endpoint provided")

    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Fetch the DB info
    fetcher = DBInfoFetcher(endpoint, database, schema)
    fetcher.process()
    logging.info("Process completed successfully")


if __name__ == "__main__":
    main()
