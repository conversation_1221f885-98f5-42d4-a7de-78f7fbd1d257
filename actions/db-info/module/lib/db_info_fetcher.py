import logging

import boto3

from . import util
from ..shared.lib import github
from ..shared.lib.rds import RDSOperations


class DBInfoFetcher:
    def __init__(self, endpoint, database=None, schema=None):
        self.endpoint = endpoint
        self.database = database
        self.schema = schema
        self.db_info = {}
        self.instance_name = None
        self.db_endpoint = None
        self.records = None
        self.rds_operations = RDSOperations()

    def fetch_hosted_zone_id(self):
        logging.info(f"Fetching hosted zone ID for endpoint: {self.db_endpoint}")
        route53_client = boto3.client('route53')
        response = route53_client.list_hosted_zones()
        for zone in response['HostedZones']:
            if self.db_endpoint.endswith(zone['Name'].rstrip('.')):
                return zone['Id']
        raise ValueError(f"No hosted zone found for endpoint: {self.db_endpoint}")

    def find_highest_weighted_record(self):
        logging.info("Finding the highest weighted record")
        if len(self.records) == 0:
            return None
        highest_weight_record = max(self.records, key=lambda x: x.get('Weight'))
        return highest_weight_record['ResourceRecords'][0]['Value']

    def fetch_secret(self):
        logging.info(f"Fetching secret for instance: {self.instance_name}")

        secret_name = "storage/governance/creds"

        try:
            secrets_manager = boto3.client('secretsmanager')
            response = secrets_manager.get_secret_value(SecretId=secret_name)

            if 'SecretString' in response:
                secrets = eval(response['SecretString'])  # Convert JSON string to dict
            else:
                raise ValueError("SecretString not found in the response")

            # Extract required values
            username = secrets.get("db_portal_user")
            password = secrets.get("db_portal_password")

            if username and password:
                return [username, password]

        except Exception as e:
            logging.error(f"Error fetching secrets: {e}")
            raise ValueError("No governance secret found! Expected keys: `db_portal_user` and `db_portal_password`")

    def fetch_db_info(self):
        logging.info(f"Fetching DB info for instance: {self.instance_name}")
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table('db-portal-rds-configs')

        try:
            response = table.scan()
            for item in response['Items']:
                if item['instance'] == self.instance_name:
                    return item
        except Exception as e:
            logging.error(f"Error fetching DynamoDB data: {e}")
            raise e

        raise ValueError(f"Instance {self.instance_name} not found in the DynamoDB table")

    def resolve_rds_endpoint(self):
        logging.info(f"Resolving RDS endpoint for: {self.endpoint}")
        if self.endpoint.endswith('.rds.amazonaws.com'):
            logging.info("Direct RDS endpoint provided, returning it directly")
            return self.endpoint

        route53_client = boto3.client('route53')
        self.db_endpoint = self.endpoint
        hosted_zone_id = self.fetch_hosted_zone_id()
        response = route53_client.list_resource_record_sets(
            HostedZoneId=hosted_zone_id,
            StartRecordName=self.endpoint,
            StartRecordType='CNAME'
        )
        self.records = [record for record in response['ResourceRecordSets'] if
                        record['Name'] == f"{self.endpoint}." and record['Type'] == 'CNAME']
        return self.find_highest_weighted_record()

    def extract_rds_instance_name(self, rds_host):
        logging.info(f"Extracting RDS instance name from endpoint: {rds_host}")
        rds_identifier = rds_host.split('.')[0]
        self.instance_name = rds_identifier

    def process_data_for_output(self):
        if self.db_info.get('engine') == 'postgres':
            self.db_info['schemas'] = util.postgres_convert_schemas_to_dict_string(self.db_info.get('schemas', []))

            if not self.schema:
                self.db_info['schema'] = 'public'

            if self.database:
                self.db_info['database'] = self.database

        elif self.db_info.get('engine') == 'mysql':
            self.db_info['schemas'] = util.mysql_convert_schemas_to_list_string(self.db_info.get('schemas', []))

            if self.database or self.schema:
                self.db_info['database'] = self.database or self.schema
                self.db_info['schema'] = self.database or self.schema

    def process(self):
        if not self.endpoint:
            raise ValueError("No DB endpoint provided")

        logging.info(f"Starting process for DB endpoint: {self.endpoint}")

        db_endpoint_value = self.resolve_rds_endpoint()

        if not db_endpoint_value:
            raise ValueError(f"No RDS endpoint found for {self.endpoint}")

        self.extract_rds_instance_name(db_endpoint_value)

        secret = self.fetch_secret()
        if not secret:
            raise ValueError(f"No secret found for instance: {self.instance_name}")

        username, password = secret
        self.db_info = self.fetch_db_info()
        self.db_info['rds_host'] = db_endpoint_value
        self.db_info['schema'] = self.schema
        self.db_info['username'] = username
        self.db_info['password'] = password

        self.process_data_for_output()

        for key, value in self.db_info.items():
            github.add_output(key, value)
