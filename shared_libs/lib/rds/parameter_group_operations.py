import logging

import boto3

from ...exceptions import *
from ...lib.aws.helper import aws_command_handler


class ParameterGroupOperations:
    def __init__(self):
        self.rds_client = boto3.client('rds')

    def get_parameter_group(self, name):
        def describe_parameter_group():
            return self.rds_client.describe_db_parameter_groups(DBParameterGroupName=name)

        try:
            return aws_command_handler(describe_parameter_group)
        except AwsDBParameterGroupNotFound as e:
            logging.warning(f"Parameter group {name} not found.")
            return None
