import json
import logging
import re

from dms import <PERSON>MS
from dms_enum import DMSAlarmType
from configs import Config
from notifications_manager import NotificationManager
from slack_teams import Teams

def parse_alarm_name(alarm_name):
    pattern = r"^(DBA_[A-Z]+)_(.*?)_METRIC_(.*)$"
    match = re.match(pattern, alarm_name)
    if not match:
        raise ValueError("Invalid alarm name format")

    prefix = match.group(1)
    identifier = match.group(2)
    metric_name = match.group(3)

    return prefix, identifier, metric_name

def lambda_handler(event, _context):
    Config().set_logging_config()
    logging.info(event)

    message = json.loads(json.loads(event.get('Records', [{}])[0].get('body', '{}'))['Message'])
    aws_region = event.get("Records", [{}])[0].get("awsRegion", "unknown")

    alarm_name = message.get('AlarmName')

    if not alarm_name:
        logging.error('Event type is not supported!')
        exit(1)

    prefix, identifier, metric = parse_alarm_name(alarm_name)
    
    if prefix == 'DBA_RDS':
        name_space = 'RDS'
        if 'green' in identifier:
            logging.info('Green RDS instance detected, skipping the notification..')
            exit(0)
    elif prefix == 'DBA_REDIS':
        name_space = 'Redis'
    elif prefix == 'DBA_DMS':
        name_space = 'DMS'
    else:
        raise ValueError("Unsupported alarm received")

    account_id = int(message['AWSAccountId'])
    Config().load_configs(account_id, name_space, identifier, aws_region)
    
    if name_space == "DMS":
        metrics = message['Trigger'].get("Metrics", [])
        # revist
        if len(metrics) > 1 and isinstance(metrics[1].get('MetricStat', {}).get('Metric', {}).get('Dimensions', []), list) and len(metrics[1]['MetricStat']['Metric']['Dimensions']) == 2:
            Config.dms_alarm_type = DMSAlarmType.TASK
            resource_external_id = metrics[1]['MetricStat']['Metric']['Dimensions'][-1]['value']
            Config.dms_resource_id = DMS().get_task_id(resource_external_id)
        
        elif len(metrics) > 1 and isinstance(metrics[1].get('MetricStat', {}).get('Metric', {}).get('Dimensions', []), list) and len(metrics[1]['MetricStat']['Metric']['Dimensions']) == 1:
            Config.dms_alarm_type = DMSAlarmType.REPLICATION_INSTANCE
            resource_external_id = metrics[1]['MetricStat']['Metric']['Dimensions'][-1]['value']
            Config.dms_resource_id = DMS().get_task_id(resource_external_id)
            
        elif len(message['Trigger']['Dimensions']) == 1:
            Config.dms_alarm_type = DMSAlarmType.REPLICATION_INSTANCE
            resource_external_id = message['Trigger']['Dimensions'][-1]['value']
            Config.dms_resource_id = DMS().get_replication_instance_id(resource_external_id)

        else:
            Config.dms_alarm_type = DMSAlarmType.TASK
            resource_external_id = message['Trigger']['Dimensions'][-1]['value']
            Config.dms_resource_id = DMS().get_task_id(resource_external_id)

    response, escalation_handle = Teams().load_teams(message)
    notification_manager = NotificationManager(message, identifier)
    notification_manager.execute_alerts(response, escalation_handle)

    # elif name_space == 'Redis' and alarm == True:
    #     sns = event['Records'][0]
    #     account_id = int(json.loads(sns['body'])['TopicArn'].split(':')[4])
    #     Config().load_configs(account_id,name_space)
    #     response, escalation_handle = Teams().load_teams(json_msg)
    #     notification_manager = NotificationManager(json_msg)
    #     notification_manager.execute_alerts(response, escalation_handle)

    # Test Comment

    # =======================================================
    # commenting the method below as the storage bot doesn't 
    # have group read permissions to automatically add users
    # to channel
    # permissions needed: 'usergroups:read'
    # =======================================================

    # notification_manager.add_team_to_slack(team_handle)


if __name__ == "__main__":
    notification_manager = NotificationManager(None)
    notification_manager.add_team_to_slack("S02RQH3DKCZ")

