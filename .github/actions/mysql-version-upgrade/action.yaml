name: mysql-version-upgrade - Execute
description: mysql-version-upgrade - Execute

inputs:
  account_identifier:
    description: Which is the source account on which we want to perform the upgrade
    required: true
  rds_identifier:
    description: Name of the rds which we want to upgrade
    required: true
  version_upgrade_to:
    description: Engine version which we want to upgrade
    required: true

runs:
  using: composite
  steps:
    - name: Run python script
      shell: bash
      working-directory: ${{ github.action_path }}
      id: execute
      run: ./run-python-script.sh ${{ inputs.account_identifier }}  ${{ inputs.rds_identifier }} ${{ inputs.version_upgrade_to }}
      env:
        account_identifier: ${{ inputs.account_identifier }}
        rds_identifier: ${{ inputs.rds_identifier }}
        version_upgrade_to: ${{ inputs.version_upgrade_to }}
