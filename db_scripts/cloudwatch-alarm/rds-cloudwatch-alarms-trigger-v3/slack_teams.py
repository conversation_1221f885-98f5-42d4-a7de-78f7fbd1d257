import json
import logging
import os
import tempfile
from functools import lru_cache

import yaml
from slack import WebClient

from client import Client
from configs import Config
from env_variables import ROLE
from tags import Tags
from dms import DMS


class Teams:

    def load_teams(self, json_msg):
        logging.info("Fetching Slack Handle")

        resource = None
        owner = None
        service = self.get_service_profile(json_msg)

        if service is None:
            resource = self.get_cloud_resource_profile(json_msg)

        if service and resource is None:
            owner = self.get_owner(json_msg)

        if service and resource and owner is None:
            logging.info("c-service or c-team is not valid")
            slack_group = Config.default_slack_group
            vo_hook = Config.default_vo_route
            group_id = self.get_group_id(slack_group)
            return self.no_team_tag_alert(group_id), vo_hook

        owner = owner or (resource or {}).get('owner') or (service or {}).get('owner')

        slack_group = self.get_slack_team_handle(owner)
        if slack_group is None:
            slack_group = Config.default_slack_group
        vo_hook = self.get_vo_route(owner)
        if vo_hook is None:
            vo_hook = Config.default_vo_route

        # Invite the group members to the channel
        self.invite_to_channel(slack_group)

        logging.info("Slack handle found successfully")

        if json_msg['NewStateValue'] == 'OK':
            return self.slack_resolve_message(slack_group), vo_hook
        else:
            return self.slack_alert_message(slack_group), vo_hook

    def get_owner(self, json_msg):
        logging.info("Getting owner from service directory")
        directory = self.read_service_directory()['teams']
        team_tag = self.get_tag(json_msg, 'c-team')
        return next((team for team in directory if team['team'] == team_tag), None)

    def get_service_profile(self, json_msg):
        logging.info("Getting service profile")
        service_tag = self.get_tag(json_msg, 'c-service')
        directory = self.read_service_directory()
        service_elem = next((service for service in directory['services'] if service['name'] == service_tag), None)
        return service_elem

    def get_cloud_resource_profile(self, json_msg):
        logging.info("Getting cloud resource profile")
        service_tag = self.get_tag(json_msg, 'c-service')
        directory = self.read_service_directory()
        resource = next((resource for resource in directory['cloud-resources'] if resource['name'] == service_tag),
                        None)
        return resource

    def get_tag(self, json_msg, search_tag):
        logging.info(f"Getting tag: {search_tag}")
        if Config.name_space == 'RDS':
            all_tags = Tags().get_instance_tags(json_msg['Trigger']['Dimensions'][0]['value'])
            service = next((tag["Value"] for tag in all_tags if tag["Key"] == search_tag), None)
        elif Config.name_space == 'DMS':
            metrics = json_msg['Trigger'].get("Metrics", [])
            if len(metrics) > 1 and isinstance(metrics[1].get('MetricStat', {}).get('Metric', {}).get('Dimensions', []), list) and len(metrics[1]['MetricStat']['Metric']['Dimensions']) > 0:
                resource_id = metrics[1]['MetricStat']['Metric']['Dimensions'][-1]['value']
            else:
                resource_id = json_msg['Trigger']['Dimensions'][-1]['value']
            all_tags = DMS().get_dms_tags(resource_id)
            service = next((tag["Value"] for tag in all_tags if tag["Key"] == search_tag), None)
        elif Config.name_space == 'Redis':
            if Config.identifier:
                all_tags = Tags().get_redis_tags(Config.identifier)
            else:
                all_tags = []
            service = next((tag["Value"] for tag in all_tags if tag["Key"] == search_tag), None)
        else:
            service = None
        return service

    @lru_cache(maxsize=None)
    def read_service_directory(self):
        logging.info("Reading service directory")
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, Config.service_directory.file.replace('/', '_'))

        if os.path.exists(file_path):
            with open(file_path, 'r') as file:
                content = file.read()
            print(f"Reusing cached file: {file_path}")
        else:
            client = Client()
            client.account_id = Config.sd_account_id
            client.role = ROLE
            s3 = client.get_session_client('s3')
            response = s3.get_object(
                Bucket=Config.service_directory.bucket,
                Key=Config.service_directory.file
            )
            content = response['Body'].read().decode('utf-8')
            with open(file_path, 'w') as file:
                file.write(content)
            print(f"Downloaded and cached file: {file_path}")

        return json.loads(content)

    def get_slack_team_handle(self, owner):
        return owner['oncall-slack-group']

    def get_vo_route(self, owner):
        return owner['victorops-route']

    def get_general_handle(self, file_path):
        with open(file_path) as file:
            all_teams = yaml.load(file, Loader=yaml.FullLoader)
            global_tag = all_teams["general"]
            return global_tag["slack_handle"], global_tag["escalation_handle"]

    def no_team_tag_alert(self, general_handle):
        return f'*{Config.slack_app_name}:* c-team is invalid <!subteam^{general_handle}>'

    def slack_alert_message(self, team_handle):
        group_id = self.get_group_id(team_handle)
        return f'*{Config.slack_app_name}:* <!subteam^{group_id}>'

    def slack_resolve_message(self, team_handle):
        group_id = self.get_group_id(team_handle)
        return f'*{Config.slack_app_name}:* <!subteam^{group_id}> (Resolved by System)'

    def invite_to_channel(self, slack_group):
        logging.info(f"Inviting to channel for slack group: {slack_group}")

        token = Config.slack_token
        slack_client = WebClient(token)
        group_id = self.get_group_id(slack_group)
        response = slack_client.usergroups_users_list(usergroup=group_id)
        channel_id = self.get_channel_id(Config.slack.channel)

        if channel_id is None:
            logging.warning(f"Channel {Config.slack.channel} not found. No users will be invited.")
            return

        # Check if the bot user is a member of the channel
        bot_user_id = slack_client.auth_test()['user_id']
        logging.info(f"Bot user ID: {bot_user_id}")
        logging.info(f"Channel ID: {channel_id}")

        channel_members = slack_client.conversations_members(channel=channel_id)['members']
        logging.info(f"Channel members: {channel_members}")

        if bot_user_id not in channel_members:
            logging.info(f"Bot user {bot_user_id} is not a member of the channel. Joining the channel.")
            # Add the bot user to the channel
            slack_client.conversations_join(channel=channel_id)
        else:
            logging.info(f"Bot user {bot_user_id} is already a member of the channel.")

        try:
            logging.info(f"Inviting users to the channel: {response['users']}")
            slack_client.conversations_invite(
                channel=channel_id,
                users=response['users']
            )
        except Exception as e:
            if e.response['error'] == 'already_in_channel':
                logging.info("Users are already in the channel")
            else:
                logging.error(f"Error inviting users to the channel: {e}")
                raise e

    @lru_cache(maxsize=None)
    def get_usergroups_list(self):
        token = Config.slack_token
        slack_client = WebClient(token)
        # Fetch all user groups
        usergroups_response = slack_client.usergroups_list()
        return usergroups_response['usergroups']

    @lru_cache(maxsize=None)
    def get_all_channels_list(self):
        token = Config.slack_token
        slack_client = WebClient(token)
        channels = []
        cursor = None

        while True:
            response = slack_client.conversations_list(
                exclude_archived=True,
                limit=1000,
                types="public_channel",
                cursor=cursor
            )
            channels.extend(response['channels'])
            cursor = response.get('response_metadata', {}).get('next_cursor')
            if not cursor:
                break

        return channels

    def get_channel_id(self, channel_name):
        channels = self.get_all_channels_list()
        return next((channel['id'] for channel in channels if channel['name'] == channel_name), None)

    def get_group_id(self, slack_group):
        usergroups = self.get_usergroups_list()
        return next((group['id'] for group in usergroups if group['handle'] == slack_group), None)
