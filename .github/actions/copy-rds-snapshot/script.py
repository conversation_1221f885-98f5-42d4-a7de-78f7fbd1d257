import boto3
import time
from clients import  Client
import threading
import sys

import sys

if len(sys.argv) != 4:
    print("Usage: python3 file.py arg1 arg2 arg3")
    sys.exit(1)


source_account_name = sys.argv[1]
snapshot_name = sys.argv[2]
target_account_name = sys.argv[3]
rds_client = boto3.client('rds')


def snapshot_exists(snapshot_id):
    try:
        rds_client.describe_db_snapshots(DBSnapshotIdentifier=snapshot_id)
        return True
    except rds_client.exceptions.DBSnapshotNotFoundFault:
        return False

# Check if snapshot exists and delete if it does
if snapshot_exists(snapshot_name):
    # Delete snapshot
    rds_client.delete_db_snapshot(DBSnapshotIdentifier=snapshot_name)
    print(f"Deleting existing snapshot {snapshot_name}")

    # Wait for snapshot to be deleted
    while snapshot_exists(snapshot_name):
        print(f"Snapshot {snapshot_name} is still being deleted...")
        time.sleep(10)

print(f"Snapshot {snapshot_name} is now deleted")

snapshot_arn=""

accounts=['************','************','************','************']
account=''


kms_key_id=''


if target_account_name == 'dev-rh':
    kms_key_id = '69320a41-0b2f-4bd8-8069-f67144c98318'
    account = '************'


if target_account_name == 'prod-rh':
    kms_key_id = '1b7fb28e-ca50-4652-ade1-63d3b9e4825b'
    account = '************'

if target_account_name == 'staging-mot':
    kms_key_id = 'aa89c862-7d54-4a3c-8566-6193167b0ce1'
    account = '************'

if target_account_name == 'prod-mot':
    kms_key_id = 'mrk-267ac400300d4fdab0660bf754276129'
    account = '************'


response = rds_client.describe_db_snapshots(
    SnapshotType='shared',
    IncludeShared=True
)

for value in (response['DBSnapshots']):
    for account in accounts:
        snapshot_arn_check = ( f'arn:aws:rds:eu-west-1:{account}:snapshot:{snapshot_name}')
        if snapshot_arn_check==value['DBSnapshotArn']:
            snapshot_arn=snapshot_arn_check
            break




# Copy the snapshot
response = rds_client.copy_db_snapshot(
    SourceDBSnapshotIdentifier=snapshot_arn,
    TargetDBSnapshotIdentifier=snapshot_name,
    KmsKeyId=kms_key_id
)