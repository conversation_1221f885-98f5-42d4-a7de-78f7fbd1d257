import pymysql
from datetime import datetime, timedelta
import logging
from sql import SQL as sql
import time
from pprint import pprint
from environment_variables import PARAMS


class UpdateLocations:

    def __init__(self, read_url, write_url, db_username, db_password, schema, db_port, start_date_time, end_date_time, table_name, batch_size):
        self.read_connection = self.get_connection(db_url=read_url, db_username=db_username, db_password=db_password, schema=schema, db_port=db_port)
        self.write_connection = self.get_connection(db_url=write_url, db_username=db_username, db_password=db_password, schema=schema, db_port=db_port)
        self.start_date_time = start_date_time
        self.end_date_time = end_date_time
        self.table_name = table_name
        self.batch_size = batch_size

    def get_connection(self, db_url, db_username, db_password, schema, db_port):
        conn = pymysql.connect(host=db_url, user=db_username, password=db_password,
                                database=schema, port=db_port, connect_timeout=5)
        print("Connection Created")
        return conn

    def execute_read_query(self, connection, query):
        with connection.cursor() as cursor:
            cursor.execute(query)
            bl_values = cursor.fetchall()
        return bl_values

    def execute_update_query(self, connection, query):
        with connection.cursor() as cursor:
            cursor.execute(query)

    def get_updated_records(self, connection, table_name, start_date_time, end_date_time):
        query = sql.get_updated_records(table_name=table_name, start_date_time=start_date_time, end_date_time=end_date_time)
        print(query)
        return self.execute_read_query(connection=connection, query=query)
    
    def update_last_updated_column(self, connection, table_name, values):
        for value in values:
            id = value[0]
            last_updated = value[1] + timedelta(seconds=1)
            update_query = sql.update_last_updated_column(table_name=table_name, id=id, last_updated=last_updated)
            print(f"Updating Query: {update_query}")
            self.execute_update_query(connection=connection, query=update_query)
            connection.commit()
            time.sleep(0.005)

    def update_batches(self):
        start_date_time = self.start_date_time
        end_date_time = self.end_date_time
        temp_end_date_time = start_date_time + timedelta(days=0.025)
        while True:
            updated_records = self.get_updated_records(connection=self.read_connection, table_name=self.table_name, start_date_time=start_date_time, end_date_time=temp_end_date_time)
            self.update_last_updated_column(connection=self.write_connection, table_name=self.table_name, values=updated_records)
            start_date_time = temp_end_date_time
            temp_end_date_time = temp_end_date_time + timedelta(days=0.025)
            if temp_end_date_time >= end_date_time:
                break
        start_date_time = temp_end_date_time
        updated_records = self.get_updated_records(connection=self.read_connection, table_name=self.table_name, start_date_time=start_date_time, end_date_time=end_date_time)
        self.update_last_updated_column(connection=self.write_connection, table_name=self.table_name, values=updated_records)