import logging
import re

import boto3

from ..dto import AlarmDTO


class Alarm:
    def __init__(self, dto: AlarmDTO):
        self.dto = dto

    @classmethod
    def all(cls, prefix=None, state=None):
        client = boto3.client('cloudwatch')
        paginator = client.get_paginator('describe_alarms')
        alarms = []

        pagination_config = {
            'PaginationConfig': {
                'StartingToken': None,
                'PageSize': 100
            }
        }

        if prefix:
            paginator_args = {'AlarmNamePrefix': prefix, 'PaginationConfig': pagination_config}
        else:
            paginator_args = {'PaginationConfig': pagination_config}

        if state:
            paginator_args['StateValue'] = state

        for page in paginator.paginate(**paginator_args):
            for alarm in page['MetricAlarms']:
                alarms.append(alarm)

        return alarms

    @classmethod
    def by_name(cls, name):
        client = boto3.client('cloudwatch')
        response = client.describe_alarms(AlarmNames=[name])
        return Alarm(AlarmDTO(raw=response['MetricAlarms'][0]))

    @classmethod
    def by_dict(cls, d):
        return Alarm(AlarmDTO(raw=d))

    @classmethod
    def by_dto(cls, dto: AlarmDTO):
        return Alarm(dto)

    @classmethod
    def find_by_prefix(cls, prefix=None, state=None):
        return [cls.by_dict(alarm) for alarm in cls.all(prefix=prefix, state=state)]

    @classmethod
    def filter_by_pattern(cls, alarms, pattern):
        return [alarm for alarm in alarms if re.match(pattern, alarm.dto.name)]

    def _get_new_object_data(self):
        unwanted_attributes = [
            "AlarmArn", "AlarmConfigurationUpdatedTimestamp", "StateValue",
            "StateReason", "StateReasonData", "StateUpdatedTimestamp",
            "StateTransitionedTimestamp"
        ]
        clean_dto = {k: v for k, v in self.dto.raw.items() if k not in unwanted_attributes}
        return clean_dto

    def refresh(self):
        client = boto3.client('cloudwatch')
        response = client.describe_alarms(AlarmNames=[self.dto.name])
        self.dto = AlarmDTO(raw=response['MetricAlarms'][0])
        logging.info(f"DTO for alarm {self.dto.name} refreshed successfully")

    def update(self, **kwargs):
        client = boto3.client('cloudwatch')
        new_alarm = self._get_new_object_data()
        new_alarm.update(kwargs)
        logging.info(f"Updating alarm {self.dto.name} with new values: {kwargs}")
        client.put_metric_alarm(**new_alarm)
        self.refresh()
        logging.info(f"Alarm {self.dto.name} updated successfully")

    def __str__(self):
        return self.dto.name
