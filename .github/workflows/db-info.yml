name: DB Info test

on:
  workflow_dispatch:
    inputs:
      env:
        type: choice
        description: AWS environment
        required: true
        options:
          - dev-rh
          - staging-mot
          - prod-rh
          - prod-mot
      db_endpoint:
        description: Database endpoint
        required: true

jobs:
  db-info:
    name: Fetch DB Info
    runs-on: ${{ fromJSON(vars.DEVX_GITHUB_RUNNERS_CONFIG)[inputs.env].runner }}
    env:
      AWS_REGION: eu-west-1
      AWS_DEFAULT_REGION: eu-west-1
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ fromJSON(vars.DEVX_PROFILES_ROLES)[inputs.env] }}
          role-duration-seconds: 3600
          aws-region: ${{ env.AWS_REGION }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'

      - name: DB Info
        uses: careem/storage-shared-workflows/actions/db-info@master
        with:
          endpoint: ${{ github.event.inputs.db_endpoint }}
