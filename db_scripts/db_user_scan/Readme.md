# Database Permissions <PERSON>an <PERSON>t

## Purpose

The purpose of this script is to scan current databases for a specific account to check for any permissions to alter tables.

## Validity

This feature is valid only for MySQL and PostgreSQL.

- **MySQL**: It scans the tables for any `ALTER` grant.
- **PostgreSQL**: It loops over databases and lists the tables which are owned by ordinary users and not the master one.

## Usage

To run the script, use the following command:

```sh
python3 script.py --account {AWS account alias} --database {postgres/mysql}
```
- **--account** (mandatory): This parameter is used to construct the master credentials secret name as storage/{args.account}/rds/master_credentials. Example: `cfstg` , `qa` 

- **--database** (optional): Use this parameter if you want to limit the output to a specific engine type (either `postgres` or `mysql`).
- **Note:** : For postgres we need to connect to an initial DB in order to get the list of all DBs then loop over them and check access. The script tries to connect to the DB configured at level of AWS upon provisioning the RDS, if this field is empty it connects to `postgres` db to get the DBs list. So if no DB is listed on AWS level, a default `postgres` DB should exist.