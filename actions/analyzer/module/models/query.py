import json
import logging
from functools import cached_property

import sqlglot
from sql_metadata import Parser, QueryType

from ..lib import jinja
from ..lib import sql
from ..models.query_table import QueryTable
from ..models.table import Table
from ..shared.dto.database.database_info import DatabaseInfo
from ..shared.lib.serialization_tools import *


class Query:
    template_context = {
        'explainable_query_types': [
            QueryType.UPDATE,
            QueryType.DELETE,
            QueryType.INSERT,
            QueryType.SELECT
        ]
    }

    @property
    def _template_context(self):
        """Dynamic template context with query-specific properties"""
        context = {**self.template_context}
        # Add new properties to the context object
        context.update({
            'query_type': self.query_type,
            'query_category': self.query_category,
            'kind': self.kind,
            'is_explainable': self.is_explainable,
            'is_create_table': self.is_create_table(),
            'is_alter_table': self.is_alter_table(),
        })
        return context

    def __init__(self, db_info: DatabaseInfo, connector, query=None):
        self.db_info = db_info
        self.connector = connector
        self.query = query

    def __str__(self):
        return str(self.query)

    @cached_property
    def tables(self) -> list[QueryTable]:
        if self.is_create_table():
            return []

        parser = Parser(self.query)

        list_of_tables = []

        # First option: use sqlglot to parse the query
        try:
            for table in sqlglot.parse_one(self.query, read=self.db_info.engine).find_all(sqlglot.exp.Table):
                list_of_tables.append(table.name)

            if len(list_of_tables) == 0:
                for table in sqlglot.parse_one(self.query).find_all(sqlglot.exp.Table):
                    list_of_tables.append(table.name)
        except Exception:
            pass

        # Second option: use sql_metadata Parser to parse the query
        if len(list_of_tables) == 0:
            try:
                list_of_tables = [table.split('.')[-1] for table in parser.tables]
            except Exception:
                pass

        # Extract only actual tables for MySQL/MariaDB to avoid sqlglot issues
        if self.db_info.engine == 'mysql' or self.db_info.engine == 'mariadb':
            original_tables = list(list_of_tables)  # Make a copy for logging
            list_of_tables = self._extract_actual_tables(list_of_tables)

            # Log if any tables were corrected
            if set(original_tables) != set(list_of_tables):
                corrected = set(list_of_tables) - set(original_tables)
                removed = set(original_tables) - set(list_of_tables)
                if removed:
                    logging.warning(f"Detected and fixed sqlglot issue: Removed non-table identifiers: {removed}")
                if corrected:
                    logging.info(f"Added actual tables from query analysis: {corrected}")
                logging.debug(f"Original tables: {original_tables}")
                logging.debug(f"Actual tables: {list_of_tables}")

        table_names = set(list_of_tables)
        tables = []

        for table_name in table_names:
            table = Table(table_name, self.db_info, self.connector)
            query_table = QueryTable(table, parser)
            tables.append(query_table)

        logging.debug(f'Parsed tables: {table_names}')
        return tables

    @cached_property
    def _jinja(self):
        return jinja.env('query_templates/query')

    @cached_property
    def _parser(self):
        return Parser(self.query)

    @cached_property
    def _parser_sqlglot(self):
        return sqlglot.parse_one(self.query)

    @cached_property
    def analysis(self):
        templates = self._jinja.list_templates()
        results = dict(map(self._process_template, templates))
        return results

    def _process_template(self, template_name):
        key = jinja.to_key(template_name)
        template = self._jinja.get_template(template_name)
        logging.debug(f'Processing template: {template_name}')
        rendered_query = template.render(
            context=self._template_context,
            engine=self.db_info.engine,
            db_name=self.db_info.database,
            schema=self.db_info.schema,
            query=self.query
        )
        rendered_query = sql.sql_format(rendered_query, split=True, engine=self.db_info.engine)
        logging.debug(f'Rendered query: {rendered_query}')

        if not rendered_query:
            return key, False

        serializer_method = getattr(self, f'serialize_template_{key}', self.default_serializer)
        logging.debug(f'Executing the query..')
        result = self.connector.dry_execute(rendered_query)
        result = serializer_method(result)
        return key, result

    @cached_property
    def query_type(self):
        try:
            return sql.cast_sqlglot_key_to_query_type(self._parser_sqlglot)
        except Exception as e:
            logging.warning(f'sqlglot parser failed to determine query type: {e}')
            try:
                return self._parser.query_type
            except Exception as e:
                logging.error(f'Parser also failed to determine query type: {e}')
                return None

    @cached_property
    def kind(self):
        """Returns the specific kind of CREATE statement, or None for non-CREATE statements"""
        try:
            if hasattr(self._parser_sqlglot, 'kind') and self._parser_sqlglot.kind:
                return self._parser_sqlglot.kind.upper()
        except Exception as e:
            logging.debug(f'Failed to determine statement kind: {e}')
        return None

    @cached_property
    def query_category(self):
        """Returns detailed category for reporting/analytics"""
        if self.kind:
            return f"CREATE_{self.kind}"  # CREATE_TABLE, CREATE_INDEX, etc.
        return self.query_type  # SELECT, INSERT, UPDATE, etc.

    @cached_property
    def is_explainable(self):
        """More accurate check for explainable queries"""
        explainable_types = {QueryType.SELECT, QueryType.INSERT, QueryType.UPDATE, QueryType.DELETE}
        return self.query_type in explainable_types

    def is_create_table(self):
        return self.kind == "TABLE"

    def is_alter_table(self):
        return self.query_type == QueryType.ALTER

    # ====================== Templates Serializers ======================

    @staticmethod
    def default_serializer(result):
        if len(result) == 1:
            return result[0]
        else:
            raise Exception('Expected only one result')

    def serialize_template_query_plan_text(self, result):
        if self.db_info.engine == 'postgres':
            return extract_pg_query_plan(result)
        elif self.db_info.engine == 'mysql' or self.db_info.engine == 'mariadb':
            return table_format_explain_query(result)
        return None

    def serialize_template_query_plan_json(self, result):
        if self.db_info.engine == 'postgres':
            return result[0]['QUERY PLAN']
        elif self.db_info.engine == 'mysql' or self.db_info.engine == 'mariadb':
            return json.loads(result[0]['EXPLAIN'])
        return None

    def serialize_template_query_analyze_text(self, result):
        return self.serialize_template_query_plan_text(result)

    def serialize_template_query_analyze_json(self, result):
        return self.serialize_template_query_plan_json(result)

    def _extract_actual_tables(self, candidate_tables):
        """
        Extract only actual table names from candidate list by analyzing the SQL AST.

        This method directly identifies real tables rather than filtering out suspected non-tables.
        It's more reliable than the filter-out approach as it positively identifies tables.

        Args:
            candidate_tables: List of potential table names from sqlglot/sql_metadata

        Returns:
            List of actual table names found in the query
        """
        try:
            parsed = sqlglot.parse_one(self.query, read=self.db_info.engine)
            actual_tables = set()

            # Strategy 1: Extract tables from different SQL statement types
            if parsed.key.upper() == "SELECT":
                # For SELECT statements, look for tables in FROM and JOIN clauses
                for table_expr in parsed.find_all(sqlglot.exp.Table):
                    if table_expr.name:
                        actual_tables.add(table_expr.name)

            elif parsed.key.upper() == "INSERT":
                # For INSERT statements, the target table is in the 'this' attribute
                if hasattr(parsed, 'this') and parsed.this:
                    actual_tables.add(parsed.this.name)
                # Also check for tables in subqueries
                for table_expr in parsed.find_all(sqlglot.exp.Table):
                    if table_expr.name:
                        actual_tables.add(table_expr.name)

            elif parsed.key.upper() == "UPDATE":
                # For UPDATE statements, look for the main table and any JOINed tables
                for table_expr in parsed.find_all(sqlglot.exp.Table):
                    if table_expr.name:
                        actual_tables.add(table_expr.name)

            elif parsed.key.upper() == "DELETE":
                # For DELETE statements, look for tables in FROM clause
                for table_expr in parsed.find_all(sqlglot.exp.Table):
                    if table_expr.name:
                        actual_tables.add(table_expr.name)

            elif parsed.key.upper() == "ALTER":
                # For ALTER statements, the table being altered is in the 'this' attribute
                if hasattr(parsed, 'this') and parsed.this:
                    actual_tables.add(parsed.this.name)

            elif parsed.key.upper() == "COMMAND":
                # sqlglot sometimes falls back to 'command' for complex ALTER statements
                # Use regex to extract the table name from ALTER TABLE statements
                import re
                alter_table_pattern = re.compile(r'ALTER\s+TABLE\s+`?(\w+)`?', re.IGNORECASE)
                matches = alter_table_pattern.findall(self.query)
                if matches:
                    actual_tables.add(matches[0])
                    # For COMMAND type, don't trust sqlglot's table extraction
                    # Return immediately with just the ALTER TABLE target
                    return [matches[0]] if matches[0] in candidate_tables else [matches[0]]

            elif parsed.key.upper() == "DROP":
                # For DROP statements, check what's being dropped
                if hasattr(parsed, 'kind') and parsed.kind:
                    if parsed.kind.upper() == "TABLE" and hasattr(parsed, 'this') and parsed.this:
                        actual_tables.add(parsed.this.name)

            # Strategy 2: If we found actual tables, return only those that are in our candidate list
            if actual_tables:
                verified_tables = [table for table in candidate_tables if table in actual_tables]
                if verified_tables:
                    logging.debug(f"Verified tables from AST analysis: {verified_tables}")
                    return verified_tables
                else:
                    # If none of the candidates match, return the AST-found tables
                    logging.debug(f"No candidates matched AST tables, returning AST tables: {list(actual_tables)}")
                    return list(actual_tables)

        except Exception as e:
            logging.debug(f"Error in AST-based table extraction: {e}")

        # Fallback: Use regex patterns to identify actual tables
        try:
            import re
            actual_tables = set()

            # Pattern for different SQL statement types
            patterns = {
                'select_from': re.compile(r'FROM\s+`?(\w+)`?', re.IGNORECASE),
                'select_join': re.compile(r'JOIN\s+`?(\w+)`?', re.IGNORECASE),
                'insert_into': re.compile(r'INSERT\s+INTO\s+`?(\w+)`?', re.IGNORECASE),
                'update_table': re.compile(r'UPDATE\s+`?(\w+)`?', re.IGNORECASE),
                'delete_from': re.compile(r'DELETE\s+FROM\s+`?(\w+)`?', re.IGNORECASE),
                'alter_table': re.compile(r'ALTER\s+TABLE\s+`?(\w+)`?', re.IGNORECASE),
                'drop_table': re.compile(r'DROP\s+TABLE\s+`?(\w+)`?', re.IGNORECASE),
            }

            # Special handling for ALTER TABLE statements to avoid column confusion
            if 'ALTER TABLE' in self.query.upper():
                alter_matches = patterns['alter_table'].findall(self.query)
                if alter_matches:
                    # For ALTER TABLE, only return the table being altered
                    table_name = alter_matches[0]
                    logging.debug(f"Found ALTER TABLE statement, returning only target table: {table_name}")
                    return [table_name]

            for pattern_name, pattern in patterns.items():
                matches = pattern.findall(self.query)
                actual_tables.update(matches)

            if actual_tables:
                # Return only candidate tables that match our regex findings
                verified_tables = [table for table in candidate_tables if table in actual_tables]
                if verified_tables:
                    logging.debug(f"Verified tables from regex analysis: {verified_tables}")
                    return verified_tables
                else:
                    # If no candidates match, return the regex-found tables
                    logging.debug(f"No candidates matched regex tables, returning regex tables: {list(actual_tables)}")
                    return list(actual_tables)

        except Exception as e:
            logging.debug(f"Error in regex-based table extraction: {e}")

        # Final fallback: return original candidate list
        logging.debug("Falling back to original candidate tables")
        return candidate_tables

    def _filter_out_index_names(self, table_names):
        """
        DEPRECATED: Use _extract_actual_tables instead.

        Filter out index names incorrectly identified as tables in MySQL DROP INDEX statements.

        In MySQL, when using DROP INDEX in an ALTER TABLE statement, sqlglot incorrectly identifies
        the index name as a table. This method filters out those false positives by using sqlglot's
        AST introspection to identify actual tables vs index names.

        Example:
            ALTER TABLE billing_entry DROP INDEX idx_my_index_name, ALGORITHM=INPLACE, LOCK=NONE;

            sqlglot incorrectly identifies both 'billing_entry' and 'idx_my_index_name' as tables.
        """
        try:
            # Parse the query using sqlglot
            parsed = sqlglot.parse_one(self.query, read='mysql')

            # Collect index names that might be incorrectly identified as tables
            index_names = set()

            # Find all Drop expressions that might be DROP INDEX
            for drop_expr in parsed.find_all(sqlglot.exp.Drop):
                # Check if this is a DROP INDEX expression
                if hasattr(drop_expr, 'kind') and drop_expr.kind and drop_expr.kind.lower() == 'index':
                    # The index name is in the 'this' attribute
                    if hasattr(drop_expr, 'this') and drop_expr.this:
                        index_names.add(drop_expr.this.name)

            # Find all Alter expressions that might contain DROP INDEX
            for alter_expr in parsed.find_all(sqlglot.exp.Alter):
                # Check if this Alter has any expressions
                if hasattr(alter_expr, 'expressions'):
                    for expr in alter_expr.expressions:
                        # Check if this is a DROP INDEX expression
                        if isinstance(expr, sqlglot.exp.Drop) and hasattr(expr, 'kind') and expr.kind and expr.kind.lower() == 'index':
                            if hasattr(expr, 'this') and expr.this:
                                index_names.add(expr.this.name)

            # If we found any index names, filter them out from the table names
            if index_names:
                logging.warning(f"Found index names that sqlglot incorrectly identified as tables: {index_names}")
                logging.debug(f"Query that caused the issue: {self.query}")
                filtered_tables = [name for name in table_names if name not in index_names]
                # If filtering would remove all tables, keep the original list as a fallback
                if filtered_tables:
                    logging.debug(f"After filtering, tables remaining: {filtered_tables}")
                    return filtered_tables
                else:
                    logging.debug("Filtering would remove all tables, keeping original list as fallback")

            # Also identify actual tables from Alter statements as a fallback
            actual_tables = set()
            for alter_expr in parsed.find_all(sqlglot.exp.Alter):
                # For ALTER TABLE statements, the table name is in the 'this' attribute
                if hasattr(alter_expr, 'this') and alter_expr.this:
                    actual_tables.add(alter_expr.this.name)

            if actual_tables:
                logging.debug(f"Found actual tables from ALTER statements: {actual_tables}")
                filtered_tables = [name for name in table_names if name in actual_tables]
                logging.debug(f"After filtering by actual tables, tables remaining: {filtered_tables}")
                return filtered_tables

        except Exception as e:
            logging.warning(f"Error filtering index names using AST: {e}")
            # Fallback to regex-based approach if AST parsing fails
            try:
                import re
                # Look for patterns like "DROP INDEX idx_name" or "ALTER TABLE table_name DROP INDEX idx_name"
                drop_index_pattern = re.compile(r'DROP\s+INDEX\s+([\w_]+)', re.IGNORECASE)
                alter_table_pattern = re.compile(r'ALTER\s+TABLE\s+([\w_]+)', re.IGNORECASE)

                # Find all index names that might be incorrectly identified as tables
                index_matches = drop_index_pattern.findall(self.query)
                index_names = set(index_matches)

                # Find all actual table names
                table_matches = alter_table_pattern.findall(self.query)
                actual_tables = set(table_matches)

                # If we found both index names and actual tables, filter out the index names
                if index_names and actual_tables:
                    return [name for name in table_names if name not in index_names or name in actual_tables]
                # If we only found index names, filter them out
                elif index_names:
                    return [name for name in table_names if name not in index_names]
                # If we only found actual tables, keep only those
                elif actual_tables:
                    return [name for name in table_names if name in actual_tables]
            except Exception as e:
                logging.warning(f"Error filtering index names using regex: {e}")

        return table_names
