import datetime
from dataclasses import dataclass
from functools import cached_property
from typing import List, Dict

from .switchover_details_dto import SwitchoverDetailDTO
from .task_dto import TaskDTO


@dataclass
class BlueGreenDeploymentDTO:
    deployment_info: Dict

    @cached_property
    def id(self) -> str:
        return self.deployment_info['BlueGreenDeploymentIdentifier']

    @cached_property
    def name(self) -> str:
        return self.deployment_info['BlueGreenDeploymentName']

    @cached_property
    def create_time(self) -> datetime:
        return self.deployment_info['CreateTime']

    @cached_property
    def source(self) -> str:
        return self.deployment_info['Source']

    @cached_property
    def status(self) -> str:
        return self.deployment_info['Status']

    @cached_property
    def switchover_details(self) -> List[SwitchoverDetailDTO]:
        return [SwitchoverDetailDTO(**detail) for detail in self.deployment_info['SwitchoverDetails']]

    @cached_property
    def tag_list(self) -> List[Dict]:
        return self.deployment_info['TagList']

    @cached_property
    def target(self) -> str:
        return self.deployment_info['Target']

    @cached_property
    def tasks(self) -> List[TaskDTO]:
        return [TaskDTO(**task) for task in self.deployment_info['Tasks']]
