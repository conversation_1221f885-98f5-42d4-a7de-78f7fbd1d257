name: rds_certificate_upgrade
description: rds_certificate_upgrade

inputs:
  env:
    description: Target AWS environment
    required: true
  rds_instance:
    description: Target RDS Instance
    required: true
  ca_certificate_name:
    description: New Certificate To Upgrade To
    required: true


runs:
  using: composite
  steps:
    - name: Update CA Certificate
      shell: bash
      working-directory: ${{ github.action_path }}
      id: execute
      run: |
        echo "Upgrading ${{ inputs.rds_instance }} in environment ${{ inputs.env }} with Certificate ${{ inputs.ca_certificate_name }}"
        chmod +x rds-certificate-upgrade.sh
        ./rds-certificate-upgrade.sh ${{ inputs.env }} ${{ inputs.rds_instance }} ${{ inputs.ca_certificate_name }}
      env:
        env: ${{ inputs.env }}
        rds_instance: ${{ inputs.rds_instance }}
        ca_certificate_name: ${{ inputs.ca_certificate_name }}