name: Restore RDS instance from snapshot - Execute
description: Restore RDS instance from snapshot - Execute

inputs:
  aws_account:
    description: Which is the account on which we want to restore
    required: true
  snapshot_name:
    description: Name of the snapshot from which we want to restore
    required: true

runs:
  using: composite
  steps:
    - name: Run python script
      shell: bash
      working-directory: ${{ github.action_path }}
      id: execute
      run: ./run-python-script.sh ${{ inputs.aws_account }} ${{ inputs.snapshot_name }}
      env:
        aws_account: ${{ inputs.aws_account }}
        snapshot_name: ${{ inputs.snapshot_name }}
